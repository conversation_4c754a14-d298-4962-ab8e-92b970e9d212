<?php

/**
 * View Reservation Page
 * Display reservation details with action buttons
 */

// Get reservation ID from URL
$reservationId = $_GET['id'] ?? '';

if (!$reservationId) {
    Application::redirect('/store-admin/?page=reservations', 'Reservation not found', 'error');
    exit;
}

// Fetch reservation data
try {
    $reservation = $db->fetchRow("
        SELECT r.*, 
               c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
               s.name as service_name, s.price as service_price, s.duration as service_duration,
               e.name as employee_name, e.position as employee_position,
               cat.name as category_name, cat.color as category_color
        FROM reservations r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN employees e ON r.employee_id = e.id
        LEFT JOIN categories cat ON s.category_id = cat.id
        WHERE r.id = :id
    ", [':id' => $reservationId]);

    if (!$reservation) {
        Application::redirect('/store-admin/?page=reservations', 'Reservation not found', 'error');
        exit;
    }
} catch (Exception $e) {
    Application::redirect('/store-admin/?page=reservations', 'Error loading reservation data', 'error');
    exit;
}

// Calculate end time
$startTime = new DateTime($reservation['date'] . ' ' . $reservation['start_time']);
$endTime = clone $startTime;
$endTime->add(new DateInterval('PT' . ($reservation['service_duration'] ?? 60) . 'M'));

// Status colors
$statusColors = [
    'pending' => '#f59e0b',
    'confirmed' => '#10b981',
    'completed' => '#06b6d4',
    'cancelled' => '#ef4444',
    'no_show' => '#6b7280'
];
?>

<div class="view-page">
    <!-- Header -->
    <div class="view-header">
        <div class="view-header-content">
            <div class="view-breadcrumb">
                <a href="/store-admin/?page=reservations" class="breadcrumb-link">
                    <i class="fas fa-calendar"></i> Reservations
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current">Reservation #<?= htmlspecialchars($reservationId) ?></span>
            </div>

            <div class="view-title">
                <h1>Reservation #<?= htmlspecialchars($reservationId) ?></h1>
                <div class="view-subtitle">
                    <span class="status-badge" style="background-color: <?= $statusColors[$reservation['status']] ?? '#6b7280' ?>">
                        <?= ucfirst($reservation['status']) ?>
                    </span>
                    <span class="date-info">
                        <?= date('l, M j, Y', strtotime($reservation['date'])) ?> at <?= date('H:i', strtotime($reservation['start_time'])) ?>
                    </span>
                </div>
            </div>
        </div>

        <div class="view-actions">
            <div class="dropdown">
                <button class="btn btn-outline dropdown-toggle" type="button">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu">
                    <a href="/store-admin/?page=edit-reservation&id=<?= htmlspecialchars($reservationId) ?>" class="dropdown-item">
                        <i class="fas fa-edit"></i> Edit Reservation
                    </a>

                    <?php if ($reservation['status'] === 'pending'): ?>
                        <button onclick="updateReservationStatus('<?= htmlspecialchars($reservationId) ?>', 'confirmed')" class="dropdown-item">
                            <i class="fas fa-check"></i> Confirm
                        </button>
                    <?php endif; ?>

                    <?php if (in_array($reservation['status'], ['pending', 'confirmed'])): ?>
                        <button onclick="updateReservationStatus('<?= htmlspecialchars($reservationId) ?>', 'completed')" class="dropdown-item">
                            <i class="fas fa-check-circle"></i> Mark Complete
                        </button>
                        <button onclick="updateReservationStatus('<?= htmlspecialchars($reservationId) ?>', 'cancelled')" class="dropdown-item">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                    <?php endif; ?>

                    <div class="dropdown-divider"></div>
                    <button onclick="sendReminder('<?= htmlspecialchars($reservationId) ?>')" class="dropdown-item">
                        <i class="fas fa-bell"></i> Send Reminder
                    </button>
                    <button onclick="rescheduleReservation('<?= htmlspecialchars($reservationId) ?>')" class="dropdown-item">
                        <i class="fas fa-calendar-alt"></i> Reschedule
                    </button>
                    <a href="/store-admin/?page=add-reservation&customer_id=<?= htmlspecialchars($reservation['customer_id']) ?>" class="dropdown-item">
                        <i class="fas fa-plus"></i> Book Another
                    </a>
                    <button onclick="duplicateReservation('<?= htmlspecialchars($reservationId) ?>')" class="dropdown-item">
                        <i class="fas fa-copy"></i> Duplicate
                    </button>

                    <div class="dropdown-divider"></div>
                    <button onclick="deleteReservation('<?= htmlspecialchars($reservationId) ?>')" class="dropdown-item text-danger">
                        <i class="fas fa-trash"></i> Delete Reservation
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="view-content">
        <div class="view-grid">
            <!-- Reservation Details -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-calendar-alt"></i> Appointment Details</h2>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Date</label>
                            <span><?= date('l, M j, Y', strtotime($reservation['date'])) ?></span>
                        </div>

                        <div class="info-item">
                            <label>Time</label>
                            <span>
                                <?= $startTime->format('H:i') ?> - <?= $endTime->format('H:i') ?>
                                <small class="text-muted">(<?= $reservation['service_duration'] ?? 60 ?> minutes)</small>
                            </span>
                        </div>

                        <div class="info-item">
                            <label>Status</label>
                            <span class="status-badge" style="background-color: <?= $statusColors[$reservation['status']] ?? '#6b7280' ?>">
                                <?= ucfirst($reservation['status']) ?>
                            </span>
                        </div>

                        <div class="info-item">
                            <label>Service</label>
                            <span>
                                <strong><?= htmlspecialchars($reservation['service_name'] ?? 'N/A') ?></strong>
                                <?php if ($reservation['category_name']): ?>
                                    <br><span class="category-badge" style="background-color: <?= htmlspecialchars($reservation['category_color']) ?>">
                                        <?= htmlspecialchars($reservation['category_name']) ?>
                                    </span>
                                <?php endif; ?>
                            </span>
                        </div>

                        <div class="info-item">
                            <label>Price</label>
                            <span class="price">€<?= number_format($reservation['service_price'] ?? 0, 2) ?></span>
                        </div>

                        <div class="info-item">
                            <label>Created</label>
                            <span><?= date('M j, Y H:i', strtotime($reservation['created_at'])) ?></span>
                        </div>

                        <?php if (!empty($reservation['notes'])): ?>
                            <div class="info-item full-width">
                                <label>Notes</label>
                                <span><?= nl2br(htmlspecialchars($reservation['notes'])) ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-user"></i> Customer Information</h2>
                    <a href="/store-admin/?page=view-customer&id=<?= htmlspecialchars($reservation['customer_id']) ?>" class="btn btn-outline btn-sm">
                        View Profile
                    </a>
                </div>
                <div class="section-content">
                    <div class="info-grid">
                        <div class="info-item">
                            <label>Name</label>
                            <span><?= htmlspecialchars($reservation['customer_name'] ?? 'N/A') ?></span>
                        </div>

                        <?php if (!empty($reservation['customer_email'])): ?>
                            <div class="info-item">
                                <label>Email</label>
                                <span>
                                    <a href="mailto:<?= htmlspecialchars($reservation['customer_email']) ?>" class="contact-link">
                                        <?= htmlspecialchars($reservation['customer_email']) ?>
                                    </a>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($reservation['customer_phone'])): ?>
                            <div class="info-item">
                                <label>Phone</label>
                                <span>
                                    <a href="tel:<?= htmlspecialchars($reservation['customer_phone']) ?>" class="contact-link">
                                        <?= htmlspecialchars($reservation['customer_phone']) ?>
                                    </a>
                                </span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Employee Information -->
            <?php if ($reservation['employee_id']): ?>
                <div class="view-section">
                    <div class="section-header">
                        <h2><i class="fas fa-user-tie"></i> Assigned Employee</h2>
                        <a href="/store-admin/?page=view-employee&id=<?= htmlspecialchars($reservation['employee_id']) ?>" class="btn btn-outline btn-sm">
                            View Profile
                        </a>
                    </div>
                    <div class="section-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <label>Name</label>
                                <span><?= htmlspecialchars($reservation['employee_name'] ?? 'N/A') ?></span>
                            </div>

                            <div class="info-item">
                                <label>Position</label>
                                <span class="position-badge"><?= htmlspecialchars($reservation['employee_position'] ?? 'Staff Member') ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>


    </div>
</div>

<script>
    function updateReservationStatus(id, status) {
        const statusNames = {
            'confirmed': 'confirm',
            'completed': 'mark as completed',
            'cancelled': 'cancel'
        };

        if (confirm(`Are you sure you want to ${statusNames[status] || 'update'} this reservation?`)) {
            const formData = new FormData();
            formData.append('action', 'update_reservation_status');
            formData.append('id', id);
            formData.append('status', status);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Reservation status updated successfully', 'success');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showNotification('Error updating reservation: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }

    function deleteReservation(id) {
        if (confirm('Are you sure you want to delete this reservation? This action cannot be undone.')) {
            const formData = new FormData();
            formData.append('action', 'delete_reservation');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Reservation deleted successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=reservations';
                        }, 1000);
                    } else {
                        showNotification('Error deleting reservation: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }

    function sendReminder(id) {
        showNotification('Reminder feature temporarily disabled during modal removal', 'warning');
    }

    function rescheduleReservation(id) {
        showNotification('Reschedule feature temporarily disabled during modal removal', 'warning');
    }

    function duplicateReservation(id) {
        if (confirm('Are you sure you want to duplicate this reservation?')) {
            const formData = new FormData();
            formData.append('action', 'duplicate_reservation');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Reservation duplicated successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=edit-reservation&id=' + data.new_id;
                        }, 1000);
                    } else {
                        showNotification('Error duplicating reservation: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }
</script>