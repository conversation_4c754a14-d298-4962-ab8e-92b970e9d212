<?php

/**
 * Multilingual Error Messages
 * Provides user-friendly error messages in multiple languages
 */

class ErrorMessages 
{
    private static array $messages = [
        'en' => [
            'time_reserved_by_another' => 'Sorry, this time slot has been reserved by another customer. Please select a different time.',
            'no_available_staff' => 'No staff available for this time slot. Please choose a different time.',
            'outside_business_hours' => 'Selected time is outside business hours. Please choose a time during our operating hours.',
            'business_closed' => 'We are closed on this day. Please select a different date.',
            'past_datetime' => 'Cannot book appointments in the past. Please select a future date and time.',
            'invalid_date' => 'Invalid date format. Please select a valid date.',
            'invalid_time' => 'Invalid time format. Please select a valid time.',
            'service_not_found' => 'Selected service is not available. Please choose a different service.',
            'system_error' => 'A system error occurred. Please try again or contact us for assistance.',
            'database_unavailable' => 'Our booking system is temporarily unavailable. Please try again in a few moments.',
            'booking_failed' => 'Failed to create your booking. Please try again.',
            'verification_failed' => 'Email verification failed. Please check your verification code.',
            'employee_conflict' => 'Your preferred staff member is not available. We have assigned another qualified staff member.',
            'time_conflict' => 'This time slot is no longer available.',
            'please_select_different_time' => 'Please select a different time slot.',
            'alternative_times_available' => 'Alternative time slots are available.',
            'booking_in_progress' => 'Another booking is in progress. Please wait a moment and try again.'
        ],
        'el' => [
            'time_reserved_by_another' => 'Λυπούμαστε, αυτή η ώρα έχει κρατηθεί από άλλον πελάτη. Παρακαλώ επιλέξτε διαφορετική ώρα.',
            'no_available_staff' => 'Δεν υπάρχει διαθέσιμο προσωπικό για αυτή την ώρα. Παρακαλώ επιλέξτε διαφορετική ώρα.',
            'outside_business_hours' => 'Η επιλεγμένη ώρα είναι εκτός ωραρίου λειτουργίας. Παρακαλώ επιλέξτε ώρα εντός του ωραρίου μας.',
            'business_closed' => 'Είμαστε κλειστοί αυτή την ημέρα. Παρακαλώ επιλέξτε διαφορετική ημερομηνία.',
            'past_datetime' => 'Δεν μπορείτε να κλείσετε ραντεβού στο παρελθόν. Παρακαλώ επιλέξτε μελλοντική ημερομηνία και ώρα.',
            'invalid_date' => 'Μη έγκυρη μορφή ημερομηνίας. Παρακαλώ επιλέξτε έγκυρη ημερομηνία.',
            'invalid_time' => 'Μη έγκυρη μορφή ώρας. Παρακαλώ επιλέξτε έγκυρη ώρα.',
            'service_not_found' => 'Η επιλεγμένη υπηρεσία δεν είναι διαθέσιμη. Παρακαλώ επιλέξτε διαφορετική υπηρεσία.',
            'system_error' => 'Παρουσιάστηκε σφάλμα συστήματος. Παρακαλώ δοκιμάστε ξανά ή επικοινωνήστε μαζί μας.',
            'database_unavailable' => 'Το σύστημα κρατήσεων είναι προσωρινά μη διαθέσιμο. Παρακαλώ δοκιμάστε ξανά σε λίγα λεπτά.',
            'booking_failed' => 'Αποτυχία δημιουργίας κράτησης. Παρακαλώ δοκιμάστε ξανά.',
            'verification_failed' => 'Η επαλήθευση email απέτυχε. Παρακαλώ ελέγξτε τον κωδικό επαλήθευσης.',
            'employee_conflict' => 'Ο προτιμώμενος εργαζόμενος δεν είναι διαθέσιμος. Έχουμε αναθέσει άλλον εξειδικευμένο εργαζόμενο.',
            'time_conflict' => 'Αυτή η ώρα δεν είναι πλέον διαθέσιμη.',
            'please_select_different_time' => 'Παρακαλώ επιλέξτε διαφορετική ώρα.',
            'alternative_times_available' => 'Υπάρχουν διαθέσιμες εναλλακτικές ώρες.',
            'booking_in_progress' => 'Μια άλλη κράτηση είναι σε εξέλιξη. Παρακαλώ περιμένετε λίγο και δοκιμάστε ξανά.'
        ]
    ];
    
    /**
     * Get error message in specified language
     */
    public static function get(string $key, string $language = 'en'): string
    {
        // Fallback to English if language not supported
        if (!isset(self::$messages[$language])) {
            $language = 'en';
        }
        
        // Return message if exists, otherwise return the key
        return self::$messages[$language][$key] ?? $key;
    }
    
    /**
     * Get error message based on conflict type
     */
    public static function getConflictMessage(array $conflict, string $language = 'en'): string
    {
        $type = $conflict['type'] ?? 'system_error';
        
        switch ($type) {
            case 'time_conflict':
            case 'employee_conflict':
                return self::get('time_reserved_by_another', $language);
                
            case 'outside_working_hours':
                return self::get('outside_business_hours', $language);
                
            case 'non_working_day':
                return self::get('business_closed', $language);
                
            case 'past_datetime':
                return self::get('past_datetime', $language);
                
            case 'invalid_date':
                return self::get('invalid_date', $language);
                
            case 'invalid_time':
                return self::get('invalid_time', $language);
                
            case 'service_not_found':
                return self::get('service_not_found', $language);
                
            default:
                return self::get('system_error', $language);
        }
    }
    
    /**
     * Detect user language from various sources
     */
    public static function detectLanguage(): string
    {
        // 1. Check if language is set in session
        if (isset($_SESSION['language'])) {
            return $_SESSION['language'];
        }
        
        // 2. Check if language is passed as parameter
        if (isset($_GET['lang']) && in_array($_GET['lang'], ['en', 'el'])) {
            return $_GET['lang'];
        }
        
        // 3. Check Accept-Language header
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $acceptLanguage = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
            if (strpos($acceptLanguage, 'el') !== false) {
                return 'el';
            }
        }
        
        // 4. Default to English
        return 'en';
    }
    
    /**
     * Format error response with proper language
     */
    public static function formatErrorResponse(array $conflicts, string $language = null): string
    {
        if ($language === null) {
            $language = self::detectLanguage();
        }
        
        if (empty($conflicts)) {
            return self::get('system_error', $language);
        }
        
        // For time conflicts, use the user-friendly message
        foreach ($conflicts as $conflict) {
            if (in_array($conflict['type'], ['time_conflict', 'employee_conflict'])) {
                return self::get('time_reserved_by_another', $language);
            }
        }
        
        // For other conflicts, return the first one
        return self::getConflictMessage($conflicts[0], $language);
    }
}
