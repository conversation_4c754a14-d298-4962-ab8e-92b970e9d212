<?php
require_once __DIR__ . '/../../shared/tenant_manager.php';

try {
    TenantManager::init();
    $db = TenantManager::getDatabase();
    
    // Common UI texts that are missing
    $missingTexts = [
        // Month names
        ['january', 'January', 'Ιανουάριος', 'ui'],
        ['february', 'February', 'Φεβρουάριος', 'ui'],
        ['march', 'March', 'Μάρτιος', 'ui'],
        ['april', 'April', 'Απρίλιος', 'ui'],
        ['may', 'May', 'Μάιος', 'ui'],
        ['june', 'June', 'Ιούνιος', 'ui'],
        ['july', 'July', 'Ιούλιος', 'ui'],
        ['august', 'August', 'Αύγουστος', 'ui'],
        ['september', 'September', 'Σεπτέμβριος', 'ui'],
        ['october', 'October', 'Οκτώβριος', 'ui'],
        ['november', 'November', 'Νοέμβριος', 'ui'],
        ['december', 'December', 'Δεκέμβριος', 'ui'],
        
        // Day names
        ['monday', 'Monday', 'Δευτέρα', 'ui'],
        ['tuesday', 'Tuesday', 'Τρίτη', 'ui'],
        ['wednesday', 'Wednesday', 'Τετάρτη', 'ui'],
        ['thursday', 'Thursday', 'Πέμπτη', 'ui'],
        ['friday', 'Friday', 'Παρασκευή', 'ui'],
        ['saturday', 'Saturday', 'Σάββατο', 'ui'],
        ['sunday', 'Sunday', 'Κυριακή', 'ui'],
        
        // Short day names
        ['mon', 'Mon', 'Δευ', 'ui'],
        ['tue', 'Tue', 'Τρι', 'ui'],
        ['wed', 'Wed', 'Τετ', 'ui'],
        ['thu', 'Thu', 'Πεμ', 'ui'],
        ['fri', 'Fri', 'Παρ', 'ui'],
        ['sat', 'Sat', 'Σαβ', 'ui'],
        ['sun', 'Sun', 'Κυρ', 'ui'],
        
        // Common booking texts
        ['booked', 'Booked', 'Κλεισμένο', 'booking'],
        ['unavailable', 'Unavailable', 'Μη διαθέσιμο', 'booking'],
        ['staff', 'Staff', 'Προσωπικό', 'booking'],
        ['auto_assign', 'Auto Assign', 'Αυτόματη Ανάθεση', 'booking'],
        ['auto_assign_tooltip', 'Let us assign the best available staff member', 'Αφήστε μας να αναθέσουμε το καλύτερο διαθέσιμο μέλος προσωπικού', 'booking'],
        
        // Error messages
        ['invalid_step', 'Invalid step number', 'Μη έγκυρος αριθμός βήματος', 'client'],
        ['no_categories', 'No categories available', 'Δεν υπάρχουν διαθέσιμες κατηγορίες', 'client'],
        ['failed_fetch_categories', 'Failed to fetch categories', 'Αποτυχία φόρτωσης κατηγοριών', 'client'],
        ['failed_fetch_services', 'Failed to fetch services', 'Αποτυχία φόρτωσης υπηρεσιών', 'client'],
        ['service_not_found', 'Service data not found', 'Δεν βρέθηκαν δεδομένα υπηρεσίας', 'client'],
        ['category_not_found', 'Category data not found', 'Δεν βρέθηκαν δεδομένα κατηγορίας', 'client'],
        
        // Validation messages
        ['name_required', 'Name is required', 'Το όνομα είναι υποχρεωτικό', 'client'],
        ['name_min_length', 'Name must be at least 2 characters', 'Το όνομα πρέπει να έχει τουλάχιστον 2 χαρακτήρες', 'client'],
        ['email_required', 'Email is required', 'Το email είναι υποχρεωτικό', 'client'],
        ['invalid_email', 'Please enter a valid email', 'Παρακαλώ εισάγετε έγκυρο email', 'client'],
        ['phone_required', 'Phone is required', 'Το τηλέφωνο είναι υποχρεωτικό', 'client'],
        ['invalid_phone', 'Please enter a valid Greek phone number', 'Παρακαλώ εισάγετε έγκυρο ελληνικό τηλέφωνο', 'client'],
        
        // Time slot statuses
        ['duration_conflict', 'Duration conflict', 'Σύγκρουση διάρκειας', 'booking'],
        ['insufficient_time', 'Insufficient time', 'Ανεπαρκής χρόνος', 'booking'],
        ['employee_unavailable', 'Staff unavailable', 'Προσωπικό μη διαθέσιμο', 'booking'],
        ['past_time', 'Past time', 'Παρελθούσα ώρα', 'booking'],
        
        // Success messages
        ['verification_resent', 'Verification code resent successfully', 'Ο κωδικός επαλήθευσης στάλθηκε επιτυχώς', 'client'],
        ['booking_created', 'Booking created successfully', 'Η κράτηση δημιουργήθηκε επιτυχώς', 'client'],
        
        // General UI
        ['loading_text', 'Loading...', 'Φόρτωση...', 'ui'],
        ['please_wait', 'Please wait', 'Παρακαλώ περιμένετε', 'ui'],
        ['error_occurred', 'An error occurred', 'Παρουσιάστηκε σφάλμα', 'ui'],
        ['try_again', 'Try again', 'Δοκιμάστε ξανά', 'ui'],
    ];
    
    $added = 0;
    
    foreach ($missingTexts as [$key, $enValue, $elValue, $category]) {
        // Check if key already exists
        $exists = $db->fetchColumn("SELECT COUNT(*) FROM texts WHERE text_key = :key", [':key' => $key]);
        
        if (!$exists) {
            // Add English version
            $db->query("INSERT INTO texts (text_key, text_value, language, category) VALUES (:key, :value, 'en', :category)", [
                ':key' => $key,
                ':value' => $enValue,
                ':category' => $category
            ]);

            // Add Greek version
            $db->query("INSERT INTO texts (text_key, text_value, language, category) VALUES (:key, :value, 'el', :category)", [
                ':key' => $key,
                ':value' => $elValue,
                ':category' => $category
            ]);

            $added++;
            echo "Added: $key\n";
        }
    }
    
    echo "\nAdded $added new translation keys to database\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
