<?php
/**
 * Multitenant Translation System
 * Clean, efficient multilingual support for multitenant architecture
 */

class Translation
{
    private static ?string $currentLanguage = null;
    private static array $cache = [];
    private static bool $cacheLoaded = false;
    private static array $discoveredTexts = [];
    
    /**
     * Initialize translation system
     */
    public static function init(): void
    {
        self::$currentLanguage = self::detectLanguage();
        self::loadCache();
    }
    
    /**
     * Main translation function
     */
    public static function t(string $key, string $defaultValue = '', string $category = 'general'): string
    {
        $translation = self::get($key, $category);
        $language = self::$currentLanguage ?? 'el';

        // Return translation for current language
        if (!empty($translation['value_' . $language])) {
            return $translation['value_' . $language];
        }

        // Fallback to Greek if not current language
        if ($language !== 'el' && !empty($translation['value_el'])) {
            return $translation['value_el'];
        }

        // Final fallback to default
        return $defaultValue ?: $key;
    }
    
    /**
     * Get translation data for both languages
     */
    public static function get(string $key, string $category = 'general'): array
    {
        $cacheKey = $category . '.' . $key;

        if (isset(self::$cache[$cacheKey])) {
            return self::$cache[$cacheKey];
        }

        try {
            $db = TenantManager::getDatabase();
            $result = $db->fetchRow("
                SELECT key, value_el, value_en, category
                FROM translations
                WHERE key = :key AND category = :category
            ", [':key' => $key, ':category' => $category]);

            if ($result) {
                self::$cache[$cacheKey] = $result;
                return $result;
            }
        } catch (Exception $e) {
            error_log('Translation error: ' . $e->getMessage());
        }

        return ['key' => $key, 'value_el' => '', 'value_en' => '', 'category' => $category];
    }

    /**
     * Save translation
     */
    public static function save(string $key, string $valueEl, string $valueEn, string $category = 'general'): bool
    {
        try {
            $db = TenantManager::getDatabase();

            $existing = $db->fetchRow("
                SELECT id FROM translations
                WHERE key = :key AND category = :category
            ", [':key' => $key, ':category' => $category]);

            if ($existing) {
                $result = $db->query("
                    UPDATE translations
                    SET value_el = :value_el, value_en = :value_en, updated_at = :updated_at
                    WHERE key = :key AND category = :category
                ", [
                    ':value_el' => $valueEl,
                    ':value_en' => $valueEn,
                    ':key' => $key,
                    ':category' => $category,
                    ':updated_at' => date('Y-m-d H:i:s')
                ]);
            } else {
                $result = $db->query("
                    INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                    VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
                ", [
                    ':id' => 'TR' . strtoupper(substr(uniqid(), -8)),
                    ':key' => $key,
                    ':value_el' => $valueEl,
                    ':value_en' => $valueEn,
                    ':category' => $category,
                    ':created_at' => date('Y-m-d H:i:s'),
                    ':updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            self::clearCache();
            return $result !== false;

        } catch (Exception $e) {
            error_log('Translation save error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all translations for category
     */
    public static function getAll(string $category = null): array
    {
        try {
            $db = TenantManager::getDatabase();

            if ($category) {
                return $db->fetchAll("
                    SELECT * FROM translations
                    WHERE category = :category
                    ORDER BY key
                ", [':category' => $category]);
            } else {
                return $db->fetchAll("
                    SELECT * FROM translations
                    ORDER BY category, key
                ");
            }
        } catch (Exception $e) {
            error_log('Translation getAll error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get only client-facing translations (editable in admin)
     */
    public static function getClientTranslations(): array
    {
        try {
            $db = TenantManager::getDatabase();
            // Include all client-facing categories that appear on client interface
            $clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];

            $placeholders = str_repeat('?,', count($clientCategories) - 1) . '?';

            return $db->fetchAll("
                SELECT * FROM translations
                WHERE category IN ({$placeholders})
                ORDER BY category, key
            ", $clientCategories);
        } catch (Exception $e) {
            error_log('Translation getClientTranslations error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Detect current language
     */
    private static function detectLanguage(): string
    {
        if (session_status() === PHP_SESSION_NONE && !headers_sent()) {
            try {
                session_start();
            } catch (Exception $e) {
                error_log('Failed to start session: ' . $e->getMessage());
                // Continue without session for language detection
            }
        }

        if (isset($_GET['lang']) && in_array($_GET['lang'], ['el', 'en'])) {
            $_SESSION['language'] = $_GET['lang'];
            return $_GET['lang'];
        }

        if (isset($_SESSION['language']) && in_array($_SESSION['language'], ['el', 'en'])) {
            return $_SESSION['language'];
        }

        $_SESSION['language'] = 'el';
        return 'el';
    }
    
    /**
     * Load cache
     */
    private static function loadCache(): void
    {
        if (self::$cacheLoaded) {
            return;
        }

        try {
            $db = TenantManager::getDatabase();
            $translations = $db->fetchAll("SELECT * FROM translations");

            foreach ($translations as $translation) {
                $cacheKey = $translation['category'] . '.' . $translation['key'];
                self::$cache[$cacheKey] = $translation;
            }

            self::$cacheLoaded = true;

        } catch (Exception $e) {
            error_log("Failed to load translation cache: " . $e->getMessage());
        }
    }

    /**
     * Clear cache
     */
    public static function clearCache(): void
    {
        self::$cache = [];
        self::$cacheLoaded = false;
    }

    /**
     * Get current language
     */
    public static function getCurrentLanguage(): string
    {
        return self::$currentLanguage ?? 'el';
    }

    /**
     * Set language
     */
    public static function setLanguage(string $language): void
    {
        if (in_array($language, ['el', 'en'])) {
            self::$currentLanguage = $language;
            $_SESSION['language'] = $language;
        }
    }
    
    /**
     * Get discovered texts (for auto-discovery)
     */
    public static function getDiscoveredTexts(): array
    {
        return self::$discoveredTexts;
    }
    
    /**
     * Save discovered texts to database
     */
    public static function saveDiscoveredTexts(): void
    {
        try {
            $db = TenantManager::getDatabase();
            $timestamp = date('Y-m-d H:i:s');
            
            foreach (self::$discoveredTexts as $key => $data) {
                // Check if translation already exists
                $existing = $db->fetchRow("
                    SELECT id FROM translations 
                    WHERE key = :key AND category = :category
                ", [':key' => $key, ':category' => $data['category']]);
                
                if (!$existing) {
                    // Create translation entry with Greek default and empty English
                    $db->query("
                        INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at) 
                        VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
                    ", [
                        ':id' => 'TR' . strtoupper(substr(uniqid(), -8)),
                        ':key' => $key,
                        ':value_el' => $data['default_value'],
                        ':value_en' => '', // Empty, to be translated later
                        ':category' => $data['category'],
                        ':created_at' => $timestamp,
                        ':updated_at' => $timestamp
                    ]);
                }
            }
            
        } catch (Exception $e) {
            error_log("Failed to save discovered texts: " . $e->getMessage());
        }
    }
    
    /**
     * Add dynamic content (services, categories)
     */
    public static function addDynamicText(string $key, string $value, string $category = 'dynamic'): void
    {
        try {
            $db = TenantManager::getDatabase();
            $timestamp = date('Y-m-d H:i:s');
            
            // Check if already exists
            $existing = $db->fetchRow("
                SELECT id FROM translations 
                WHERE key = :key AND category = :category
            ", [':key' => $key, ':category' => $category]);
            
            if (!$existing) {
                // Create translation entry with Greek value and empty English
                $db->query("
                    INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at) 
                    VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
                ", [
                    ':id' => 'TR' . strtoupper(substr(uniqid(), -8)),
                    ':key' => $key,
                    ':value_el' => $value,
                    ':value_en' => '', // Empty, to be translated later
                    ':category' => $category,
                    ':created_at' => $timestamp,
                    ':updated_at' => $timestamp
                ]);
            }

        } catch (Exception $e) {
            error_log("Failed to add dynamic text: " . $e->getMessage());
        }
    }

    /**
     * Get categories (only client-facing categories for admin editing)
     */
    public static function getCategories(): array
    {
        try {
            $db = TenantManager::getDatabase();
            // Include all client-facing categories that appear on client interface
            $clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];
            $placeholders = str_repeat('?,', count($clientCategories) - 1) . '?';

            $result = $db->fetchAll("
                SELECT DISTINCT category, COUNT(*) as count
                FROM translations
                WHERE category IN ({$placeholders})
                GROUP BY category
                ORDER BY category
            ", $clientCategories);
            return $result;
        } catch (Exception $e) {
            error_log('Translation getCategories error: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Get language name
     */
    public static function getLanguageName(string $code): string
    {
        $names = [
            'el' => 'Ελληνικά',
            'en' => 'English'
        ];
        return $names[$code] ?? $code;
    }

    /**
     * Reload translations from database (refresh cache)
     */
    public static function reloadTexts(): void
    {
        self::clearCache();
        self::loadCache();
    }

    /**
     * Generate unique ID for translations
     */
    private static function generateId(): string
    {
        return 'TXT' . strtoupper(substr(uniqid(), -8));
    }

    /**
     * Get all supported languages
     */
    public static function getSupportedLanguages(): array
    {
        return ['el', 'en'];
    }

    /**
     * Get translation version for cache busting
     */
    public static function getVersion(): int
    {
        try {
            $db = TenantManager::getDatabase();
            $lastUpdate = $db->fetchRow("
                SELECT MAX(updated_at) as last_update
                FROM translations
                WHERE category IN ('general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates')
            ");

            return $lastUpdate['last_update'] ? strtotime($lastUpdate['last_update']) : time();
        } catch (Exception $e) {
            error_log('Translation version error: ' . $e->getMessage());
            return time();
        }
    }
}

/**
 * Global translation function
 */
function t(string $key, string $defaultValue = '', string $category = 'client'): string
{
    return Translation::t($key, $defaultValue, $category);
}
