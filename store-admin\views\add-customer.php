<?php

/**
 * Add Customer Page
 * Dedicated page for adding new customers
 */

// Handle form submission
if ($_POST && !isset($_POST['ajax'])) {
    require_once __DIR__ . '/../controllers/customers.php';
    $result = handleCustomersForm($_POST, $db);
    if ($result['success']) {
        Application::redirect('/store-admin/?page=customers', $result['message'], 'success');
    } else {
        $error = $result['error'];
    }
}
?>

<div class="page-header">
    <div class="page-header-left">
        <h1 class="page-title">Add Customer</h1>
        <div class="breadcrumb">
            <a href="/store-admin/?page=customers">Customers</a>
            <span class="breadcrumb-separator">/</span>
            <span>Add Customer</span>
        </div>
    </div>
    <div class="page-header-right">
        <a href="/store-admin/?page=customers" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Customers
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" class="entity-form">
        <input type="hidden" name="action" value="add">
        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

        <div class="form-section">
            <h3 class="form-section-title">Customer Information</h3>

            <div class="form-group">
                <label for="name">Customer Name *</label>
                <input type="text" id="name" name="name" required
                    value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                    placeholder="Enter customer's full name">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email"
                        value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                        placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="phone">Phone</label>
                    <input type="tel" id="phone" name="phone"
                        value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>"
                        placeholder="69XXXXXXXX">
                </div>
            </div>

            <div class="form-group">
                <label for="language">Preferred Language</label>
                <select id="language" name="language">
                    <option value="el" <?php echo (!isset($_POST['language']) || $_POST['language'] === 'el') ? 'selected' : ''; ?>>
                        Greek
                    </option>
                    <option value="en" <?php echo (isset($_POST['language']) && $_POST['language'] === 'en') ? 'selected' : ''; ?>>
                        English
                    </option>
                </select>
            </div>

            <div class="form-group">
                <label for="notes">Notes</label>
                <textarea id="notes" name="notes" rows="4"
                    placeholder="Any special notes about this customer..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Create Customer
            </button>
            <a href="/store-admin/?page=customers" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<!-- Inline styles removed - now using consistent CSS framework -->