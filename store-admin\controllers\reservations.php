<?php
/**
 * Reservations Controller
 * Handles reservation CRUD operations
 */

/**
 * Handle reservation form submission
 */
function handleReservationsForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';
    $id = $data['id'] ?? '';
    
    if ($action === 'save') {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }
        
        // Validate required fields
        if (empty($data['customer_id'])) {
            return ['success' => false, 'error' => 'Customer is required'];
        }
        
        if (empty($data['service_id'])) {
            return ['success' => false, 'error' => 'Service is required'];
        }
        
        if (empty($data['date'])) {
            return ['success' => false, 'error' => 'Date is required'];
        }
        
        if (empty($data['start_time'])) {
            return ['success' => false, 'error' => 'Start time is required'];
        }
        
        // Validate date is not in the past (except for admin)
        if (strtotime($data['date']) < strtotime(date('Y-m-d'))) {
            return ['success' => false, 'error' => 'Cannot create reservations for past dates'];
        }
        
        // Get service to calculate end time and price
        $service = $db->fetchRow("SELECT duration, price FROM services WHERE id = :id", [':id' => $data['service_id']]);
        if (!$service) {
            return ['success' => false, 'error' => 'Selected service not found'];
        }
        
        $duration = $service['duration'];
        $defaultPrice = $service['price'];
        
        $startTime = $data['start_time'];
        $endTime = date('H:i', strtotime($startTime) + ($duration * 60));
        
        // Use provided price or default to service price
        $price = !empty($data['price']) ? (float)$data['price'] : $defaultPrice;
        
        $reservationData = [
            ':customer_id' => $data['customer_id'],
            ':service_id' => $data['service_id'],
            ':employee_id' => $data['employee_id'] ?: null,
            ':date' => $data['date'],
            ':start_time' => $startTime,
            ':end_time' => $endTime,
            ':status' => $data['status'] ?? 'confirmed',
            ':price' => $price,
            ':notes' => Application::sanitize($data['notes']),
            ':updated_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $db->beginTransaction();
            
            // Check for time conflicts (except when editing the same reservation)
            $conflictSql = "SELECT id FROM reservations 
                           WHERE date = :date 
                           AND status != 'cancelled'
                           AND (
                               (start_time <= :start_time AND end_time > :start_time) OR
                               (start_time < :end_time AND end_time >= :end_time) OR
                               (start_time >= :start_time AND end_time <= :end_time)
                           )";
            
            $conflictParams = [
                ':date' => $data['date'],
                ':start_time' => $startTime,
                ':end_time' => $endTime
            ];
            
            if ($data['employee_id']) {
                $conflictSql .= " AND employee_id = :employee_id";
                $conflictParams[':employee_id'] = $data['employee_id'];
            }
            
            if ($id) {
                $conflictSql .= " AND id != :current_id";
                $conflictParams[':current_id'] = $id;
            }
            
            $conflict = $db->fetchRow($conflictSql, $conflictParams);
            if ($conflict) {
                $db->rollback();
                return ['success' => false, 'error' => 'Time slot is already booked'];
            }
            
            if ($id) {
                // Update existing reservation
                $reservationData[':id'] = $id;
                $sql = "UPDATE reservations SET 
                        customer_id = :customer_id, 
                        service_id = :service_id, 
                        employee_id = :employee_id, 
                        date = :date, 
                        start_time = :start_time, 
                        end_time = :end_time, 
                        status = :status, 
                        price = :price, 
                        notes = :notes, 
                        updated_at = :updated_at 
                        WHERE id = :id";
            } else {
                // Create new reservation
                $reservationData[':id'] = Application::generateId('RSV');
                $reservationData[':created_at'] = date('Y-m-d H:i:s');
                $sql = "INSERT INTO reservations (id, customer_id, service_id, employee_id, date, start_time, end_time, 
                        status, price, notes, created_at, updated_at) 
                        VALUES (:id, :customer_id, :service_id, :employee_id, :date, :start_time, :end_time, 
                        :status, :price, :notes, :created_at, :updated_at)";
            }
            
            $result = $db->query($sql, $reservationData);
            
            if ($result !== false) {
                $db->commit();
                
                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=reservations',
                    'message' => $id ? 'Reservation updated successfully' : 'Reservation created successfully'
                ];
            } else {
                $db->rollback();
                return ['success' => false, 'error' => 'Failed to save reservation'];
            }
            
        } catch (Exception $e) {
            $db->rollback();
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    return ['success' => false, 'error' => 'Invalid action'];
}
