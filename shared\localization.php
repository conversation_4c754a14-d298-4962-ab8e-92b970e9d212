<?php

/**
 * Localization System
 * Handles multi-language support and text management
 */

require_once __DIR__ . '/tenant_manager.php';

class LocalizationManager
{
    private static ?string $currentLanguage = null;
    private static array $texts = [];
    private static bool $initialized = false;

    /**
     * Initialize localization system
     */
    public static function init(): void
    {
        if (self::$initialized) {
            return;
        }

        self::detectLanguage();
        self::loadTexts();
        self::$initialized = true;
    }
    
    /**
     * Detect user's preferred language
     */
    private static function detectLanguage(): void
    {
        // Priority order: URL parameter > Session > Browser > Default

        // 1. Check URL parameter
        if (isset($_GET['lang']) && in_array($_GET['lang'], Config::SUPPORTED_LANGUAGES)) {
            self::$currentLanguage = $_GET['lang'];
            SessionManager::set('language', self::$currentLanguage);
            return;
        }

        // 2. Check session
        $sessionLang = SessionManager::get('language');
        if ($sessionLang && in_array($sessionLang, Config::SUPPORTED_LANGUAGES)) {
            self::$currentLanguage = $sessionLang;
            return;
        }

        // 3. Check browser preference
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $browserLangs = explode(',', $_SERVER['HTTP_ACCEPT_LANGUAGE']);
            foreach ($browserLangs as $lang) {
                $lang = strtolower(substr(trim($lang), 0, 2));
                if (in_array($lang, Config::SUPPORTED_LANGUAGES)) {
                    self::$currentLanguage = $lang;
                    SessionManager::set('language', self::$currentLanguage);
                    return;
                }
            }
        }

        // 4. Default language
        self::$currentLanguage = Config::DEFAULT_LANGUAGE;
        SessionManager::set('language', self::$currentLanguage);
    }
    
    private static function loadTexts(): void
    {
        try {
            $db = TenantManager::getDatabase();
            $texts = $db->fetchAll(
                "SELECT text_key, text_value FROM texts WHERE language = :lang",
                [':lang' => self::$currentLanguage]
            );

            foreach ($texts as $text) {
                self::$texts[$text['text_key']] = $text['text_value'];
            }
            
        } catch (Exception $e) {
            error_log("Failed to load localization texts: " . $e->getMessage());
        }
    }
    
    public static function getText(string $key, string $default = ''): string
    {
        return self::$texts[$key] ?? $default;
    }
    
    public static function t(string $key, string $default = ''): string
    {
        return self::getText($key, $default);
    }
    
    public static function getCurrentLanguage(): string
    {
        return self::$currentLanguage ?? Config::DEFAULT_LANGUAGE;
    }
    
    public static function setLanguage(string $language): void
    {
        if (in_array($language, Config::SUPPORTED_LANGUAGES)) {
            self::$currentLanguage = $language;
            SessionManager::set('language', $language);
            self::loadTexts();
        }
    }
    
    public static function getSupportedLanguages(): array
    {
        return Config::SUPPORTED_LANGUAGES;
    }
    
    public static function getLanguageName(string $code): string
    {
        $names = [
            'en' => 'English',
            'el' => 'Ελληνικά',
            'fr' => 'Français',
            'de' => 'Deutsch',
            'es' => 'Español',
            'it' => 'Italiano'
        ];
        
        return $names[$code] ?? $code;
    }
    
    public static function updateText(string $key, string $language, string $value): bool
    {
        try {
            $db = TenantManager::getDatabase();
            
            $existing = $db->fetchRow(
                "SELECT id FROM texts WHERE text_key = :key AND language = :lang AND category = 'ui'",
                [':key' => $key, ':lang' => $language]
            );

            if ($existing) {
                $result = $db->query(
                    "UPDATE texts SET text_value = :value, updated_at = :updated WHERE id = :id",
                    [
                        ':value' => $value,
                        ':updated' => date('Y-m-d H:i:s'),
                        ':id' => $existing['id']
                    ]
                );
            } else {
                $result = $db->query(
                    "INSERT INTO texts (id, text_key, text_value, language, category, created_at, updated_at) VALUES (:id, :key, :value, :lang, :category, :created, :updated)",
                    [
                        ':id' => 'TXT' . uniqid(),
                        ':key' => $key,
                        ':value' => $value,
                        ':lang' => $language,
                        ':category' => 'ui',
                        ':created' => date('Y-m-d H:i:s'),
                        ':updated' => date('Y-m-d H:i:s')
                    ]
                );
            }
            
            // Refresh texts if current language
            if ($language === self::$currentLanguage) {
                self::loadTexts();
            }
            
            return $result !== false;
            
        } catch (Exception $e) {
            error_log("Failed to update text: " . $e->getMessage());
            return false;
        }
    }
    
    public static function getAllTexts(string $language = null): array
    {
        $language = $language ?? self::$currentLanguage;
        
        try {
            $db = TenantManager::getDatabase();
            return $db->fetchAll(
                "SELECT key, value FROM texts WHERE language = :lang ORDER BY key",
                [':lang' => $language]
            );
        } catch (Exception $e) {
            error_log("Failed to get all texts: " . $e->getMessage());
            return [];
        }
    }
    
    public static function getTextsByKey(string $key): array
    {
        try {
            $db = TenantManager::getDatabase();
            $texts = $db->fetchAll(
                "SELECT language, text_value FROM texts WHERE text_key = :key",
                [':key' => $key]
            );

            $result = [];
            foreach ($texts as $text) {
                $result[$text['language']] = $text['text_value'];
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Failed to get texts by key: " . $e->getMessage());
            return [];
        }
    }
    
    public static function deleteText(string $key, string $language = null): bool
    {
        try {
            $db = TenantManager::getDatabase();
            
            if ($language) {
                $result = $db->query(
                    "DELETE FROM texts WHERE text_key = :key AND language = :lang",
                    [':key' => $key, ':lang' => $language]
                );
            } else {
                $result = $db->query(
                    "DELETE FROM texts WHERE text_key = :key",
                    [':key' => $key]
                );
            }
            
            // Refresh texts if current language affected
            if (!$language || $language === self::$currentLanguage) {
                self::loadTexts();
            }
            
            return $result !== false;
            
        } catch (Exception $e) {
            error_log("Failed to delete text: " . $e->getMessage());
            return false;
        }
    }
    
    public static function renderLanguageSelector(string $baseUrl = ''): string
    {
        $current = self::getCurrentLanguage();
        $languages = self::getSupportedLanguages();
        
        if (count($languages) <= 1) {
            return '';
        }
        
        $html = '<div class="language-selector">';
        
        foreach ($languages as $code) {
            $name = self::getLanguageName($code);
            $active = $code === $current ? ' class="active"' : '';
            $url = $baseUrl . '?lang=' . $code;
            
            $html .= '<a href="' . $url . '"' . $active . '>' . $name . '</a>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
}
