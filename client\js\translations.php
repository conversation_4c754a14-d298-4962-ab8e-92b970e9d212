<?php
/**
 * JavaScript Translations Generator
 * Generates JavaScript translation object for client-side use
 */

// Prevent caching to ensure admin changes are immediately reflected
header('Content-Type: application/javascript; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0, private');
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');
header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
header('ETag: "' . md5(time() . rand()) . '"');
header('Vary: *');

// Initialize translation system
require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

try {
    // Initialize systems
    Config::init();
    TenantManager::init();
    Translation::init();

    $language = Translation::getCurrentLanguage();
    $db = TenantManager::getDatabase();


    
    // Get all client-side translations from new translations table (including dynamic content)
    // Order by category priority: lower priority first, so client category overwrites others
    $translations = $db->fetchAll("
        SELECT key, value_el, value_en, category, updated_at
        FROM translations
        WHERE category IN ('general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'ui', 'dates', 'client')
        ORDER BY
            CASE category
                WHEN 'client' THEN 9
                WHEN 'ui' THEN 8
                WHEN 'user_interface' THEN 7
                ELSE 1
            END,
            key
    ");

    // Get last update time for cache busting
    $lastUpdate = $db->fetchRow("
        SELECT MAX(updated_at) as last_update
        FROM translations
        WHERE category IN ('general', 'booking_system', 'booking_flow', 'user_interface', 'ui', 'dates', 'client')
    ");
    
    // Convert to JavaScript object
    $clientTranslations = [];
    foreach ($translations as $translation) {
        $key = $translation['key'];
        $value = $translation['value_' . $language];

        // Use Greek as fallback if English is empty
        if (empty($value) && $language === 'en') {
            $value = $translation['value_el'];
        }

        $clientTranslations[$key] = $value;
    }
    
    // Add fallback translations if missing
    $fallbacks = [
        'select_category' => $language === 'el' ? 'Επιλέξτε Κατηγορία' : 'Select Category',
        'select_service' => $language === 'el' ? 'Επιλέξτε Υπηρεσία' : 'Select Service',
        'select_date' => $language === 'el' ? 'Επιλέξτε Ημερομηνία' : 'Select Date',
        'select_time' => $language === 'el' ? 'Επιλέξτε Ώρα' : 'Select Time',
        'select_time_staff' => $language === 'el' ? 'Επιλέξτε Ώρα & Προσωπικό' : 'Select Time & Staff',
        'contact_details' => $language === 'el' ? 'Στοιχεία Επικοινωνίας' : 'Contact Details',
        'verification' => $language === 'el' ? 'Επιβεβαίωση' : 'Verification',
        'confirmation' => $language === 'el' ? 'Επιβεβαίωση' : 'Confirmation',
        'choose_preferred_time' => $language === 'el' ? 'Επιλέξτε την προτιμώμενη ώρα' : 'Choose your preferred time slot',
        'choose_preferred_time_staff' => $language === 'el' ? 'Επιλέξτε την προτιμώμενη ώρα και μέλος προσωπικού' : 'Choose your preferred time slot and staff member',
        'name' => $language === 'el' ? 'Όνομα' : 'Name',
        'email' => $language === 'el' ? 'Email' : 'Email',
        'phone' => $language === 'el' ? 'Τηλέφωνο' : 'Phone',
        'notes' => $language === 'el' ? 'Σημειώσεις' : 'Notes',
        'next' => $language === 'el' ? 'Επόμενο' : 'Next',
        'back' => $language === 'el' ? 'Πίσω' : 'Back',
        'continue' => $language === 'el' ? 'Συνέχεια' : 'Continue',
        'loading' => $language === 'el' ? 'Φόρτωση...' : 'Loading...',
        'please_wait' => $language === 'el' ? 'Παρακαλώ περιμένετε...' : 'Please wait...',
        'booking_confirmed' => $language === 'el' ? 'Το ραντεβού επιβεβαιώθηκε!' : 'Booking Confirmed!',
        'booking_success_message' => $language === 'el' ? 'Το ραντεβού σας κλείστηκε επιτυχώς. Θα λάβετε email επιβεβαίωσης σύντομα.' : 'Your appointment has been successfully booked. We\'ll send you a confirmation email shortly.',
        'book_another' => $language === 'el' ? 'Κλείσιμο Άλλου Ραντεβού' : 'Book Another Appointment',
        'available' => $language === 'el' ? 'Διαθέσιμο' : 'Available',
        'unavailable' => $language === 'el' ? 'Μη διαθέσιμο' : 'Unavailable',
        'closed' => $language === 'el' ? 'Κλειστό' : 'Closed',
        'required_field' => $language === 'el' ? 'Αυτό το πεδίο είναι υποχρεωτικό' : 'This field is required',
        'invalid_email' => $language === 'el' ? 'Παρακαλώ εισάγετε έγκυρο email' : 'Please enter a valid email',
        'invalid_phone' => $language === 'el' ? 'Παρακαλώ εισάγετε έγκυρο τηλέφωνο' : 'Please enter a valid phone number',
        'enter_verification_code' => $language === 'el' ? 'Εισάγετε τον κωδικό επιβεβαίωσης' : 'Enter verification code',
        'verification_code_sent' => $language === 'el' ? 'Ο κωδικός επιβεβαίωσης στάλθηκε' : 'Verification code sent',
        'verify_email' => $language === 'el' ? 'Επιβεβαιώστε το Email σας' : 'Verify Your Email',
        'verify_phone' => $language === 'el' ? 'Επιβεβαιώστε το Τηλέφωνό σας' : 'Verify Your Phone',
        'verification_code_sent_to_email' => $language === 'el' ? 'Στείλαμε κωδικό επιβεβαίωσης στο email' : 'We sent a verification code to your email',
        'verification_code_sent_to_phone' => $language === 'el' ? 'Στείλαμε κωδικό επιβεβαίωσης στο τηλέφωνο' : 'We sent a verification code to your phone',
        'didnt_receive_code' => $language === 'el' ? 'Δεν λάβατε τον κωδικό;' : 'Didn\'t receive the code?',
        'resend_code' => $language === 'el' ? 'Επαναποστολή κωδικού' : 'Resend code',
        'verify_booking' => $language === 'el' ? 'Επιβεβαίωση Ραντεβού' : 'Verify Booking',
        'minutes' => $language === 'el' ? 'λεπτά' : 'min',
        'any_staff' => $language === 'el' ? 'Οποιοδήποτε προσωπικό' : 'Any Staff',
        'auto_assign' => $language === 'el' ? 'Αυτόματη ανάθεση' : 'Auto Assign',
        'no_available_times' => $language === 'el' ? 'Δεν υπάρχουν διαθέσιμες ώρες' : 'No available times',
        'select_different_date' => $language === 'el' ? 'Επιλέξτε διαφορετική ημερομηνία' : 'Please select a different date',
        'booking_summary' => $language === 'el' ? 'Περίληψη Ραντεβού' : 'Booking Summary',
        'service' => $language === 'el' ? 'Υπηρεσία' : 'Service',
        'date' => $language === 'el' ? 'Ημερομηνία' : 'Date',
        'time' => $language === 'el' ? 'Ώρα' : 'Time',
        'staff' => $language === 'el' ? 'Προσωπικό' : 'Staff',
        'duration' => $language === 'el' ? 'Διάρκεια' : 'Duration',
        'price' => $language === 'el' ? 'Τιμή' : 'Price',

        // Navigation buttons
        'previous' => $language === 'el' ? 'Προηγούμενο' : 'Previous',
        'next' => $language === 'el' ? 'Επόμενο' : 'Next',
        'back' => $language === 'el' ? 'Πίσω' : 'Back',
        'continue' => $language === 'el' ? 'Συνέχεια' : 'Continue',

        // Date translations
        'monday' => $language === 'el' ? 'Δευτέρα' : 'Monday',
        'tuesday' => $language === 'el' ? 'Τρίτη' : 'Tuesday',
        'wednesday' => $language === 'el' ? 'Τετάρτη' : 'Wednesday',
        'thursday' => $language === 'el' ? 'Πέμπτη' : 'Thursday',
        'friday' => $language === 'el' ? 'Παρασκευή' : 'Friday',
        'saturday' => $language === 'el' ? 'Σάββατο' : 'Saturday',
        'sunday' => $language === 'el' ? 'Κυριακή' : 'Sunday',
        'mon' => $language === 'el' ? 'Δευ' : 'Mon',
        'tue' => $language === 'el' ? 'Τρι' : 'Tue',
        'wed' => $language === 'el' ? 'Τετ' : 'Wed',
        'thu' => $language === 'el' ? 'Πεμ' : 'Thu',
        'fri' => $language === 'el' ? 'Παρ' : 'Fri',
        'sat' => $language === 'el' ? 'Σαβ' : 'Sat',
        'sun' => $language === 'el' ? 'Κυρ' : 'Sun',
        'january' => $language === 'el' ? 'Ιανουάριος' : 'January',
        'february' => $language === 'el' ? 'Φεβρουάριος' : 'February',
        'march' => $language === 'el' ? 'Μάρτιος' : 'March',
        'april' => $language === 'el' ? 'Απρίλιος' : 'April',
        'may' => $language === 'el' ? 'Μάιος' : 'May',
        'june' => $language === 'el' ? 'Ιούνιος' : 'June',
        'july' => $language === 'el' ? 'Ιούλιος' : 'July',
        'august' => $language === 'el' ? 'Αύγουστος' : 'August',
        'september' => $language === 'el' ? 'Σεπτέμβριος' : 'September',
        'october' => $language === 'el' ? 'Οκτώβριος' : 'October',
        'november' => $language === 'el' ? 'Νοέμβριος' : 'November',
        'december' => $language === 'el' ? 'Δεκέμβριος' : 'December',
    ];
    
    // Merge fallbacks with database translations
    foreach ($fallbacks as $key => $value) {
        if (!isset($clientTranslations[$key]) || empty($clientTranslations[$key])) {
            $clientTranslations[$key] = $value;
        }
    }
    
} catch (Exception $e) {
    error_log("Translation generation failed: " . $e->getMessage());
    $clientTranslations = [];
    $language = 'el';
}

// Debug: Log translation count
error_log("Client translations loaded: " . count($clientTranslations) . " for language: {$language}");

// Output JavaScript
$version = $lastUpdate['last_update'] ? strtotime($lastUpdate['last_update']) : time();
echo "// Auto-generated translations for language: {$language}\n";
echo "// Generated at: " . date('Y-m-d H:i:s') . "\n";
echo "// Tenant: " . TenantManager::getCurrentTenant() . "\n";
echo "// Version: {$version}\n";
echo "// Last update: " . ($lastUpdate['last_update'] ?: 'Never') . "\n\n";

echo "window.translations = " . json_encode($clientTranslations, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . ";\n";
echo "window.translationsVersion = {$version};\n";
echo "window.currentLanguage = " . json_encode($language) . ";\n";
echo "window.currentTenant = " . json_encode(TenantManager::getCurrentTenant()) . ";\n\n";
echo "// Translation function\n";
echo "window.t = function(key, defaultValue) {\n";
echo "    return window.translations[key] || defaultValue || key;\n";
echo "};\n";
?>
