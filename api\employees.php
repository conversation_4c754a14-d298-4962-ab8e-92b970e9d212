<?php
/**
 * Employees API
 * Get employees for a specific service and date
 */

// Include required files (initialization already done by index.php)
require_once __DIR__ . '/../shared/availability_checker.php';
require_once __DIR__ . '/../shared/functions.php';

try {
    // Get parameters
    $serviceId = $_GET['service_id'] ?? '';
    $date = $_GET['date'] ?? '';
    $time = $_GET['time'] ?? null; // Optional - for specific time slot

    if (!$serviceId || !$date) {
        errorResponse('Missing required parameters: service_id and date');
    }

    // Validate date format
    if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
        errorResponse('Invalid date format. Use YYYY-MM-DD');
    }

    // Get database connection
    $db = TenantManager::getDatabase();
    $checker = new AvailabilityChecker($db);

    // Get service details
    $service = $db->fetchRow(
        "SELECT * FROM services WHERE id = :id AND is_active = 1",
        [':id' => $serviceId]
    );

    if (!$service) {
        errorResponse('Service not found or inactive');
    }

    // Get all employees who can perform this service
    $employees = $db->fetchAll(
        "SELECT e.* FROM employees e
         JOIN employee_services es ON e.id = es.employee_id
         WHERE es.service_id = :service_id AND e.is_active = 1
         ORDER BY e.name",
        [':service_id' => $serviceId]
    );

    if (empty($employees)) {
        successResponse([
            'service_id' => $serviceId,
            'date' => $date,
            'employees' => []
        ]);
        exit;
    }

    // Filter employees based on whether time is specified
    $availableEmployees = [];

    foreach ($employees as $employee) {
        if ($time) {
            // For specific time slot - check if employee is available (not busy)
            if ($checker->isEmployeeAvailableAtTime($employee['id'], $date, $time, $service['duration'])) {
                $availableEmployees[] = [
                    'id' => $employee['id'],
                    'name' => $employee['name'],
                    'position' => $employee['position'] ?? '',
                    'color' => $employee['color'] ?? '#3498db'
                ];
            }
        } else {
            // For general date - check if employee is working on this date
            if ($checker->isEmployeeWorkingOnDay($employee, date('w', strtotime($date)), $date)) {
                $availableEmployees[] = [
                    'id' => $employee['id'],
                    'name' => $employee['name'],
                    'position' => $employee['position'] ?? '',
                    'color' => $employee['color'] ?? '#3498db'
                ];
            }
        }
    }

    successResponse([
        'service_id' => $serviceId,
        'date' => $date,
        'service_name' => $service['name'],
        'employees' => $availableEmployees
    ]);

} catch (Exception $e) {
    logActivity("Employees API error: " . $e->getMessage(), 'error');
    errorResponse('Failed to fetch employee data');
}
