<?php
/**
 * Translation Controller
 * Handles all translation operations for current tenant
 */

// Ensure Translation class is available
if (!class_exists('Translation')) {
    require_once __DIR__ . '/../../shared/translation.php';
}

class TranslationController
{
    public static function handleRequest(array $data, Database $db): array
    {
        $action = $data['action'] ?? 'list';
        
        if (in_array($action, ['save', 'delete']) && 
            !Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }
        
        switch ($action) {
            case 'save':
                return self::save($data);
            case 'delete':
                return self::delete($data);
            default:
                return ['success' => false, 'error' => 'Unknown action'];
        }
    }
    
    private static function save(array $data): array
    {
        try {
            // Ensure Translation class is initialized
            if (!class_exists('Translation')) {
                throw new Exception('Translation class not available');
            }

            $saved = 0;
            $errors = [];

            // Group translations by key to process them together
            $translationPairs = [];

            foreach ($data as $key => $value) {
                if (strpos($key, 'el_') === 0) {
                    $translationKey = substr($key, 3);
                    if (!isset($translationPairs[$translationKey])) {
                        $translationPairs[$translationKey] = ['el' => '', 'en' => ''];
                    }
                    $translationPairs[$translationKey]['el'] = trim($value);
                } elseif (strpos($key, 'en_') === 0) {
                    $translationKey = substr($key, 3);
                    if (!isset($translationPairs[$translationKey])) {
                        $translationPairs[$translationKey] = ['el' => '', 'en' => ''];
                    }
                    $translationPairs[$translationKey]['en'] = trim($value);
                }
            }

            $db = TenantManager::getDatabase();
            // Include dynamic content categories (services, categories) that appear on client
            $clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];

            foreach ($translationPairs as $translationKey => $values) {
                $valueEl = $values['el'];
                $valueEn = $values['en'];

                // Get category from form data or find existing translation
                $categoryKey = 'category_' . $translationKey;
                if (isset($data[$categoryKey])) {
                    $category = $data[$categoryKey];
                } else {
                    // Fallback: find the existing translation to determine its category
                    $existing = $db->fetchRow("
                        SELECT category FROM translations WHERE key = :key LIMIT 1
                    ", [':key' => $translationKey]);
                    $category = $existing['category'] ?? 'client'; // Default to 'client' instead of 'general'
                }

                // Only allow saving client-facing translations
                if (!in_array($category, $clientCategories)) {
                    $errors[] = "Cannot edit admin translation: {$translationKey} (category: {$category})";
                    continue;
                }

                try {
                    if (Translation::save($translationKey, $valueEl, $valueEn, $category)) {
                        $saved++;
                    }
                } catch (Exception $e) {
                    $errors[] = "Failed to save {$translationKey}: " . $e->getMessage();
                }
            }

            if (!empty($errors)) {
                return [
                    'success' => false,
                    'error' => "Saved {$saved} translations, but had errors: " . implode(', ', $errors)
                ];
            }

            return [
                'success' => true,
                'message' => "Saved {$saved} translations successfully",
                'saved_count' => $saved
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    private static function delete(array $data): array
    {
        try {
            $key = $data['key'] ?? '';
            $category = $data['category'] ?? 'general';
            
            if (empty($key)) {
                return ['success' => false, 'error' => 'Translation key is required'];
            }
            
            $db = TenantManager::getDatabase();
            $result = $db->query("
                DELETE FROM translations 
                WHERE key = :key AND category = :category
            ", [':key' => $key, ':category' => $category]);
            
            if ($result) {
                Translation::clearCache();
                return ['success' => true, 'message' => 'Translation deleted successfully'];
            } else {
                return ['success' => false, 'error' => 'Failed to delete translation'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
?>
