<?php

/**
 * Categories View
 * Manage service categories
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by name, English name, description, and icon
$searchWhere = AdminHelpers::buildSearchWhere($search, ['name', 'name_en', 'description', 'icon']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total FROM categories {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get categories with proper SQL pagination
$sql = "SELECT * FROM categories {$searchWhere['where']} ORDER BY sort_order ASC, name ASC LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedCategories = $db->fetchAll($sql, $searchWhere['params']);



// Add services to each category
foreach ($paginatedCategories as &$category) {
    $category['services'] = $db->fetchAll(
        "SELECT id, name, price, is_active FROM services WHERE category_id = :category_id ORDER BY name ASC",
        [':category_id' => $category['id']]
    );
}
unset($category); // Important: unset the reference to prevent issues with subsequent foreach loops

// Force browser cache refresh
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

?>



<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-category" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Category
        </a>
        <button class="btn btn-secondary" onclick="reorderCategories()">
            <i class="fas fa-sort"></i> Reorder
        </button>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search categories, descriptions..." value="<?php echo htmlspecialchars($search); ?>" id="category-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('category-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Sort:</label>
            <select class="form-control" id="sort-filter">
                <option value="sort_order">Sort Order</option>
                <option value="name">Name A-Z</option>
                <option value="services_count">Service Count</option>
                <option value="created_at">Date Created</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Services:</label>
            <select class="form-control" id="services-filter">
                <option value="">All Categories</option>
                <option value="with-services">With Services</option>
                <option value="empty">Empty Categories</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter">
                <option value="">All Categories</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Color:</label>
            <select class="form-control" id="color-filter">
                <option value="">All Colors</option>
                <option value="blue">Blue</option>
                <option value="green">Green</option>
                <option value="red">Red</option>
                <option value="purple">Purple</option>
                <option value="orange">Orange</option>
            </select>
        </div>
    </div>
</div>



<!-- Enhanced Categories Grid -->
<div class="entity-grid" id="categories-grid">
    <?php if (empty($paginatedCategories)): ?>
        <div class="empty-state">
            <i class="fas fa-tags fa-2x text-muted"></i>
            <h3>No categories found</h3>
            <p>Start by adding your first category or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addCategory()">
                <i class="fas fa-plus"></i> Add First Category
            </button>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedCategories as $categoryIndex => $category):
            // Prepare category data for the card component
            $serviceCount = count($category['services'] ?? []);
            $categoryColor = $category['color'] ?: '#' . substr(md5($category['name']), 0, 6);

            // Prepare category data for card
            $categoryData = [
                'id' => $category['id'],
                'name' => $category['name'],
                'name_en' => $category['name_en'] ?? '',
                'description' => $category['description'] ?? '',
                'color' => $categoryColor,
                'icon' => $category['icon'] ?? 'fas fa-tag',
                'active' => $category['is_active'] ?? true,
                'services_count' => $serviceCount,
                'sort_order' => $category['sort_order'] ?? 0,
                'services' => $category['services'] ?? [],
                'created_at' => $category['created_at'] ?? null
            ];

            // Render the category card
            echo renderEntityCard($categoryData, 'category', [
                'show_checkbox' => false,
                'show_actions' => false
            ]);
        endforeach; ?>

        <!-- Add Category Card -->
        <div class="entity-card add-card" onclick="window.location.href='/store-admin/?page=add-category'">
            <div class="card__body">
                <div class="add-card-content">
                    <i class="fas fa-plus fa-2x" style="color: var(--gray-400); margin-bottom: 16px;"></i>
                    <h3 style="color: var(--gray-600); font-size: var(--text-lg);">Add New Category</h3>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>