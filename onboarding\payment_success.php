<?php
session_start();

// Check if payment was successful
if (!isset($_SESSION['payment_success'])) {
    header('Location: register_shop.php');
    exit;
}

$payment_data = $_SESSION['payment_success'];
$tenant_id = $payment_data['tenant_id'];
$credentials = $payment_data['credentials'];
$payment_info = $payment_data['payment'];

// Clear the payment success data from session
unset($_SESSION['payment_success']);
unset($_SESSION['registration_data']);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to GK Radevou - Account Created Successfully!</title>
    <link rel="stylesheet" href="assets/landing.css">
    <style>
        .success-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-top: 100px;
            margin-bottom: 2rem;
        }
        
        .success-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .success-icon {
            font-size: 4rem;
            color: #28a745;
            margin-bottom: 1rem;
        }
        
        .success-header h1 {
            color: #28a745;
            margin-bottom: 0.5rem;
        }
        
        .success-header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .credentials-section {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            border: 2px solid #28a745;
        }
        
        .credentials-section h2 {
            color: #28a745;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .credentials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .credential-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .credential-card h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .credential-value {
            font-family: monospace;
            background: #e9ecef;
            padding: 0.75rem;
            border-radius: 4px;
            font-size: 1rem;
            font-weight: bold;
            color: #007bff;
            word-break: break-all;
        }
        
        .security-warning {
            background: #fff3cd;
            color: #856404;
            padding: 1rem;
            border-radius: 6px;
            margin-top: 1rem;
            text-align: center;
        }
        
        .next-steps {
            background: #e8f5e8;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .next-steps h2 {
            color: #155724;
            margin-bottom: 1rem;
        }
        
        .steps-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
        }
        
        .step-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: #28a745;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 1rem;
        }
        
        .step-card h3 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .step-card p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .action-buttons {
            text-align: center;
            margin: 2rem 0;
        }
        
        .action-buttons .btn-primary {
            font-size: 1.2rem;
            padding: 1rem 2rem;
            margin: 0.5rem;
        }
        
        .payment-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .payment-summary h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        
        .payment-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .payment-detail {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #ddd;
        }
        
        .payment-detail:last-child {
            border-bottom: none;
        }
        
        .trial-info {
            background: #d4edda;
            color: #155724;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .trial-info h3 {
            margin-bottom: 1rem;
        }
        
        .support-section {
            background: #e3f2fd;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .support-section h2 {
            color: #0d47a1;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .support-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .support-option {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .support-option h4 {
            color: #0d47a1;
            margin-bottom: 0.5rem;
        }
        
        .support-option p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            margin-left: 0.5rem;
        }
        
        .copy-btn:hover {
            background: #0056b3;
        }
        
        @media (max-width: 768px) {
            .success-container {
                margin-top: 80px;
                margin-left: 1rem;
                margin-right: 1rem;
            }
            
            .credentials-grid,
            .steps-grid,
            .support-options {
                grid-template-columns: 1fr;
            }
            
            .action-buttons .btn-primary {
                display: block;
                margin: 0.5rem auto;
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>GK Radevou</h2>
            </div>
            <div class="nav-actions">
                <a href="<?php echo htmlspecialchars($credentials['login_url']); ?>" class="btn-primary">Login to Account</a>
            </div>
        </div>
    </nav>

    <div class="success-container">
        <div class="success-header">
            <div class="success-icon">🎉</div>
            <h1>Welcome to GK Radevou!</h1>
            <p>Your account has been successfully created and your free trial has started</p>
        </div>

        <div class="trial-info">
            <h3>🆓 Your 14-Day Free Trial is Now Active</h3>
            <p>Full access to all features until <strong><?php echo date('F j, Y', strtotime('+14 days')); ?></strong></p>
            <p>No charges until trial ends • Cancel anytime</p>
        </div>

        <div class="credentials-section">
            <h2>🔐 Your Login Credentials</h2>
            <div class="credentials-grid">
                <div class="credential-card">
                    <h3>Username</h3>
                    <div class="credential-value">
                        <?php echo htmlspecialchars($credentials['username']); ?>
                        <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($credentials['username']); ?>')">Copy</button>
                    </div>
                </div>
                <div class="credential-card">
                    <h3>Password</h3>
                    <div class="credential-value">
                        <?php echo htmlspecialchars($credentials['password']); ?>
                        <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($credentials['password']); ?>')">Copy</button>
                    </div>
                </div>
                <div class="credential-card">
                    <h3>Login URL</h3>
                    <div class="credential-value">
                        <?php echo htmlspecialchars($credentials['login_url']); ?>
                        <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($credentials['login_url']); ?>')">Copy</button>
                    </div>
                </div>
            </div>
            <div class="security-warning">
                <strong>⚠️ Important:</strong> Please save these credentials securely and change your password after first login.
            </div>
        </div>

        <div class="action-buttons">
            <a href="<?php echo htmlspecialchars($credentials['login_url']); ?>" class="btn-primary">Login to Your Account</a>
            <a href="<?php echo htmlspecialchars($credentials['setup_url']); ?>" class="btn-primary">Quick Setup Guide</a>
        </div>

        <div class="payment-summary">
            <h3>💳 Payment Summary</h3>
            <div class="payment-info">
                <div class="payment-detail">
                    <span>Transaction ID:</span>
                    <span><?php echo htmlspecialchars($payment_info['transaction_id']); ?></span>
                </div>
                <div class="payment-detail">
                    <span>Amount:</span>
                    <span>$<?php echo number_format($payment_info['amount'], 2); ?>/month</span>
                </div>
                <div class="payment-detail">
                    <span>Payment Method:</span>
                    <span><?php echo ucfirst($payment_info['method']); ?></span>
                </div>
                <div class="payment-detail">
                    <span>Status:</span>
                    <span style="color: #28a745; font-weight: bold;">✅ Active</span>
                </div>
            </div>
        </div>

        <div class="next-steps">
            <h2>🚀 Next Steps to Get Started</h2>
            <div class="steps-grid">
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h3>Login</h3>
                    <p>Use your credentials to access your account</p>
                </div>
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h3>Setup</h3>
                    <p>Complete the quick setup wizard</p>
                </div>
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h3>Configure</h3>
                    <p>Add your services and business settings</p>
                </div>
                <div class="step-card">
                    <div class="step-number">4</div>
                    <h3>Import</h3>
                    <p>Import existing customer data</p>
                </div>
                <div class="step-card">
                    <div class="step-number">5</div>
                    <h3>Launch</h3>
                    <p>Start taking bookings and managing your business</p>
                </div>
            </div>
        </div>

        <div class="support-section">
            <h2>📞 Need Help Getting Started?</h2>
            <div class="support-options">
                <div class="support-option">
                    <h4>📧 Email Support</h4>
                    <p><EMAIL></p>
                    <p>Response within 2 hours</p>
                </div>
                <div class="support-option">
                    <h4>📞 Phone Support</h4>
                    <p>******-123-4567</p>
                    <p>Mon-Fri 9AM-6PM EST</p>
                </div>
                <div class="support-option">
                    <h4>💬 Live Chat</h4>
                    <p>Available in your dashboard</p>
                    <p>Instant support</p>
                </div>
                <div class="support-option">
                    <h4>📚 Help Center</h4>
                    <p>help.gkradevou.com</p>
                    <p>Guides and tutorials</p>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 2rem 0; padding: 2rem; background: #f8f9fa; border-radius: 8px;">
            <h3>📧 Check Your Email</h3>
            <p>We've sent a welcome email with your login credentials and next steps to:</p>
            <p style="font-weight: bold; color: #007bff;"><?php echo htmlspecialchars($credentials['email']); ?></p>
            <p style="color: #666; font-size: 0.9rem;">Don't see it? Check your spam folder or contact support.</p>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show success message
                const notification = document.createElement('div');
                notification.textContent = 'Copied to clipboard!';
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #28a745;
                    color: white;
                    padding: 10px 15px;
                    border-radius: 5px;
                    z-index: 1000;
                    font-size: 14px;
                `;
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 2000);
            }).catch(function() {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                
                alert('Copied to clipboard!');
            });
        }

        // Auto-redirect to login after 30 seconds (optional)
        let redirectTimer = null;
        
        function startRedirectTimer() {
            redirectTimer = setTimeout(() => {
                if (confirm('Would you like to go to your account now?')) {
                    window.location.href = '<?php echo htmlspecialchars($credentials['login_url']); ?>';
                }
            }, 30000);
        }
        
        // Uncomment to enable auto-redirect
        // startRedirectTimer();
        
        // Track successful registration
        if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
                'send_to': 'AW-CONVERSION_ID/CONVERSION_LABEL',
                'value': <?php echo $payment_info['amount']; ?>,
                'currency': 'USD',
                'transaction_id': '<?php echo $payment_info['transaction_id']; ?>'
            });
        }
        
        // Show success animation
        document.addEventListener('DOMContentLoaded', function() {
            const successIcon = document.querySelector('.success-icon');
            successIcon.style.animation = 'bounce 2s ease-in-out';
        });
    </script>
    
    <style>
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }
    </style>
</body>
</html>
