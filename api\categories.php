<?php

/**
 * Categories API
 * Provides category data for the booking system
 */

// Basic error handling
error_reporting(0);
ini_set('display_errors', 0);

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

try {
    // Simple response function
    function sendResponse($success, $data = null, $message = 'Success') {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data
        ];
        echo json_encode($response);
        exit;
    }

    // Test 1: Basic functionality
    if (isset($_GET['test']) && $_GET['test'] === 'basic') {
        sendResponse(true, ['test' => 'basic'], 'Basic test successful');
    }

    // Test 2: Database connection
    if (isset($_GET['test']) && $_GET['test'] === 'db') {
        require_once __DIR__ . '/../shared/config.php';
        require_once __DIR__ . '/../shared/database.php';
        
        Config::init();
        
        // Try to connect to realma database directly
        $dbPath = __DIR__ . '/../data/tenants/realma.db';
        if (!file_exists($dbPath)) {
            sendResponse(false, null, 'Realma database file not found');
        }
        
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM categories WHERE is_active = 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        sendResponse(true, ['count' => $result['count']], 'Database test successful');
    }

    // Test 3: Full categories
    if (isset($_GET['test']) && $_GET['test'] === 'full') {
        require_once __DIR__ . '/../shared/config.php';
        require_once __DIR__ . '/../shared/database.php';
        
        Config::init();
        
        // Direct database access
        $dbPath = __DIR__ . '/../data/tenants/realma.db';
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $stmt = $pdo->query("
            SELECT c.*, COUNT(s.id) as service_count
            FROM categories c
            LEFT JOIN services s ON c.id = s.category_id AND s.is_active = 1
            WHERE c.is_active = 1
            GROUP BY c.id
            ORDER BY c.sort_order, c.name
        ");
        
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        sendResponse(true, $categories, 'Full categories test successful');
    }

    // Default: Try normal API
    require_once __DIR__ . '/../shared/config.php';
    require_once __DIR__ . '/../shared/database.php';
    require_once __DIR__ . '/../shared/tenant_manager.php';
    require_once __DIR__ . '/../shared/functions.php';
    require_once __DIR__ . '/../shared/translation.php';
    require_once __DIR__ . '/../store-admin/core/Application.php';

    // Initialize systems
    Config::init();
    TenantManager::forceRedetect();
    Translation::init();
    Application::init();

    $db = TenantManager::getDatabase();
    
    $categories = $db->fetchAll(
        "SELECT c.*, COUNT(s.id) as service_count
         FROM categories c
         LEFT JOIN services s ON c.id = s.category_id AND s.is_active = 1
         WHERE c.is_active = 1
         GROUP BY c.id
         ORDER BY c.sort_order, c.name"
    );

    // Add translated names to each category
    foreach ($categories as &$category) {
        $nameKey = "category_name_{$category['id']}";
        $descKey = "category_description_{$category['id']}";
        
        // Get translated name (fallback to original if translation doesn't exist)
        $translatedName = Translation::t($nameKey, $category['name'], 'categories');
        $translatedDesc = Translation::t($descKey, $category['description'] ?? '', 'categories');
        
        // Add translated fields
        $category['translated_name'] = $translatedName;
        $category['translated_description'] = $translatedDesc;
        
        // For backward compatibility, also update the main name field
        $category['name'] = $translatedName;
        if (!empty($translatedDesc)) {
            $category['description'] = $translatedDesc;
        }
    }

    sendResponse(true, $categories);

} catch (Exception $e) {
    sendResponse(false, null, 'Error: ' . $e->getMessage());
} catch (Error $e) {
    sendResponse(false, null, 'Fatal Error: ' . $e->getMessage());
}

?>
