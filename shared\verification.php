<?php

/**
 * Verification System
 * Handles verification code generation, storage, and validation for email/SMS
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/smtp.php';
require_once __DIR__ . '/sms_service.php';

class VerificationManager
{
    private Database $db;
    private SMTPMailer $mailer;

    /**
     * Initialize verification manager with database connection
     */
    public function __construct(Database $db)
    {
        $this->db = $db;
        $this->mailer = new SMTPMailer();
    }
    
    /**
     * Generate and store verification code
     */
    public function generateCode(string $type, string $identifier): string
    {
        $code = $this->generateRandomCode();
        $expires = date('Y-m-d H:i:s', time() + Config::VERIFICATION_CODE_EXPIRY);

        // Store verification code
        $this->db->query(
            "INSERT INTO verification_codes (type, identifier, code, expires_at, created_at)
             VALUES (:type, :identifier, :code, :expires, :created)",
            [
                ':type' => $type,
                ':identifier' => $identifier,
                ':code' => $code,
                ':expires' => $expires,
                ':created' => date('Y-m-d H:i:s')
            ]
        );

        return $code;
    }
    
    public function verifyCode(string $type, string $identifier, string $code): bool
    {
        $verification = $this->db->fetchRow(
            "SELECT * FROM verification_codes 
             WHERE type = :type AND identifier = :identifier AND code = :code 
             AND expires_at > :now AND used_at IS NULL
             ORDER BY created_at DESC LIMIT 1",
            [
                ':type' => $type,
                ':identifier' => $identifier,
                ':code' => $code,
                ':now' => date('Y-m-d H:i:s')
            ]
        );
        
        if (!$verification) {
            return false;
        }
        
        // Mark as used
        $this->db->query(
            "UPDATE verification_codes SET used_at = :used WHERE id = :id",
            [
                ':used' => date('Y-m-d H:i:s'),
                ':id' => $verification['id']
            ]
        );
        
        return true;
    }
    
    public function sendEmailVerification(string $email, string $businessName = ''): bool
    {
        $code = $this->generateCode('email', $email);
        return $this->mailer->sendVerificationCode($email, $code, $businessName);
    }
    
    public function sendSMSVerification(string $phone, string $businessName = ''): bool
    {
        $code = $this->generateCode('sms', $phone);
        return $this->sendSMSCode($phone, $code, $businessName);
    }

    /**
     * Send verification based on tenant's verification method setting
     */
    public function sendVerificationByMethod(string $email, string $phone = '', string $businessName = ''): array
    {
        // Get verification method setting
        $verificationMethod = $this->db->fetchColumn(
            "SELECT value FROM settings WHERE key = 'verification_method'",
            []
        ) ?: 'email';

        $result = [
            'method' => $verificationMethod,
            'success' => false,
            'message' => ''
        ];

        switch ($verificationMethod) {
            case 'none':
                $result['success'] = true;
                $result['message'] = 'No verification required';
                break;

            case 'email':
                $result['success'] = $this->sendEmailVerification($email, $businessName);
                $result['message'] = $result['success'] ? 'Verification code sent to email' : 'Failed to send email verification';
                break;

            case 'sms':
                if (empty($phone)) {
                    $result['message'] = 'Phone number required for SMS verification';
                } else {
                    $result['success'] = $this->sendSMSVerification($phone, $businessName);
                    $result['message'] = $result['success'] ? 'Verification code sent to phone' : 'Failed to send SMS verification';
                }
                break;

            default:
                $result['message'] = 'Invalid verification method';
        }

        return $result;
    }

    /**
     * Send SMS verification code using configured SMS provider
     */
    private function sendSMSCode(string $phone, string $code, string $businessName = ''): bool
    {
        try {
            // Prepare message
            $message = "Your verification code for " . ($businessName ?: 'booking') . " is: " . $code;

            // Use SMS service to send
            $smsService = new SMSService($this->db);
            return $smsService->sendSMS($phone, $message);

        } catch (Exception $e) {
            error_log("SMS verification failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function cleanupExpired(): int
    {
        $result = $this->db->query(
            "DELETE FROM verification_codes WHERE expires_at < :now",
            [':now' => date('Y-m-d H:i:s')]
        );
        
        return $result->rowCount();
    }
    
    public static function periodicCleanup(): void
    {
        // Clean up expired codes across all tenants
        $tenants = TenantManager::listTenants();
        
        foreach ($tenants as $tenant) {
            try {
                $db = Database::getInstance($tenant['name']);
                $verification = new VerificationManager($db);
                $verification->cleanupExpired();
            } catch (Exception $e) {
                error_log("Failed to cleanup verification codes for tenant {$tenant['name']}: " . $e->getMessage());
            }
        }
    }
    
    private function generateRandomCode(): string
    {
        $length = Config::VERIFICATION_CODE_LENGTH;
        $characters = '0123456789';
        $code = '';
        
        for ($i = 0; $i < $length; $i++) {
            $code .= $characters[random_int(0, strlen($characters) - 1)];
        }
        
        return $code;
    }
    
    public function getVerificationStatus(string $type, string $identifier): ?array
    {
        return $this->db->fetchRow(
            "SELECT * FROM verification_codes 
             WHERE type = :type AND identifier = :identifier 
             AND expires_at > :now AND used_at IS NULL
             ORDER BY created_at DESC LIMIT 1",
            [
                ':type' => $type,
                ':identifier' => $identifier,
                ':now' => date('Y-m-d H:i:s')
            ]
        );
    }
}
