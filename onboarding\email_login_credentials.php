<?php
function sendLoginCredentialsEmail($registration_data, $credentials) {
    $to = $registration_data['email'];
    $subject = "Welcome to GK Radevou - Your Account is Ready!";
    
    // Email template
    $html_body = generateWelcomeEmailTemplate($registration_data, $credentials);
    $text_body = generateWelcomeEmailText($registration_data, $credentials);
    
    // Email headers
    $headers = [
        'From: GK Radevou <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'Content-Type: text/html; charset=UTF-8',
        'X-Mailer: PHP/' . phpversion()
    ];
    
    // Send email (in production, use proper email service)
    if (function_exists('mail')) {
        $result = mail($to, $subject, $html_body, implode("\r\n", $headers));
    } else {
        // Fallback for development - save to file
        saveEmailToFile($to, $subject, $html_body, $registration_data);
        $result = true;
    }
    
    // Log email send attempt
    logEmailSend($to, $subject, $result);
    
    return $result;
}

function generateWelcomeEmailTemplate($data, $credentials) {
    $business_name = htmlspecialchars($data['business_name']);
    $owner_name = htmlspecialchars($data['owner_name']);
    $username = htmlspecialchars($credentials['username']);
    $password = htmlspecialchars($credentials['password']);
    $login_url = htmlspecialchars($credentials['login_url']);
    $setup_url = htmlspecialchars($credentials['setup_url']);
    
    return "
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <meta name='viewport' content='width=device-width, initial-scale=1.0'>
        <title>Welcome to GK Radevou</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }
            .email-container {
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }
            .email-header {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 2rem;
                text-align: center;
            }
            .email-header h1 {
                margin: 0;
                font-size: 2rem;
            }
            .email-body {
                padding: 2rem;
            }
            .welcome-message {
                font-size: 1.1rem;
                margin-bottom: 2rem;
            }
            .credentials-box {
                background: #f8f9fa;
                border: 2px solid #007bff;
                border-radius: 8px;
                padding: 1.5rem;
                margin: 2rem 0;
            }
            .credentials-box h3 {
                color: #007bff;
                margin-top: 0;
            }
            .credential-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 1rem;
                padding: 0.5rem;
                background: white;
                border-radius: 4px;
            }
            .credential-item strong {
                color: #333;
            }
            .credential-value {
                font-family: monospace;
                background: #e9ecef;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.9rem;
            }
            .action-buttons {
                text-align: center;
                margin: 2rem 0;
            }
            .btn {
                display: inline-block;
                padding: 12px 24px;
                margin: 0 10px;
                text-decoration: none;
                border-radius: 6px;
                font-weight: 600;
                transition: all 0.3s;
            }
            .btn-primary {
                background: #007bff;
                color: white;
            }
            .btn-secondary {
                background: white;
                color: #007bff;
                border: 2px solid #007bff;
            }
            .next-steps {
                background: #e8f5e8;
                padding: 1.5rem;
                border-radius: 8px;
                margin: 2rem 0;
            }
            .next-steps h3 {
                color: #155724;
                margin-top: 0;
            }
            .next-steps ul {
                margin: 1rem 0;
            }
            .next-steps li {
                margin-bottom: 0.5rem;
            }
            .support-info {
                background: #fff3cd;
                padding: 1.5rem;
                border-radius: 8px;
                margin: 2rem 0;
            }
            .support-info h3 {
                color: #856404;
                margin-top: 0;
            }
            .email-footer {
                background: #333;
                color: white;
                padding: 2rem;
                text-align: center;
                font-size: 0.9rem;
            }
            .email-footer a {
                color: #007bff;
                text-decoration: none;
            }
            .security-notice {
                background: #f8d7da;
                color: #721c24;
                padding: 1rem;
                border-radius: 4px;
                margin: 1rem 0;
                font-size: 0.9rem;
            }
            @media (max-width: 600px) {
                .email-body {
                    padding: 1rem;
                }
                .btn {
                    display: block;
                    margin: 0.5rem 0;
                }
            }
        </style>
    </head>
    <body>
        <div class='email-container'>
            <div class='email-header'>
                <h1>🎉 Welcome to GK Radevou!</h1>
                <p>Your business management platform is ready</p>
            </div>
            
            <div class='email-body'>
                <div class='welcome-message'>
                    <p>Dear $owner_name,</p>
                    <p>Congratulations! Your GK Radevou account for <strong>$business_name</strong> has been successfully created and is ready to use.</p>
                    <p>Your <strong>14-day free trial</strong> has started, giving you full access to all features without any charges.</p>
                </div>
                
                <div class='credentials-box'>
                    <h3>🔐 Your Login Credentials</h3>
                    <div class='credential-item'>
                        <strong>Username:</strong>
                        <span class='credential-value'>$username</span>
                    </div>
                    <div class='credential-item'>
                        <strong>Password:</strong>
                        <span class='credential-value'>$password</span>
                    </div>
                    <div class='credential-item'>
                        <strong>Login URL:</strong>
                        <span class='credential-value'>$login_url</span>
                    </div>
                    
                    <div class='security-notice'>
                        <strong>⚠️ Security Notice:</strong> Please change your password after your first login and store these credentials securely.
                    </div>
                </div>
                
                <div class='action-buttons'>
                    <a href='$login_url' class='btn btn-primary'>Login to Your Account</a>
                    <a href='$setup_url' class='btn btn-secondary'>Quick Setup Guide</a>
                </div>
                
                <div class='next-steps'>
                    <h3>🚀 Next Steps to Get Started</h3>
                    <ul>
                        <li><strong>Login</strong> to your account using the credentials above</li>
                        <li><strong>Complete Setup</strong> by following our quick setup wizard</li>
                        <li><strong>Add Your Services</strong> and configure your business settings</li>
                        <li><strong>Import Customer Data</strong> (if you have existing customers)</li>
                        <li><strong>Start Taking Bookings</strong> and managing your business</li>
                    </ul>
                </div>
                
                <div class='support-info'>
                    <h3>📞 Need Help?</h3>
                    <p>Our support team is here to help you get started:</p>
                    <ul>
                        <li><strong>Email:</strong> <EMAIL></li>
                        <li><strong>Phone:</strong> ******-123-4567</li>
                        <li><strong>Live Chat:</strong> Available in your dashboard</li>
                        <li><strong>Help Center:</strong> <a href='https://help.gkradevou.com'>help.gkradevou.com</a></li>
                    </ul>
                </div>
                
                <div style='text-align: center; margin: 2rem 0;'>
                    <p><strong>Your trial ends on:</strong> " . date('F j, Y', strtotime('+14 days')) . "</p>
                    <p>You can cancel anytime before then with no charges.</p>
                </div>
            </div>
            
            <div class='email-footer'>
                <p>Thank you for choosing GK Radevou!</p>
                <p>
                    <a href='https://gkradevou.com'>Visit our website</a> | 
                    <a href='https://help.gkradevou.com'>Help Center</a> | 
                    <a href='mailto:<EMAIL>'>Contact Support</a>
                </p>
                <p style='margin-top: 1rem; font-size: 0.8rem; color: #999;'>
                    © 2024 GK Radevou. All rights reserved.<br>
                    If you didn't create this account, please contact support immediately.
                </p>
            </div>
        </div>
    </body>
    </html>
    ";
}

function generateWelcomeEmailText($data, $credentials) {
    $business_name = $data['business_name'];
    $owner_name = $data['owner_name'];
    $username = $credentials['username'];
    $password = $credentials['password'];
    $login_url = $credentials['login_url'];
    
    return "
Welcome to GK Radevou!
======================

Dear $owner_name,

Congratulations! Your GK Radevou account for $business_name has been successfully created and is ready to use.

Your 14-day free trial has started, giving you full access to all features without any charges.

LOGIN CREDENTIALS:
==================
Username: $username
Password: $password
Login URL: $login_url

SECURITY NOTICE: Please change your password after your first login and store these credentials securely.

NEXT STEPS:
===========
1. Login to your account using the credentials above
2. Complete setup by following our quick setup wizard
3. Add your services and configure your business settings
4. Import customer data (if you have existing customers)
5. Start taking bookings and managing your business

NEED HELP?
==========
Our support team is here to help you get started:
- Email: <EMAIL>
- Phone: ******-123-4567
- Help Center: https://help.gkradevou.com

Your trial ends on: " . date('F j, Y', strtotime('+14 days')) . "
You can cancel anytime before then with no charges.

Thank you for choosing GK Radevou!

---
© 2024 GK Radevou. All rights reserved.
If you didn't create this account, please contact support immediately.
    ";
}

function saveEmailToFile($to, $subject, $body, $data) {
    $filename = 'emails/welcome_' . date('Y-m-d_H-i-s') . '_' . md5($to) . '.html';
    
    // Create emails directory if it doesn't exist
    if (!is_dir('emails')) {
        mkdir('emails', 0755, true);
    }
    
    // Save email to file for development/testing
    file_put_contents($filename, $body);
    
    // Also save a summary
    $summary = [
        'timestamp' => date('Y-m-d H:i:s'),
        'to' => $to,
        'subject' => $subject,
        'business_name' => $data['business_name'],
        'file' => $filename
    ];
    
    file_put_contents('emails/email_log.json', json_encode($summary) . "\n", FILE_APPEND);
}

function logEmailSend($to, $subject, $success) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'to' => $to,
        'subject' => $subject,
        'success' => $success,
        'method' => function_exists('mail') ? 'php_mail' : 'file_storage'
    ];
    
    $log_file = 'logs/email_send.log';
    
    // Create logs directory if it doesn't exist
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND);
}

function sendFollowUpEmail($email, $business_name, $days_into_trial) {
    $subject = '';
    $template = '';
    
    switch ($days_into_trial) {
        case 3:
            $subject = 'How is your GK Radevou trial going?';
            $template = generateFollowUpDay3Template($business_name);
            break;
        case 7:
            $subject = 'One week left in your trial - Need help?';
            $template = generateFollowUpDay7Template($business_name);
            break;
        case 12:
            $subject = 'Your trial expires in 2 days - Continue with GK Radevou?';
            $template = generateFollowUpDay12Template($business_name);
            break;
        default:
            return false;
    }
    
    $headers = [
        'From: GK Radevou <<EMAIL>>',
        'Reply-To: <EMAIL>',
        'Content-Type: text/html; charset=UTF-8'
    ];
    
    if (function_exists('mail')) {
        return mail($email, $subject, $template, implode("\r\n", $headers));
    } else {
        saveEmailToFile($email, $subject, $template, ['business_name' => $business_name]);
        return true;
    }
}

function generateFollowUpDay3Template($business_name) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>How is your trial going?</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 2rem; text-align: center; border-radius: 8px 8px 0 0; }
            .body { background: white; padding: 2rem; border: 1px solid #ddd; }
            .btn { display: inline-block; background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h1>How is your trial going?</h1>
        </div>
        <div class='body'>
            <p>Hi there,</p>
            <p>It's been 3 days since you started your GK Radevou trial for <strong>$business_name</strong>. We hope you're finding the platform helpful!</p>
            <p>Here are some tips to make the most of your trial:</p>
            <ul>
                <li>Set up your services and pricing</li>
                <li>Import your existing customer data</li>
                <li>Try creating a few test bookings</li>
                <li>Explore the reporting features</li>
            </ul>
            <p>Need help? Our support team is standing by!</p>
            <a href='https://help.gkradevou.com' class='btn'>Get Help</a>
            <a href='https://app.gkradevou.com' class='btn'>Continue Setup</a>
        </div>
    </body>
    </html>
    ";
}

function generateFollowUpDay7Template($business_name) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>One week left in your trial</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 2rem; text-align: center; border-radius: 8px 8px 0 0; }
            .body { background: white; padding: 2rem; border: 1px solid #ddd; }
            .btn { display: inline-block; background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h1>One week left in your trial</h1>
        </div>
        <div class='body'>
            <p>Hi there,</p>
            <p>You have one week left in your GK Radevou trial for <strong>$business_name</strong>.</p>
            <p>Have you had a chance to explore all the features? If not, here's what you might have missed:</p>
            <ul>
                <li>Advanced reporting and analytics</li>
                <li>Customer communication tools</li>
                <li>Inventory management</li>
                <li>Marketing automation</li>
            </ul>
            <p>Questions? Book a free consultation with our team!</p>
            <a href='mailto:<EMAIL>' class='btn'>Book Consultation</a>
            <a href='https://app.gkradevou.com' class='btn'>Continue Trial</a>
        </div>
    </body>
    </html>
    ";
}

function generateFollowUpDay12Template($business_name) {
    return "
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset='UTF-8'>
        <title>Your trial expires in 2 days</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 2rem; text-align: center; border-radius: 8px 8px 0 0; }
            .body { background: white; padding: 2rem; border: 1px solid #ddd; }
            .btn { display: inline-block; background: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h1>Your trial expires in 2 days</h1>
        </div>
        <div class='body'>
            <p>Hi there,</p>
            <p>Your GK Radevou trial for <strong>$business_name</strong> expires in just 2 days.</p>
            <p>Don't lose access to your data and all the progress you've made. Continue with GK Radevou to keep:</p>
            <ul>
                <li>All your customer data</li>
                <li>Your booking history</li>
                <li>Your customized settings</li>
                <li>Your business insights</li>
            </ul>
            <p>Ready to continue? Choose your plan and keep your business running smoothly.</p>
            <a href='https://app.gkradevou.com/billing' class='btn'>Choose Plan</a>
            <a href='mailto:<EMAIL>' class='btn'>Contact Support</a>
        </div>
    </body>
    </html>
    ";
}

// Function to be called from cron job or scheduled task
function processTrialFollowUps() {
    $tenants_file = 'tenants.json';
    
    if (!file_exists($tenants_file)) {
        return;
    }
    
    $tenants = file($tenants_file, FILE_IGNORE_NEW_LINES);
    
    foreach ($tenants as $tenant_line) {
        $tenant = json_decode($tenant_line, true);
        
        if (!$tenant || $tenant['status'] !== 'active') {
            continue;
        }
        
        $created_date = new DateTime($tenant['created_at']);
        $now = new DateTime();
        $days_diff = $now->diff($created_date)->days;
        
        // Send follow-up emails at specific intervals
        if (in_array($days_diff, [3, 7, 12])) {
            sendFollowUpEmail($tenant['email'], $tenant['business_name'], $days_diff);
        }
    }
}

// Manual testing function
function testWelcomeEmail() {
    $test_data = [
        'business_name' => 'Test Salon & Spa',
        'business_type' => 'salon',
        'owner_name' => 'John Doe',
        'email' => '<EMAIL>',
        'phone' => '******-123-4567',
        'address' => '123 Main St',
        'city' => 'Anytown',
        'state' => 'CA',
        'zip' => '12345',
        'country' => 'US',
        'plan' => 'professional'
    ];
    
    $test_credentials = [
        'username' => 'test_salon_spa',
        'password' => 'SecurePass123!',
        'email' => '<EMAIL>',
        'login_url' => 'https://app.gkradevou.com/login',
        'setup_url' => 'https://app.gkradevou.com/setup'
    ];
    
    return sendLoginCredentialsEmail($test_data, $test_credentials);
}

// If called directly, run test
if (basename(__FILE__) == basename($_SERVER['PHP_SELF'])) {
    echo "Testing welcome email...\n";
    $result = testWelcomeEmail();
    echo $result ? "Email sent successfully!" : "Email failed to send.";
    echo "\n";
}
?>
