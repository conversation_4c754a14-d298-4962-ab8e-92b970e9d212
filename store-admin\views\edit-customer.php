<?php

/**
 * Edit Customer Page
 * Dedicated page for editing existing customers
 */

// Get customer ID
$customerId = $_GET['id'] ?? '';
if (!$customerId) {
    Application::redirect('/store-admin/?page=customers', 'Customer ID is required', 'error');
}

// Get customer data
$customer = $db->fetchRow("SELECT * FROM customers WHERE id = :id", [':id' => $customerId]);
if (!$customer) {
    Application::redirect('/store-admin/?page=customers', 'Customer not found', 'error');
}

// Handle form submission
if ($_POST && !isset($_POST['ajax'])) {
    $_POST['id'] = $customerId; // Ensure ID is set
    require_once __DIR__ . '/../controllers/customers.php';
    $result = handleCustomersForm($_POST, $db);
    if ($result['success']) {
        Application::redirect('/store-admin/?page=customers', $result['message'], 'success');
    } else {
        $error = $result['error'];
    }
}
?>

<div class="page-header">
    <div class="page-header-left">
        <h1 class="page-title">Edit Customer</h1>
        <div class="breadcrumb">
            <a href="/store-admin/?page=customers">Customers</a>
            <span class="breadcrumb-separator">/</span>
            <span><?php echo htmlspecialchars($customer['name']); ?></span>
        </div>
    </div>
    <div class="page-header-right">
        <a href="/store-admin/?page=customers" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Customers
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" class="entity-form">
        <input type="hidden" name="action" value="edit">
        <input type="hidden" name="id" value="<?php echo $customer['id']; ?>">
        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

        <div class="form-section">
            <h3 class="form-section-title">Customer Information</h3>

            <div class="form-group">
                <label for="name">Customer Name *</label>
                <input type="text" id="name" name="name" required
                    value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : htmlspecialchars($customer['name']); ?>"
                    placeholder="Enter customer's full name">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email"
                        value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : htmlspecialchars($customer['email']); ?>"
                        placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="phone">Phone</label>
                    <input type="tel" id="phone" name="phone"
                        value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : htmlspecialchars($customer['phone']); ?>"
                        placeholder="69XXXXXXXX">
                </div>
            </div>

            <div class="form-group">
                <label for="language">Preferred Language</label>
                <select id="language" name="language">
                    <?php
                    $selectedLanguage = isset($_POST['language']) ? $_POST['language'] : $customer['language'];
                    ?>
                    <option value="el" <?php echo ($selectedLanguage === 'el') ? 'selected' : ''; ?>>
                        Greek
                    </option>
                    <option value="en" <?php echo ($selectedLanguage === 'en') ? 'selected' : ''; ?>>
                        English
                    </option>
                </select>
            </div>

            <div class="form-group">
                <label for="notes">Notes</label>
                <textarea id="notes" name="notes" rows="4"
                    placeholder="Any special notes about this customer..."><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : htmlspecialchars($customer['notes']); ?></textarea>
            </div>
        </div>



        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Customer
            </button>
            <a href="/store-admin/?page=customers" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .page-header-left h1 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
    }

    .breadcrumb {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .breadcrumb a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
    }

    .content-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        margin: 0 0 1.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        font-size: 1.125rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .stat-item {
        text-align: center;
        padding: 1.5rem;
        background: var(--light-color);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }

    .stat-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .stat-label {
        display: block;
        font-size: 0.875rem;
        color: var(--text-muted);
        margin-top: 0.5rem;
    }

    .customer-dates {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .date-item {
        padding: 0.75rem 1rem;
        background: var(--light-color);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    .action-buttons {
        display: flex;
        gap: 1rem;
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }

    .alert {
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .alert-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            flex-direction: column;
        }

        .form-actions {
            flex-direction: column;
        }

        .content-card {
            padding: 1rem;
        }
    }
</style>