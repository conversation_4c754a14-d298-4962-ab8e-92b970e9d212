<?php
/**
 * Clean Duplicate Translations Script
 * Removes cross-category duplicates and consolidates translations
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

function cleanDuplicateTranslations(): void {
    echo "🧹 Cleaning Duplicate Translations\n";
    echo str_repeat("=", 60) . "\n";

    try {
        Config::init();
        TenantManager::init();
        Translation::init();
        $db = TenantManager::getDatabase();

        // Find cross-category duplicates
        $duplicatesByKey = $db->fetchAll("
            SELECT key, COUNT(*) as count
            FROM translations
            GROUP BY key
            HAVING COUNT(*) > 1
            ORDER BY key
        ");

        if (empty($duplicatesByKey)) {
            echo "✅ No cross-category duplicates found\n";
            return;
        }

        echo "Found " . count($duplicatesByKey) . " keys with cross-category duplicates\n\n";

        $cleanupActions = [];
        $totalRemoved = 0;

        foreach ($duplicatesByKey as $dup) {
            $key = $dup['key'];
            echo "🔍 Processing key: '{$key}'\n";

            // Get all entries for this key
            $entries = $db->fetchAll("
                SELECT id, key, category, value_el, value_en, created_at, updated_at
                FROM translations
                WHERE key = :key
                ORDER BY 
                    CASE 
                        WHEN category = 'ui' THEN 1
                        WHEN category = 'user_interface' THEN 2
                        WHEN category = 'client' THEN 3
                        ELSE 4
                    END,
                    updated_at DESC
            ", [':key' => $key]);

            echo "   Found " . count($entries) . " entries:\n";
            foreach ($entries as $i => $entry) {
                $num = $i + 1;
                echo "     [{$num}] {$entry['category']}: EL='{$entry['value_el']}' EN='{$entry['value_en']}'\n";
            }

            // Determine the best entry to keep
            $bestEntry = null;
            $entriesToRemove = [];

            // Strategy: Keep the entry with the most complete translation
            foreach ($entries as $entry) {
                $score = 0;
                
                // Prefer entries with both Greek and English
                if (!empty($entry['value_el'])) $score += 2;
                if (!empty($entry['value_en'])) $score += 2;
                
                // Prefer 'ui' category over 'user_interface'
                if ($entry['category'] === 'ui') $score += 1;
                
                // Prefer more recent updates
                $score += strtotime($entry['updated_at']) / 1000000; // Small boost for newer entries

                $entry['score'] = $score;
                
                if ($bestEntry === null || $score > $bestEntry['score']) {
                    if ($bestEntry !== null) {
                        $entriesToRemove[] = $bestEntry;
                    }
                    $bestEntry = $entry;
                } else {
                    $entriesToRemove[] = $entry;
                }
            }

            echo "   ✅ Keeping: [{$bestEntry['category']}] EL='{$bestEntry['value_el']}' EN='{$bestEntry['value_en']}'\n";
            
            foreach ($entriesToRemove as $entry) {
                echo "   ❌ Removing: [{$entry['category']}] EL='{$entry['value_el']}' EN='{$entry['value_en']}'\n";
                
                // Remove the duplicate entry
                $db->query("DELETE FROM translations WHERE id = :id", [':id' => $entry['id']]);
                $totalRemoved++;
            }

            // If the best entry is not in 'ui' category, move it there for consistency
            if ($bestEntry['category'] !== 'ui') {
                echo "   🔄 Moving best entry to 'ui' category\n";
                $db->query("
                    UPDATE translations 
                    SET category = 'ui', updated_at = :updated_at
                    WHERE id = :id
                ", [
                    ':id' => $bestEntry['id'],
                    ':updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            echo "\n";
        }

        echo "📊 Cleanup Summary\n";
        echo str_repeat("=", 60) . "\n";
        echo "- Keys processed: " . count($duplicatesByKey) . "\n";
        echo "- Duplicate entries removed: {$totalRemoved}\n";
        echo "- Remaining translations: " . ($db->fetchColumn("SELECT COUNT(*) FROM translations")) . "\n";

        // Clear translation cache
        Translation::clearCache();
        echo "- Translation cache cleared\n";

        echo "\n✅ Duplicate cleanup completed!\n";
        echo "All cross-category duplicates have been resolved.\n";

    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Specific cleanup for known duplicates
function cleanSpecificDuplicates(): void {
    echo "🎯 Cleaning Specific Known Duplicates\n";
    echo str_repeat("=", 60) . "\n";

    try {
        Config::init();
        TenantManager::init();
        Translation::init();
        $db = TenantManager::getDatabase();

        $knownDuplicates = [
            'next' => [
                'keep_category' => 'ui',
                'preferred_el' => 'Επόμενο',
                'preferred_en' => 'Next'
            ],
            'previous' => [
                'keep_category' => 'ui', 
                'preferred_el' => 'Προηγούμενο',
                'preferred_en' => 'Previous'
            ]
        ];

        foreach ($knownDuplicates as $key => $config) {
            echo "🔧 Fixing '{$key}'...\n";

            // Get all entries for this key
            $entries = $db->fetchAll("
                SELECT id, category, value_el, value_en
                FROM translations
                WHERE key = :key
            ", [':key' => $key]);

            if (count($entries) <= 1) {
                echo "   ✅ No duplicates found for '{$key}'\n";
                continue;
            }

            // Remove all entries
            $db->query("DELETE FROM translations WHERE key = :key", [':key' => $key]);
            echo "   🗑️  Removed " . count($entries) . " duplicate entries\n";

            // Add single consolidated entry
            $id = 'TR' . strtoupper(substr(uniqid(), -8));
            $db->query("
                INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
            ", [
                ':id' => $id,
                ':key' => $key,
                ':value_el' => $config['preferred_el'],
                ':value_en' => $config['preferred_en'],
                ':category' => $config['keep_category'],
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]);

            echo "   ✅ Added consolidated entry: [{$config['keep_category']}] EL='{$config['preferred_el']}' EN='{$config['preferred_en']}'\n";
        }

        // Clear cache
        Translation::clearCache();
        echo "\n✅ Specific duplicates cleaned up!\n";

    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
}

// Run the cleanup
echo "🚀 Starting Translation Cleanup\n";
echo "================================\n\n";

// First, run specific cleanup for known issues
cleanSpecificDuplicates();

echo "\n" . str_repeat("-", 60) . "\n\n";

// Then run general cleanup
cleanDuplicateTranslations();

echo "\n🎉 All translation duplicates have been cleaned up!\n";
?>
