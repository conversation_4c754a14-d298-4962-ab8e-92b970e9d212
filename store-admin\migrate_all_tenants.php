<?php
/**
 * Migrate All Tenants
 * Creates bulk action tables in all existing tenant databases
 */

require_once __DIR__ . '/core/Application.php';

// Initialize application
Application::init();

try {
    echo "🔄 Migrating Bulk Action Tables to All Tenants\n";
    echo "===============================================\n\n";
    
    // Get all tenant database files
    $tenantsDir = __DIR__ . '/../data/tenants/';
    $tenantFiles = glob($tenantsDir . '*.db');
    
    if (empty($tenantFiles)) {
        echo "❌ No tenant databases found in $tenantsDir\n";
        exit(1);
    }
    
    echo "Found " . count($tenantFiles) . " tenant database(s):\n";
    foreach ($tenantFiles as $file) {
        $tenantName = basename($file, '.db');
        echo "  - $tenantName\n";
    }
    echo "\n";
    
    // Define the tables to create
    $tables = [
        'email_log' => "CREATE TABLE IF NOT EXISTS email_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id VARCHAR(50),
            subject VARCHAR(255),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        'sms_log' => "CREATE TABLE IF NOT EXISTS sms_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id VARCHAR(50),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        'employee_notifications' => "CREATE TABLE IF NOT EXISTS employee_notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id VARCHAR(50),
            type VARCHAR(50),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        'reminder_log' => "CREATE TABLE IF NOT EXISTS reminder_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reservation_id VARCHAR(50),
            customer_id VARCHAR(50),
            type VARCHAR(20),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )"
    ];
    
    // Create indexes
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_email_log_customer_id ON email_log(customer_id)",
        "CREATE INDEX IF NOT EXISTS idx_email_log_sent_at ON email_log(sent_at)",
        "CREATE INDEX IF NOT EXISTS idx_sms_log_customer_id ON sms_log(customer_id)",
        "CREATE INDEX IF NOT EXISTS idx_sms_log_sent_at ON sms_log(sent_at)",
        "CREATE INDEX IF NOT EXISTS idx_employee_notifications_employee_id ON employee_notifications(employee_id)",
        "CREATE INDEX IF NOT EXISTS idx_employee_notifications_sent_at ON employee_notifications(sent_at)",
        "CREATE INDEX IF NOT EXISTS idx_reminder_log_reservation_id ON reminder_log(reservation_id)",
        "CREATE INDEX IF NOT EXISTS idx_reminder_log_customer_id ON reminder_log(customer_id)",
        "CREATE INDEX IF NOT EXISTS idx_reminder_log_sent_at ON reminder_log(sent_at)"
    ];
    
    $totalSuccess = 0;
    $totalErrors = 0;
    
    // Process each tenant database
    foreach ($tenantFiles as $file) {
        $tenantName = basename($file, '.db');
        echo "Processing tenant: $tenantName\n";
        
        try {
            // Get database instance for this tenant
            $db = Database::getInstance($tenantName);
            
            // Create tables
            foreach ($tables as $tableName => $sql) {
                try {
                    $result = $db->query($sql);
                    if ($result !== false) {
                        echo "  ✓ Table $tableName created/verified\n";
                        $totalSuccess++;
                    } else {
                        echo "  ✗ Failed to create table $tableName\n";
                        $totalErrors++;
                    }
                } catch (Exception $e) {
                    echo "  ✗ Error creating table $tableName: " . $e->getMessage() . "\n";
                    $totalErrors++;
                }
            }
            
            // Create indexes
            foreach ($indexes as $sql) {
                try {
                    $result = $db->query($sql);
                    if ($result !== false) {
                        $totalSuccess++;
                    } else {
                        $totalErrors++;
                    }
                } catch (Exception $e) {
                    $totalErrors++;
                }
            }
            
            // Add schedule column to employees table if it doesn't exist
            try {
                $columns = $db->fetchAll("PRAGMA table_info(employees)");
                $hasScheduleColumn = false;
                foreach ($columns as $column) {
                    if ($column['name'] === 'schedule') {
                        $hasScheduleColumn = true;
                        break;
                    }
                }
                
                if (!$hasScheduleColumn) {
                    $result = $db->query("ALTER TABLE employees ADD COLUMN schedule TEXT");
                    if ($result !== false) {
                        echo "  ✓ Added schedule column to employees table\n";
                        $totalSuccess++;
                    } else {
                        echo "  ✗ Failed to add schedule column\n";
                        $totalErrors++;
                    }
                } else {
                    echo "  ✓ Schedule column already exists\n";
                }
            } catch (Exception $e) {
                echo "  ✗ Error adding schedule column: " . $e->getMessage() . "\n";
                $totalErrors++;
            }
            
            echo "  ✅ Tenant $tenantName migration complete\n\n";
            
        } catch (Exception $e) {
            echo "  ❌ Error processing tenant $tenantName: " . $e->getMessage() . "\n\n";
            $totalErrors++;
        }
    }
    
    echo "🎉 All Tenants Migration Complete!\n";
    echo "==================================\n";
    echo "Total successful operations: $totalSuccess\n";
    echo "Total errors: $totalErrors\n";
    
    if ($totalErrors === 0) {
        echo "\n✅ All bulk action tables have been created in all tenant databases!\n";
        echo "The bulk actions should now work correctly regardless of tenant context.\n";
    } else {
        echo "\n⚠️  Some operations failed. Please check the errors above.\n";
    }
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
