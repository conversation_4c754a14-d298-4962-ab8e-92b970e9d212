<?php

/**
 * View Customer Page - Compact Design
 * Display customer details with icon-based information and improved layout
 */

// Get customer ID from URL
$customerId = $_GET['id'] ?? '';

if (!$customerId) {
    Application::redirect('/store-admin/?page=customers', 'Customer not found', 'error');
    exit;
}

// Fetch customer data
try {
    $customer = $db->fetchRow("
        SELECT c.*, 
               COUNT(r.id) as total_reservations,
               COUNT(CASE WHEN r.status = 'completed' THEN 1 END) as completed_reservations,
               MAX(r.date) as last_visit_date,
               SUM(CASE WHEN r.status = 'completed' THEN s.price ELSE 0 END) as total_spent
        FROM customers c
        LEFT JOIN reservations r ON c.id = r.customer_id
        LEFT JOIN services s ON r.service_id = s.id
        WHERE c.id = :id
        GROUP BY c.id
    ", [':id' => $customerId]);

    if (!$customer) {
        Application::redirect('/store-admin/?page=customers', 'Customer not found', 'error');
        exit;
    }

    // Get recent reservations
    $recentReservations = $db->fetchAll("
        SELECT r.*, s.name as service_name, e.name as employee_name
        FROM reservations r
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN employees e ON r.employee_id = e.id
        WHERE r.customer_id = :id
        ORDER BY r.date DESC, r.start_time DESC
        LIMIT 10
    ", [':id' => $customerId]);
} catch (Exception $e) {
    Application::redirect('/store-admin/?page=customers', 'Error loading customer data', 'error');
    exit;
}
?>

<div class="view-page">
    <!-- Header -->
    <div class="view-header">
        <div class="view-header-content">
            <div class="view-breadcrumb">
                <a href="/store-admin/?page=customers" class="breadcrumb-link">
                    <i class="fas fa-users"></i> Customers
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current"><?= htmlspecialchars($customer['name']) ?></span>
            </div>

            <div class="view-title">
                <div class="title-content">
                    <h1><?= htmlspecialchars($customer['name']) ?></h1>
                    <div class="view-subtitle">
                        <span class="date-info">Customer since <?= date('M j, Y', strtotime($customer['created_at'])) ?></span>
                        <span class="status-badge status-active">
                            <i class="fas fa-check-circle"></i> Active Customer
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="view-actions">
            <div class="dropdown">
                <button class="btn btn-outline dropdown-toggle" type="button">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu">
                    <a href="/store-admin/?page=edit-customer&id=<?= htmlspecialchars($customerId) ?>" class="dropdown-item">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                    <a href="/store-admin/?page=add-reservation&customer_id=<?= htmlspecialchars($customerId) ?>" class="dropdown-item">
                        <i class="fas fa-calendar-plus"></i> Book Appointment
                    </a>
                    <a href="/store-admin/?page=reservations&customer_id=<?= htmlspecialchars($customerId) ?>" class="dropdown-item">
                        <i class="fas fa-history"></i> View History
                    </a>
                    <button onclick="sendMessage('<?= htmlspecialchars($customerId) ?>')" class="dropdown-item">
                        <i class="fas fa-message"></i> Send Message
                    </button>
                    <button onclick="sendEmail('<?= htmlspecialchars($customerId) ?>')" class="dropdown-item">
                        <i class="fas fa-envelope"></i> Send Email
                    </button>
                    <button onclick="sendSMS('<?= htmlspecialchars($customerId) ?>')" class="dropdown-item">
                        <i class="fas fa-sms"></i> Send SMS
                    </button>
                    <button onclick="sendReminder('<?= htmlspecialchars($customerId) ?>')" class="dropdown-item">
                        <i class="fas fa-bell"></i> Send Reminder
                    </button>
                    <a href="/store-admin/?page=customers&action=duplicate&id=<?= htmlspecialchars($customerId) ?>" class="dropdown-item">
                        <i class="fas fa-copy"></i> Duplicate Customer
                    </a>
                    <div class="dropdown-divider"></div>
                    <button onclick="deleteCustomer('<?= htmlspecialchars($customerId) ?>')" class="dropdown-item text-danger">
                        <i class="fas fa-trash"></i> Delete Customer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="view-content">
        <div class="view-grid">
            <!-- Contact Information -->
            <div class="consolidated-info-box compact">
                <div class="compact-header">
                    <i class="fas fa-user"></i>
                    <span>Contact Information</span>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <i class="fas fa-user info-icon"></i>
                        <span class="info-label">Name:</span>
                        <span class="info-value"><?= htmlspecialchars($customer['name']) ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-language info-icon"></i>
                        <span class="info-label">Language:</span>
                        <span class="info-value"><?= $customer['language'] === 'el' ? 'Greek' : 'English' ?></span>
                    </div>
                </div>

                <?php if (!empty($customer['email']) || !empty($customer['phone'])): ?>
                    <div class="info-row">
                        <?php if (!empty($customer['email'])): ?>
                            <div class="info-item">
                                <i class="fas fa-envelope info-icon"></i>
                                <span class="info-label">Email:</span>
                                <a href="mailto:<?= htmlspecialchars($customer['email']) ?>" class="info-value contact-link">
                                    <?= htmlspecialchars($customer['email']) ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($customer['phone'])): ?>
                            <div class="info-item">
                                <i class="fas fa-phone info-icon"></i>
                                <span class="info-label">Phone:</span>
                                <a href="tel:<?= htmlspecialchars($customer['phone']) ?>" class="info-value contact-link">
                                    <?= htmlspecialchars($customer['phone']) ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($customer['notes'])): ?>
                    <div class="info-row full-width">
                        <div class="info-item">
                            <i class="fas fa-sticky-note info-icon"></i>
                            <span class="info-label">Notes:</span>
                            <div class="info-value description"><?= nl2br(htmlspecialchars($customer['notes'])) ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Statistics -->
            <div class="consolidated-info-box compact">
                <div class="compact-header">
                    <i class="fas fa-chart-bar"></i>
                    <span>Customer Statistics</span>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <i class="fas fa-calendar-check info-icon"></i>
                        <span class="info-label">Total Bookings:</span>
                        <span class="info-value"><?= $customer['total_reservations'] ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-check-circle info-icon"></i>
                        <span class="info-label">Completed:</span>
                        <span class="info-value"><?= $customer['completed_reservations'] ?></span>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <i class="fas fa-euro-sign info-icon"></i>
                        <span class="info-label">Total Spent:</span>
                        <span class="info-value price">€<?= number_format($customer['total_spent'] ?? 0, 2) ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock info-icon"></i>
                        <span class="info-label">Last Visit:</span>
                        <span class="info-value">
                            <?= $customer['last_visit_date'] ? date('M j, Y', strtotime($customer['last_visit_date'])) : 'Never' ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>



        <!-- Recent Reservations -->
        <?php if (!empty($recentReservations)): ?>
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-calendar-alt"></i> Recent Reservations</h2>
                    <a href="/store-admin/?page=reservations&customer_id=<?= htmlspecialchars($customerId) ?>" class="btn btn-outline btn-sm">
                        <i class="fas fa-external-link-alt"></i> View All
                    </a>
                </div>
                <div class="section-content">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Service</th>
                                    <th>Employee</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentReservations as $reservation): ?>
                                    <tr>
                                        <td>
                                            <div class="date-info">
                                                <strong><?= date('M j, Y', strtotime($reservation['date'])) ?></strong><br>
                                                <small><?= date('H:i', strtotime($reservation['start_time'])) ?></small>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($reservation['service_name']) ?></td>
                                        <td><?= htmlspecialchars($reservation['employee_name']) ?></td>
                                        <td>
                                            <span class="status-badge status-<?= $reservation['status'] ?>">
                                                <?= ucfirst($reservation['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="/store-admin/?page=view-reservation&id=<?= $reservation['id'] ?>"
                                                class="btn btn-sm btn-outline" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    function deleteCustomer(id) {
        if (confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
            const formData = new FormData();
            formData.append('action', 'delete_customer');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Customer deleted successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=customers';
                        }, 1000);
                    } else {
                        showNotification('Error deleting customer: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }

    function sendMessage(customerId) {
        showNotification('Message feature coming soon', 'info');
    }
</script>