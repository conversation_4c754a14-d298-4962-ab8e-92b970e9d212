<?php
/**
 * Admin Interface Translation System
 * Translates admin interface based on Default Language setting
 */

class AdminTranslation
{
    private static ?string $adminLanguage = null;
    private static array $adminTexts = [];
    
    /**
     * Initialize admin translation system
     */
    public static function init(Database $db): void
    {
        self::detectAdminLanguage($db);
        self::loadAdminTexts();
    }
    
    /**
     * Detect admin language from settings
     */
    private static function detectAdminLanguage(Database $db): void
    {
        try {
            // Check if settings table exists and has the setting
            $tableExists = $db->fetchColumn("
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='settings'
            ");

            if ($tableExists) {
                $defaultLang = $db->fetchColumn("
                    SELECT value FROM settings
                    WHERE key = 'default_language'
                ");

                // Map setting values to language codes
                if ($defaultLang === 'ελληνικά' || $defaultLang === 'el' || $defaultLang === 'greek') {
                    self::$adminLanguage = 'el';
                } else if ($defaultLang === 'english' || $defaultLang === 'en') {
                    self::$adminLanguage = 'en';
                } else {
                    // Default to Greek if setting exists but value is unclear
                    self::$adminLanguage = 'el';
                }
            } else {
                // Default to Greek if settings table doesn't exist
                self::$adminLanguage = 'el';
            }

        } catch (Exception $e) {
            // Default to Greek if any error occurs
            self::$adminLanguage = 'el';
            error_log("AdminTranslation error: " . $e->getMessage());
        }
    }
    
    /**
     * Load admin interface texts
     */
    private static function loadAdminTexts(): void
    {
        self::$adminTexts = [
            // Navigation
            'dashboard' => ['el' => 'Πίνακας Ελέγχου', 'en' => 'Dashboard'],
            'customers' => ['el' => 'Πελάτες', 'en' => 'Customers'],
            'services' => ['el' => 'Υπηρεσίες', 'en' => 'Services'],
            'employees' => ['el' => 'Προσωπικό', 'en' => 'Employees'],
            'categories' => ['el' => 'Κατηγορίες', 'en' => 'Categories'],
            'reservations' => ['el' => 'Κρατήσεις', 'en' => 'Reservations'],
            'calendar' => ['el' => 'Ημερολόγιο', 'en' => 'Calendar'],
            'management' => ['el' => 'Διαχείριση', 'en' => 'Management'],
            'settings' => ['el' => 'Ρυθμίσεις', 'en' => 'Settings'],
            'translations' => ['el' => 'Μεταφράσεις', 'en' => 'Translations'],
            'email_logs' => ['el' => 'Αρχεία Email', 'en' => 'Email Logs'],
            'logout' => ['el' => 'Αποσύνδεση', 'en' => 'Logout'],
            
            // Common buttons and actions
            'save' => ['el' => 'Αποθήκευση', 'en' => 'Save'],
            'cancel' => ['el' => 'Ακύρωση', 'en' => 'Cancel'],
            'delete' => ['el' => 'Διαγραφή', 'en' => 'Delete'],
            'edit' => ['el' => 'Επεξεργασία', 'en' => 'Edit'],
            'add' => ['el' => 'Προσθήκη', 'en' => 'Add'],
            'view' => ['el' => 'Προβολή', 'en' => 'View'],
            'search' => ['el' => 'Αναζήτηση', 'en' => 'Search'],
            'filter' => ['el' => 'Φίλτρο', 'en' => 'Filter'],
            'export' => ['el' => 'Εξαγωγή', 'en' => 'Export'],
            'import' => ['el' => 'Εισαγωγή', 'en' => 'Import'],
            'refresh' => ['el' => 'Ανανέωση', 'en' => 'Refresh'],
            'back' => ['el' => 'Πίσω', 'en' => 'Back'],
            'next' => ['el' => 'Επόμενο', 'en' => 'Next'],
            'previous' => ['el' => 'Προηγούμενο', 'en' => 'Previous'],
            'close' => ['el' => 'Κλείσιμο', 'en' => 'Close'],
            'confirm' => ['el' => 'Επιβεβαίωση', 'en' => 'Confirm'],
            'yes' => ['el' => 'Ναι', 'en' => 'Yes'],
            'no' => ['el' => 'Όχι', 'en' => 'No'],
            
            // Form fields
            'name' => ['el' => 'Όνομα', 'en' => 'Name'],
            'email' => ['el' => 'Email', 'en' => 'Email'],
            'phone' => ['el' => 'Τηλέφωνο', 'en' => 'Phone'],
            'address' => ['el' => 'Διεύθυνση', 'en' => 'Address'],
            'description' => ['el' => 'Περιγραφή', 'en' => 'Description'],
            'price' => ['el' => 'Τιμή', 'en' => 'Price'],
            'duration' => ['el' => 'Διάρκεια', 'en' => 'Duration'],
            'status' => ['el' => 'Κατάσταση', 'en' => 'Status'],
            'date' => ['el' => 'Ημερομηνία', 'en' => 'Date'],
            'time' => ['el' => 'Ώρα', 'en' => 'Time'],
            'notes' => ['el' => 'Σημειώσεις', 'en' => 'Notes'],
            'active' => ['el' => 'Ενεργό', 'en' => 'Active'],
            'inactive' => ['el' => 'Ανενεργό', 'en' => 'Inactive'],
            
            // Status messages
            'success' => ['el' => 'Επιτυχία', 'en' => 'Success'],
            'error' => ['el' => 'Σφάλμα', 'en' => 'Error'],
            'warning' => ['el' => 'Προειδοποίηση', 'en' => 'Warning'],
            'info' => ['el' => 'Πληροφορία', 'en' => 'Info'],
            'loading' => ['el' => 'Φόρτωση...', 'en' => 'Loading...'],
            'saved_successfully' => ['el' => 'Αποθηκεύτηκε επιτυχώς', 'en' => 'Saved successfully'],
            'deleted_successfully' => ['el' => 'Διαγράφηκε επιτυχώς', 'en' => 'Deleted successfully'],
            'updated_successfully' => ['el' => 'Ενημερώθηκε επιτυχώς', 'en' => 'Updated successfully'],
            'created_successfully' => ['el' => 'Δημιουργήθηκε επιτυχώς', 'en' => 'Created successfully'],
            
            // Validation messages
            'required_field' => ['el' => 'Αυτό το πεδίο είναι υποχρεωτικό', 'en' => 'This field is required'],
            'invalid_email' => ['el' => 'Μη έγκυρο email', 'en' => 'Invalid email'],
            'invalid_phone' => ['el' => 'Μη έγκυρο τηλέφωνο', 'en' => 'Invalid phone'],
            'invalid_format' => ['el' => 'Μη έγκυρη μορφή', 'en' => 'Invalid format'],
            
            // Pagination
            'showing' => ['el' => 'Εμφάνιση', 'en' => 'Showing'],
            'of' => ['el' => 'από', 'en' => 'of'],
            'entries' => ['el' => 'εγγραφές', 'en' => 'entries'],
            'no_data' => ['el' => 'Δεν υπάρχουν δεδομένα', 'en' => 'No data available'],
            
            // Confirmation dialogs
            'confirm_delete' => ['el' => 'Είστε σίγουροι ότι θέλετε να διαγράψετε;', 'en' => 'Are you sure you want to delete?'],
            'confirm_action' => ['el' => 'Είστε σίγουροι για αυτή την ενέργεια;', 'en' => 'Are you sure about this action?'],
            'cannot_be_undone' => ['el' => 'Αυτή η ενέργεια δεν μπορεί να αναιρεθεί', 'en' => 'This action cannot be undone'],

            // Translation page
            'translations' => ['el' => 'Μεταφράσεις', 'en' => 'Translations'],
            'manage_website_translations' => ['el' => 'Διαχειριστείτε τις μεταφράσεις του ιστότοπού σας στα ελληνικά και αγγλικά', 'en' => 'Manage your website translations in Greek and English'],
            'save_all_changes' => ['el' => 'Αποθήκευση Όλων των Αλλαγών', 'en' => 'Save All Changes'],
            'all_categories' => ['el' => 'Όλες οι Κατηγορίες', 'en' => 'All Categories'],
            'search_translations' => ['el' => 'Αναζήτηση μεταφράσεων...', 'en' => 'Search translations...'],
            'key' => ['el' => 'Κλειδί', 'en' => 'Key'],
            'greek' => ['el' => 'Ελληνικά', 'en' => 'Greek'],
            'english' => ['el' => 'Αγγλικά', 'en' => 'English'],
            'actions' => ['el' => 'Ενέργειες', 'en' => 'Actions'],
        ];
    }
    
    /**
     * Get admin translation
     */
    public static function at(string $key, string $fallback = ''): string
    {
        if (!isset(self::$adminTexts[$key])) {
            return $fallback ?: $key;
        }
        
        $language = self::$adminLanguage ?? 'el';
        return self::$adminTexts[$key][$language] ?? $fallback ?: $key;
    }
    
    /**
     * Get current admin language
     */
    public static function getAdminLanguage(): string
    {
        return self::$adminLanguage ?? 'el';
    }
}

/**
 * Global admin translation function
 */
function at(string $key, string $fallback = ''): string
{
    try {
        return AdminTranslation::at($key, $fallback);
    } catch (Exception $e) {
        // Fallback to key or provided fallback if translation system fails
        return $fallback ?: $key;
    }
}
