<?php

/**
 * System Core Dashboard
 * Main interface for system-wide administration and tenant management
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../shared/tenant_manager.php';

// Require system authentication
requireSystemAuth();

// Get current domain for tenant links
$currentDomain = TenantManager::getBaseDomain();

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Get system statistics
$stats = getSystemStats();
$tenants = Database::master()->fetchAll("SELECT * FROM tenants ORDER BY created_at DESC LIMIT 10");

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SYSTEM_NAME ?> - Dashboard</title>
    <link rel="stylesheet" href="assets/system.css">
</head>
<body>
    <div class="layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><?= SYSTEM_NAME ?></h1>
                <button class="sidebar-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="index.php" class="nav-link active">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="tenant_manager.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span class="nav-text">Tenant Manager</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <a href="create_tenant.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        <span class="nav-text">Create Tenant</span>
                    </a>
                    <a href="dummy_data.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        <span class="nav-text">Dummy Data</span>
                    </a>
                    <a href="clean_db.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 7h16"></path>
                            <path d="M10 11v6"></path>
                            <path d="M14 11v6"></path>
                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                            <path d="M9 7V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                        </svg>
                        <span class="nav-text">Database Tools</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="?logout=1" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Dashboard</h1>
                    <div class="breadcrumb">
                        <span>System</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Dashboard</span>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-search">
                        <input type="text" class="search-input" placeholder="Search...">
                        <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <main class="main">
                <div class="container">
                    <!-- Quick Stats -->
                    <div class="dashboard-grid">
                        <div class="card">
                            <div class="card-header">
                                <h2 class="card-title">System Overview</h2>
                            </div>
                            <div class="card-body">
                                <div class="stats-grid">
                                    <div class="stat-item">
                                        <div class="stat-value"><?= $stats['tenants_total'] ?></div>
                                        <div class="stat-label">Total Tenants</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?= $stats['tenants_active'] ?></div>
                                        <div class="stat-label">Active Tenants</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?= $stats['total_customers'] ?></div>
                                        <div class="stat-label">Total Customers</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?= $stats['total_bookings'] ?></div>
                                        <div class="stat-label">Total Bookings</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?= formatFileSize($stats['total_db_size']) ?></div>
                                        <div class="stat-label">Database Size</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?= SYSTEM_VERSION ?></div>
                                        <div class="stat-label">System Version</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Health -->
                        <div class="card">
                            <div class="card-header">
                                <h2 class="card-title">System Health</h2>
                            </div>
                            <div class="card-body">
                                <div class="health-checks">
                                    <div class="health-item">
                                        <div class="health-status <?= file_exists(Config::MASTER_DB) ? 'healthy' : 'error' ?>">
                                            <?= file_exists(Config::MASTER_DB) ? '✓' : '✗' ?>
                                        </div>
                                        <div class="health-label">Master Database</div>
                                    </div>
                                    <div class="health-item">
                                        <div class="health-status <?= is_writable(Config::DB_DIR) ? 'healthy' : 'error' ?>">
                                            <?= is_writable(Config::DB_DIR) ? '✓' : '✗' ?>
                                        </div>
                                        <div class="health-label">Database Directory</div>
                                    </div>
                                    <div class="health-item">
                                        <div class="health-status <?= is_writable(__DIR__ . '/../storage/') ? 'healthy' : 'error' ?>">
                                            <?= is_writable(__DIR__ . '/../storage/') ? '✓' : '✗' ?>
                                        </div>
                                        <div class="health-label">Storage Directory</div>
                                    </div>
                                    <div class="health-item">
                                        <div class="health-status <?= extension_loaded('sqlite3') ? 'healthy' : 'error' ?>">
                                            <?= extension_loaded('sqlite3') ? '✓' : '✗' ?>
                                        </div>
                                        <div class="health-label">SQLite3 Extension</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Tenants -->
                        <div class="card">
                            <div class="card-header">
                                <h2 class="card-title">Recent Tenants</h2>
                                <a href="tenant_manager.php" class="btn btn-outline btn-sm">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (empty($tenants)): ?>
                                    <div class="empty-state">
                                        <p>No tenants found.</p>
                                        <a href="create_tenant.php" class="btn btn-primary">Create First Tenant</a>
                                    </div>
                                <?php else: ?>
                                    <div class="tenant-list">
                                        <?php foreach ($tenants as $tenant): ?>
                                            <div class="tenant-item">
                                                <div class="tenant-info">
                                                    <h3><?= htmlspecialchars($tenant['business_name']) ?></h3>
                                                    <p class="tenant-subdomain">
                                                        <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.<?= htmlspecialchars($currentDomain) ?>" target="_blank">
                                                            <?= htmlspecialchars($tenant['subdomain']) ?>.<?= htmlspecialchars($currentDomain) ?>
                                                        </a>
                                                    </p>
                                                    <p class="tenant-created">Created: <?= date('M j, Y', strtotime($tenant['created_at'])) ?></p>
                                                </div>
                                                <div class="tenant-status">
                                                    <span class="status-badge <?= $tenant['status'] ?>">
                                                        <?= ucfirst($tenant['status']) ?>
                                                    </span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="card-footer">
                                <a href="tenant_manager.php" class="btn btn-primary">Manage All Tenants</a>
                                <a href="create_tenant.php" class="btn btn-secondary">Create New</a>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card">
                            <div class="card-header">
                                <h2 class="card-title">Quick Actions</h2>
                            </div>
                            <div class="card-body">
                                <div class="action-buttons">
                                    <a href="create_tenant.php" class="btn btn-success">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="16"></line>
                                            <line x1="8" y1="12" x2="16" y2="12"></line>
                                        </svg>
                                        Create New Tenant
                                    </a>
                                    <a href="dummy_data.php" class="btn btn-info">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                            <polyline points="14,2 14,8 20,8"></polyline>
                                        </svg>
                                        Generate Test Data
                                    </a>
                                    <a href="clean_db.php" class="btn btn-warning">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M4 7h16"></path>
                                            <path d="M10 11v6"></path>
                                            <path d="M14 11v6"></path>
                                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                                        </svg>
                                        Database Cleanup
                                    </a>
                                    <button onclick="SystemCore.cleanupLogs()" class="btn btn-secondary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M3 6h18"></path>
                                            <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                            <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                                        </svg>
                                        Cleanup Logs
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- System Information -->
                        <div class="card">
                            <div class="card-header">
                                <h2 class="card-title">System Information</h2>
                            </div>
                            <div class="card-body">
                                <div class="info-table">
                                    <table>
                                        <tr>
                                            <td>PHP Version</td>
                                            <td><?= PHP_VERSION ?></td>
                                        </tr>
                                        <tr>
                                            <td>Server Software</td>
                                            <td><?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></td>
                                        </tr>
                                        <tr>
                                            <td>Document Root</td>
                                            <td><?= $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown' ?></td>
                                        </tr>
                                        <tr>
                                            <td>System Load</td>
                                            <td><?= $stats['system_uptime'] ?></td>
                                        </tr>
                                        <tr>
                                            <td>Memory Usage</td>
                                            <td><?= formatFileSize(memory_get_usage(true)) ?></td>
                                        </tr>
                                        <tr>
                                            <td>Peak Memory</td>
                                            <td><?= formatFileSize(memory_get_peak_usage(true)) ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <script src="assets/system.js"></script>
</body>
</html>
