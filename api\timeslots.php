<?php

/**
 * Time Slots API
 * Generates available time slots for a service
 */

// Prevent any output before JSON response
error_reporting(0);
ini_set('display_errors', 0);
ob_start();

// Include dependencies if not already loaded
if (!class_exists('Config')) {
    require_once __DIR__ . '/../shared/config.php';
    require_once __DIR__ . '/../shared/database.php';
    require_once __DIR__ . '/../shared/tenant_manager.php';
    require_once __DIR__ . '/../shared/functions.php';
    require_once __DIR__ . '/../store-admin/core/Application.php';

    // Initialize systems
    Config::init();
    TenantManager::init();
    Application::init();

    // Set JSON response headers
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

    // Handle preflight requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        exit;
    }
}

require_once __DIR__ . '/../shared/availability_checker.php';

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    errorResponse('Method not allowed', 405);
}

$serviceId = $_GET['service_id'] ?? null;
$date = $_GET['date'] ?? null;

// Log the request for debugging
error_log("Timeslots API called with: serviceId=$serviceId, date=$date");
error_log("Request URI: " . $_SERVER['REQUEST_URI']);
error_log("HTTP Host: " . $_SERVER['HTTP_HOST']);

if (!$serviceId || !$date) {
    error_log("Timeslots API: Missing required parameters");
    errorResponse('Missing required parameters: service_id, date');
}

try {
    $db = TenantManager::getDatabase();
    $checker = new AvailabilityChecker($db);
    
    // Get service details
    $service = $db->fetchRow(
        "SELECT * FROM services WHERE id = :id AND is_active = 1",
        [':id' => $serviceId]
    );
    
    if (!$service) {
        errorResponse('Service not found', 404);
    }
    
    // Get detailed time slot availability
    $detailedSlots = $checker->getDetailedTimeSlotAvailability($serviceId, $date);

    // Add human-readable labels for frontend display
    $enhancedSlots = [];
    foreach ($detailedSlots as $time => $slot) {
        $enhancedSlots[$time] = [
            'available' => $slot['available'],
            'reason' => $slot['reason'] ?? 'available',
            'message' => $slot['message'] ?? ($slot['available'] ? 'Available for booking' : 'Not available'),
            'label' => getSlotLabel($slot['reason'] ?? 'available', $slot['available']),
            'color' => getSlotColor($slot['reason'] ?? 'available', $slot['available'])
        ];
    }

    successResponse([
        'service_id' => $serviceId,
        'date' => $date,
        'service_name' => $service['name'],
        'duration' => $service['duration'],
        'slots' => $enhancedSlots
    ]);
    
} catch (Exception $e) {
    logActivity("Time slots API error: " . $e->getMessage(), 'error');
    errorResponse('Failed to fetch time slots');
}

/**
 * Get human-readable label for slot status
 */
function getSlotLabel(string $reason, bool $available): string
{
    if ($available) {
        return 'Available';
    }

    switch ($reason) {
        case 'booked':
            return 'Booked';
        case 'duration_conflict':
            return 'Duration conflict';
        case 'employee_unavailable':
        case 'no_available_employees':
            return 'Staff unavailable';
        case 'insufficient_time':
            return 'Insufficient time';
        case 'past_time':
            return 'Past time';
        default:
            return 'Not available';
    }
}

/**
 * Get color code for slot status
 */
function getSlotColor(string $reason, bool $available): string
{
    if ($available) {
        return '#28a745'; // Green
    }

    switch ($reason) {
        case 'booked':
            return '#dc3545'; // Red
        case 'duration_conflict':
            return '#fd7e14'; // Orange
        case 'employee_unavailable':
        case 'no_available_employees':
            return '#6c757d'; // Gray
        case 'insufficient_time':
            return '#ffc107'; // Yellow
        case 'past_time':
            return '#6c757d'; // Gray
        default:
            return '#6c757d'; // Gray
    }
}
