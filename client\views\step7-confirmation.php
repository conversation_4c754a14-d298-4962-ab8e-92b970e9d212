<?php
/**
 * Step 7: Booking Confirmation View Template
 */
?>

<div class="confirmation-container">
    <div class="confirmation-icon">
        <i class="fas fa-check-circle"></i>
    </div>
    <div class="confirmation-title"><?= t('booking_confirmed', 'Το ραντεβού επιβεβαιώθηκε!') ?></div>
    <div class="confirmation-message">
        <?= t('booking_success_message', 'Το ραντεβού σας κλείστηκε επιτυχώς.<br>Θα λάβετε email επιβεβαίωσης σύντομα.') ?>
    </div>
    
    <div class="booking-summary">
        <div class="booking-id" id="bookingId">
            <!-- Booking ID will be loaded dynamically -->
        </div>
        
        <div id="bookingSummary">
            <!-- Booking summary will be loaded dynamically -->
        </div>
    </div>
    
    <div class="text-center">
        <button class="btn btn-primary" onclick="BookingSystem.instance.newBooking()">
            <i class="fas fa-plus"></i>
            <?= t('book_another', 'Κλείσιμο Άλλου Ραντεβού') ?>
        </button>
    </div>
</div>

<script>
// Load confirmation details when this step is shown
document.addEventListener('DOMContentLoaded', function() {
    if (typeof BookingSystem !== 'undefined' && BookingSystem.instance) {
        BookingSystem.instance.loadConfirmation();
    }
});
</script>
