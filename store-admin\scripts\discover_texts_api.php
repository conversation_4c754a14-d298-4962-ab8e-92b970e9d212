<?php
/**
 * Auto-Discovery API Endpoint
 * Provides web interface for text discovery
 */

// Start output buffering immediately to prevent any output before JSON
ob_start();

header('Content-Type: application/json');

require_once __DIR__ . '/../../shared/tenant_manager.php';

try {
    // Initialize tenant context
    TenantManager::init();

    // Include the discovery class
    require_once __DIR__ . '/discover_texts.php';

    $discovery = new TextDiscovery();

    // Capture all output from the discovery process
    ob_start();
    $discovery->scan();
    $output = ob_get_clean();

    // Parse the output to get count
    $count = 0;
    if (preg_match('/Saved (\d+) new translation keys/', $output, $matches)) {
        $count = (int)$matches[1];
    }

    // Clear any previous output and send JSON response
    ob_clean();
    echo json_encode([
        'success' => true,
        'count' => $count,
        'message' => 'Auto-discovery completed successfully',
        'output' => $output
    ]);

} catch (Exception $e) {
    // Clear any previous output and send error response
    ob_clean();
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// End output buffering
ob_end_flush();
