<?php

/**
 * Add Service Page
 * Dedicated page for adding new services
 */

// Handle form submission
if ($_POST && !isset($_POST['ajax'])) {
    require_once __DIR__ . '/../controllers/services.php';
    $result = handleServicesForm($_POST, $db);
    if ($result['success']) {
        Application::redirect('/store-admin/?page=services', $result['message'], 'success');
    } else {
        $error = $result['error'];
    }
}

// Get categories safely
try {
    $categories = $db->fetchAll("SELECT * FROM categories WHERE is_active = 1 ORDER BY name ASC");
} catch (Exception $e) {
    $categories = [];
    error_log("Error fetching categories: " . $e->getMessage());
}

// Get all employees safely
try {
    $employees = $db->fetchAll("SELECT * FROM employees WHERE is_active = 1 ORDER BY name ASC");
} catch (Exception $e) {
    $employees = [];
    error_log("Error fetching employees: " . $e->getMessage());
}
?>

<div class="page-header">
    <div class="page-header-left">
        <h1 class="page-title">Add Service</h1>
        <div class="breadcrumb">
            <a href="/store-admin/?page=services">Services</a>
            <span class="breadcrumb-separator">/</span>
            <span>Add Service</span>
        </div>
    </div>
    <div class="page-header-right">
        <a href="/store-admin/?page=services" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Services
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" class="entity-form">
        <input type="hidden" name="action" value="save">
        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

        <div class="form-section">
            <h3 class="form-section-title">Service Information</h3>

            <div class="form-group">
                <label for="name">Service Name *</label>
                <input type="text" id="name" name="name" required
                    value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                    placeholder="Enter service name">
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea id="description" name="description" rows="3"
                    placeholder="Describe the service..."><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="category_id">Category</label>
                    <select id="category_id" name="category_id" data-searchable data-placeholder="Search categories..." data-max-visible="5">
                        <option value="">No Category</option>
                        <?php foreach ($categories as $category): ?>
                            <option value="<?php echo $category['id']; ?>"
                                <?php echo (isset($_POST['category_id']) && $_POST['category_id'] === $category['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($category['name']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="duration">Duration (minutes) *</label>
                    <input type="number" id="duration" name="duration" min="5" max="480" required
                        value="<?php echo isset($_POST['duration']) ? $_POST['duration'] : 60; ?>">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="price">Price (€) *</label>
                    <input type="number" id="price" name="price" step="0.01" min="0" required
                        value="<?php echo isset($_POST['price']) ? $_POST['price'] : ''; ?>"
                        placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="employee_selection">Employee Selection</label>
                    <select id="employee_selection" name="employee_selection">
                        <option value="auto" <?php echo (!isset($_POST['employee_selection']) || $_POST['employee_selection'] === 'auto') ? 'selected' : ''; ?>>
                            Automatic (First Available)
                        </option>
                        <option value="manual" <?php echo (isset($_POST['employee_selection']) && $_POST['employee_selection'] === 'manual') ? 'selected' : ''; ?>>
                            Manual Selection Required
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Time Settings -->
        <div class="form-section">
            <h3 class="form-section-title">Time Settings</h3>
            <div class="form-row">
                <div class="form-group">
                    <label for="preparation_time">Preparation Time (minutes)</label>
                    <input type="number" id="preparation_time" name="preparation_time" min="0" max="60"
                        value="<?php echo isset($_POST['preparation_time']) ? $_POST['preparation_time'] : Application::getSetting('default_preparation_time', 0); ?>">
                </div>
                <div class="form-group">
                    <label for="cleanup_time">Cleanup Time (minutes)</label>
                    <input type="number" id="cleanup_time" name="cleanup_time" min="0" max="60"
                        value="<?php echo isset($_POST['cleanup_time']) ? $_POST['cleanup_time'] : Application::getSetting('default_cleanup_time', 0); ?>">
                </div>
            </div>
        </div>

        <!-- Employee Assignment -->
        <div class="form-section">
            <h3 class="form-section-title">Assign Employees</h3>
            <div class="employee-checkboxes">
                <?php if (empty($employees)): ?>
                    <p class="text-muted">No employees available. <a href="/store-admin/?page=add-employee">Add employees first</a>.</p>
                <?php else: ?>
                    <?php foreach ($employees as $employee): ?>
                        <label class="employee-checkbox">
                            <input type="checkbox" name="employee_ids[]" value="<?php echo $employee['id']; ?>"
                                <?php echo (isset($_POST['employee_ids']) && in_array($employee['id'], $_POST['employee_ids'])) ? 'checked' : ''; ?>>
                            <div class="employee-info">
                                <div class="employee-avatar" style="background-color: <?php echo htmlspecialchars($employee['color'] ?: '#3498db'); ?>">
                                    <?php echo strtoupper(substr($employee['name'], 0, 1)); ?>
                                </div>
                                <span><?php echo htmlspecialchars($employee['name']); ?></span>
                                <small><?php echo htmlspecialchars($employee['position'] ?? ''); ?></small>
                            </div>
                        </label>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="form-section">
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="is_active"
                        <?php echo (!isset($_POST['is_active']) || $_POST['is_active']) ? 'checked' : ''; ?>>
                    <span class="checkmark"></span>
                    Active
                </label>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Create Service
            </button>
            <a href="/store-admin/?page=services" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .page-header-left h1 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
    }

    .breadcrumb {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .breadcrumb a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
    }

    .content-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        margin: 0 0 1.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        font-size: 1.125rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .employee-checkboxes {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .employee-checkbox {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .employee-checkbox:hover {
        border-color: var(--primary-color);
        background-color: var(--light-color);
    }

    .employee-checkbox input[type="checkbox"] {
        margin-right: 0.75rem;
        width: auto;
    }

    .employee-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .employee-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .employee-info span {
        font-weight: 500;
    }

    .employee-info small {
        color: var(--text-muted);
        font-size: 0.75rem;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: 500;
    }

    .checkbox-label input[type="checkbox"] {
        margin-right: 0.5rem;
        width: auto;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }

    .alert {
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .alert-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    .text-muted {
        color: var(--text-muted);
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .employee-checkboxes {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .content-card {
            padding: 1rem;
        }
    }
</style>