<?php

/**
 * Reservations View
 * Manage reservations
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Additional filters
$statusFilter = $_GET['status'] ?? '';
$dateFilter = $_GET['date'] ?? '';
$customerFilter = $_GET['customer_id'] ?? '';

// Build search query - search in customer name/email/phone, service name, employee name/phone, and notes
$searchWhere = AdminHelpers::buildSearchWhere($search, ['c.name', 'c.email', 'c.phone', 's.name', 'e.name', 'e.phone', 'r.notes']);

// Add additional filters
$additionalWhere = [];
$additionalParams = [];

if ($statusFilter) {
    $additionalWhere[] = "r.status = :status_filter";
    $additionalParams[':status_filter'] = $statusFilter;
}

if ($dateFilter) {
    $additionalWhere[] = "r.date = :date_filter";
    $additionalParams[':date_filter'] = $dateFilter;
}

if ($customerFilter) {
    $additionalWhere[] = "r.customer_id = :customer_filter";
    $additionalParams[':customer_filter'] = $customerFilter;
}

// Combine where clauses
$whereClause = $searchWhere['where'];
if (!empty($additionalWhere)) {
    if ($whereClause) {
        $whereClause .= " AND " . implode(' AND ', $additionalWhere);
    } else {
        $whereClause = "WHERE " . implode(' AND ', $additionalWhere);
    }
}

$allParams = array_merge($searchWhere['params'], $additionalParams);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total
             FROM reservations r
             LEFT JOIN customers c ON r.customer_id = c.id
             LEFT JOIN services s ON r.service_id = s.id
             LEFT JOIN employees e ON r.employee_id = e.id
             {$whereClause}";
$totalCount = $db->fetchRow($countSql, $allParams)['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get reservations with related data using proper SQL pagination
$sql = "SELECT r.*, c.name as customer_name, c.email as customer_email, c.phone as customer_phone,
               s.name as service_name, s.duration as service_duration, s.price as service_price,
               e.name as employee_name, e.color as employee_color
        FROM reservations r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN services s ON r.service_id = s.id
        LEFT JOIN employees e ON r.employee_id = e.id
        {$whereClause}
        ORDER BY r.date DESC, r.start_time DESC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedReservations = $db->fetchAll($sql, $allParams);

// Get filter options
$statuses = ['pending', 'confirmed', 'completed', 'cancelled'];
?>



<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-reservation" class="btn btn-primary">
            <i class="fas fa-plus"></i> New Booking
        </a>
        <button class="btn btn-secondary" onclick="showCalendar()">
            <i class="fas fa-calendar"></i> Calendar
        </button>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search by customer, service, employee..." value="<?php echo htmlspecialchars($search); ?>" id="reservation-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('reservation-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Date:</label>
            <select class="form-control" id="date-filter">
                <option value="">All Dates</option>
                <option value="today">Today</option>
                <option value="tomorrow">Tomorrow</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter">
                <option value="">All Status</option>
                <option value="confirmed">Confirmed</option>
                <option value="completed">Completed</option>
                <option value="cancelled">Cancelled</option>
                <option value="no-show">No Show</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Employee:</label>
            <select class="form-control" id="employee-filter">
                <option value="">All Employees</option>
                <?php
                $employees = $db->fetchAll("SELECT id, name FROM employees WHERE is_active = 1 ORDER BY name");
                foreach ($employees as $emp): ?>
                    <option value="<?php echo $emp['id']; ?>"><?php echo htmlspecialchars($emp['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Service:</label>
            <select class="form-control" id="service-filter">
                <option value="">All Services</option>
                <?php
                $services = $db->fetchAll("SELECT id, name FROM services ORDER BY name");
                foreach ($services as $service): ?>
                    <option value="<?php echo $service['id']; ?>"><?php echo htmlspecialchars($service['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
    </div>
</div>



<!-- Old filter section removed - using enhanced toolbar above -->

<!-- Enhanced Reservations Grid -->
<div class="entity-grid" id="reservations-grid">
    <?php if (empty($paginatedReservations)): ?>
        <div class="empty-state">
            <i class="fas fa-calendar-alt fa-2x text-muted"></i>
            <h3>No reservations found</h3>
            <p>Start by creating your first reservation or adjust your search criteria.</p>
            <a href="/store-admin/?page=add-reservation" class="btn btn-primary btn-lg">
                <i class="fas fa-plus"></i> New Reservation
            </a>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedReservations as $reservation):
            // Prepare reservation data for the card component
            $reservationDate = new DateTime($reservation['date']);
            $today = new DateTime();
            $isToday = $reservationDate->format('Y-m-d') === $today->format('Y-m-d');
            $isPast = $reservationDate < $today;
            $isFuture = $reservationDate > $today;

            // Prepare reservation data for card
            $reservationData = [
                'id' => $reservation['id'],
                'name' => $reservation['customer_name'],
                'customer_name' => $reservation['customer_name'],
                'customer_email' => $reservation['customer_email'] ?? '',
                'customer_phone' => $reservation['customer_phone'] ?? '',
                'service_name' => $reservation['service_name'],
                'employee_name' => $reservation['employee_name'],
                'date' => $reservation['date'],
                'start_time' => $reservation['start_time'],
                'end_time' => $reservation['end_time'],
                'duration' => $reservation['duration'],
                'price' => $reservation['price'],
                'status' => $reservation['status'],
                'notes' => $reservation['notes'] ?? '',
                'is_today' => $isToday,
                'is_past' => $isPast,
                'is_future' => $isFuture,
                'created_at' => $reservation['created_at'] ?? null
            ];

            // Render the reservation card
            echo renderEntityCard($reservationData, 'reservation', [
                'show_checkbox' => false,
                'show_actions' => false
            ]);
        endforeach; ?>

        <!-- Add Reservation Card -->
        <div class="entity-card add-card" onclick="window.location.href='/store-admin/?page=add-reservation'">
            <div class="card__body">
                <div class="add-card-content">
                    <i class="fas fa-plus fa-2x" style="color: var(--gray-400); margin-bottom: 16px;"></i>
                    <h3 style="color: var(--gray-600); font-size: var(--text-lg);">New Reservation</h3>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<script>
    function applyFilter(type, value) {
        const url = new URL(window.location);
        if (value) {
            url.searchParams.set(type, value);
        } else {
            url.searchParams.delete(type);
        }
        url.searchParams.set('page_num', 1); // Reset to first page
        window.location.href = url.toString();
    }

    function removeFilter(type) {
        const url = new URL(window.location);
        url.searchParams.delete(type);
        url.searchParams.set('page_num', 1);
        window.location.href = url.toString();
    }

    function clearAllFilters() {
        const url = new URL(window.location);
        url.searchParams.delete('status');
        url.searchParams.delete('date');
        url.searchParams.delete('customer_id');
        url.searchParams.set('page_num', 1);
        window.location.href = url.toString();
    }
</script>