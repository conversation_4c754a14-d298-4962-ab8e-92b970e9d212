/**
 * System Core Dashboard JavaScript - Enhanced UX/UI
 * Interactive functionality for the system admin interface
 */

// Global system state
const SystemCore = {
    currentView: 'cards',
    selectedTenants: new Set(),
    searchQuery: '',
    filters: {
        status: 'all',
        dateRange: 'all'
    },
    sidebarCollapsed: false
};

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// DOM ready handler
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

/**
 * Initialize system functionality
 */
function initializeSystem() {
    // Initialize layout
    initializeLayout();

    // Initialize search and filters
    initializeSearchAndFilters();

    // Initialize bulk actions
    initializeBulkActions();

    // Initialize view toggle
    initializeViewToggle();

    // Initialize dropdowns
    initializeDropdowns();

    // Initialize modals
    initializeModals();

    // Initialize tooltips
    initializeTooltips();

    // Initialize auto-refresh
    initializeAutoRefresh();

    // Initialize form validation
    initializeFormValidation();

    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();

    // Load saved preferences
    loadSavedPreferences();

    // Add resize listener for responsive behavior
    window.addEventListener('resize', debounce(handleResponsiveViewChange, 250));

    console.log('Enhanced System Core initialized');
}

/**
 * Initialize layout functionality
 */
function initializeLayout() {
    // Sidebar toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Mobile sidebar overlay
    const sidebarOverlay = document.createElement('div');
    sidebarOverlay.className = 'sidebar-overlay';
    sidebarOverlay.addEventListener('click', closeSidebar);
    document.body.appendChild(sidebarOverlay);
}

/**
 * Toggle sidebar
 */
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        SystemCore.sidebarCollapsed = !SystemCore.sidebarCollapsed;
        sidebar.classList.toggle('collapsed', SystemCore.sidebarCollapsed);
        localStorage.setItem('sidebarCollapsed', SystemCore.sidebarCollapsed);

        // Mobile handling
        if (window.innerWidth <= 1024) {
            sidebar.classList.toggle('open');
            document.querySelector('.sidebar-overlay').classList.toggle('show');
        }
    }
}

/**
 * Close sidebar (mobile)
 */
function closeSidebar() {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.classList.remove('open');
        document.querySelector('.sidebar-overlay').classList.remove('show');
    }
}

/**
 * Initialize search and filters
 */
function initializeSearchAndFilters() {
    // Search functionality
    const searchInput = document.querySelector('.search-input, .search-bar input');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(handleSearch, 300));
    }

    // Filter functionality
    const filterSelects = document.querySelectorAll('.filter-select');
    filterSelects.forEach(select => {
        select.addEventListener('change', handleFilterChange);
    });

    // Clear filters button
    const clearFiltersBtn = document.querySelector('.clear-filters');
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearFilters);
    }
}

/**
 * Handle search input
 */
function handleSearch(e) {
    SystemCore.searchQuery = e.target.value.toLowerCase();
    filterTenants();
}

/**
 * Handle filter changes
 */
function handleFilterChange(e) {
    const filterType = e.target.dataset.filter;
    const filterValue = e.target.value;
    SystemCore.filters[filterType] = filterValue;
    filterTenants();
}

/**
 * Clear all filters
 */
function clearFilters() {
    SystemCore.searchQuery = '';
    SystemCore.filters = { status: 'all', dateRange: 'all' };

    // Reset UI
    const searchInput = document.querySelector('.search-input, .search-bar input');
    if (searchInput) searchInput.value = '';

    document.querySelectorAll('.filter-select').forEach(select => {
        select.value = 'all';
    });

    filterTenants();
}

/**
 * Filter tenants based on search and filters
 */
function filterTenants() {
    const tenantCards = document.querySelectorAll('.tenant-card');
    const tableRows = document.querySelectorAll('.table tbody tr');

    let visibleCount = 0;

    // Filter cards
    tenantCards.forEach(card => {
        const shouldShow = matchesFilters(card);
        card.style.display = shouldShow ? 'block' : 'none';
        if (shouldShow) visibleCount++;
    });

    // Filter table rows
    tableRows.forEach(row => {
        const shouldShow = matchesFilters(row);
        row.style.display = shouldShow ? 'table-row' : 'none';
    });

    updateEmptyState(visibleCount === 0);
    updateResultsCount(visibleCount);
}

/**
 * Check if element matches current filters
 */
function matchesFilters(element) {
    // Search query filter
    if (SystemCore.searchQuery) {
        const text = element.textContent.toLowerCase();
        if (!text.includes(SystemCore.searchQuery)) {
            return false;
        }
    }

    // Status filter
    if (SystemCore.filters.status !== 'all') {
        const statusElement = element.querySelector('.status-badge');
        if (statusElement && !statusElement.classList.contains(SystemCore.filters.status)) {
            return false;
        }
    }

    return true;
}

/**
 * Update empty state visibility
 */
function updateEmptyState(isEmpty) {
    const emptyState = document.querySelector('.empty-state');
    const contentArea = document.querySelector('.tenant-grid, .table-view');

    if (emptyState && contentArea) {
        emptyState.style.display = isEmpty ? 'block' : 'none';
        contentArea.style.display = isEmpty ? 'none' : '';
    }
}

/**
 * Update results count
 */
function updateResultsCount(count) {
    const resultsCount = document.querySelector('.results-count');
    if (resultsCount) {
        resultsCount.textContent = `${count} tenant${count !== 1 ? 's' : ''}`;
    }
}

/**
 * Initialize bulk actions
 */
function initializeBulkActions() {
    // Tenant selection checkboxes
    document.addEventListener('change', function(e) {
        if (e.target.matches('.tenant-select')) {
            handleTenantSelection(e.target);
        }
    });

    // Select all checkbox
    const selectAllCheckbox = document.querySelector('#select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', handleSelectAll);
    }

    // Bulk action buttons
    const bulkActionButtons = document.querySelectorAll('.bulk-action-btn');
    bulkActionButtons.forEach(button => {
        button.addEventListener('click', handleBulkAction);
    });
}

/**
 * Handle tenant selection
 */
function handleTenantSelection(checkbox) {
    const tenantId = checkbox.value;
    const tenantCard = checkbox.closest('.tenant-card, tr');

    if (checkbox.checked) {
        SystemCore.selectedTenants.add(tenantId);
        tenantCard?.classList.add('selected');
    } else {
        SystemCore.selectedTenants.delete(tenantId);
        tenantCard?.classList.remove('selected');
    }

    updateBulkActionsVisibility();
    updateSelectAllState();
}

/**
 * Handle select all
 */
function handleSelectAll(e) {
    const isChecked = e.target.checked;
    const visibleCheckboxes = document.querySelectorAll('.tenant-select:not([style*="display: none"])');

    visibleCheckboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
        handleTenantSelection(checkbox);
    });
}

/**
 * Update bulk actions visibility
 */
function updateBulkActionsVisibility() {
    const bulkActions = document.querySelector('.bulk-actions');
    const selectedCount = SystemCore.selectedTenants.size;

    if (bulkActions) {
        bulkActions.classList.toggle('show', selectedCount > 0);

        const countText = document.querySelector('.bulk-actions-text');
        if (countText) {
            countText.textContent = `${selectedCount} tenant${selectedCount !== 1 ? 's' : ''} selected`;
        }
    }
}

/**
 * Update select all checkbox state
 */
function updateSelectAllState() {
    const selectAllCheckbox = document.querySelector('#select-all');
    const visibleCheckboxes = document.querySelectorAll('.tenant-select:not([style*="display: none"])');
    const checkedCheckboxes = document.querySelectorAll('.tenant-select:checked:not([style*="display: none"])');

    if (selectAllCheckbox && visibleCheckboxes.length > 0) {
        selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < visibleCheckboxes.length;
        selectAllCheckbox.checked = checkedCheckboxes.length === visibleCheckboxes.length;
    }
}

/**
 * Handle bulk actions
 */
function handleBulkAction(e) {
    const action = e.target.dataset.action;
    const selectedIds = Array.from(SystemCore.selectedTenants);

    if (selectedIds.length === 0) {
        showNotification('Please select at least one tenant.', 'warning');
        return;
    }

    switch (action) {
        case 'delete':
            if (confirm(`Are you sure you want to delete ${selectedIds.length} tenant(s)? This action cannot be undone.`)) {
                bulkDeleteTenants(selectedIds);
            }
            break;
        case 'activate':
            bulkUpdateStatus(selectedIds, 'active');
            break;
        case 'deactivate':
            bulkUpdateStatus(selectedIds, 'inactive');
            break;
        case 'backup':
            bulkBackupTenants(selectedIds);
            break;
        case 'export':
            exportSelectedTenants(selectedIds);
            break;
    }
}

/**
 * Initialize view toggle with responsive defaults
 */
function initializeViewToggle() {
    const viewToggleButtons = document.querySelectorAll('.view-toggle button');
    viewToggleButtons.forEach(button => {
        button.addEventListener('click', handleViewToggle);
    });

    // Initialize with responsive default or saved preference
    initializeDefaultView();
}

/**
 * Initialize default view based on screen size or saved preference
 */
function initializeDefaultView() {
    // Check for saved user preference first
    const savedPreference = localStorage.getItem('preferredView');

    let defaultView;
    if (savedPreference) {
        // Use saved preference
        defaultView = savedPreference;
        console.log(`Using saved view preference: ${defaultView}`);
    } else {
        // Determine default based on screen size
        defaultView = getResponsiveDefaultView();
        console.log(`Using responsive default view: ${defaultView}`);
    }

    // Apply the default view
    setActiveView(defaultView);
}

/**
 * Get responsive default view based on screen size
 */
function getResponsiveDefaultView() {
    // Use CSS media query to determine screen size
    const isLargeScreen = window.matchMedia('(min-width: 1024px)').matches;
    const isMobile = window.matchMedia('(max-width: 768px)').matches;

    if (isMobile) {
        return 'cards'; // Cards are better for mobile
    } else if (isLargeScreen) {
        return 'table'; // Table view for large screens
    } else {
        return 'cards'; // Default to cards for medium screens
    }
}

/**
 * Set active view and update UI
 */
function setActiveView(viewType) {
    SystemCore.currentView = viewType;

    // Update button states
    document.querySelectorAll('.view-toggle button').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.view === viewType) {
            btn.classList.add('active');
        }
    });

    // Switch view
    switchView(viewType);
}

/**
 * Handle view toggle
 */
function handleViewToggle(e) {
    const viewType = e.target.dataset.view;
    if (!viewType) return;

    setActiveView(viewType);

    // Save user preference
    localStorage.setItem('preferredView', viewType);
    console.log(`User selected ${viewType} view`);
}

/**
 * Switch between card and table view
 */
function switchView(viewType) {
    const gridView = document.querySelector('.tenant-grid');
    const tableView = document.querySelector('.table-view');

    if (viewType === 'table') {
        if (gridView) gridView.style.display = 'none';
        if (tableView) tableView.style.display = 'block';
    } else {
        if (gridView) gridView.style.display = 'grid';
        if (tableView) tableView.style.display = 'none';
    }
}

/**
 * Handle responsive view changes
 */
function handleResponsiveViewChange() {
    const savedPreference = localStorage.getItem('preferredView');

    // Only auto-switch if user hasn't made a preference choice
    if (!savedPreference) {
        const newDefaultView = getResponsiveDefaultView();

        if (SystemCore.currentView !== newDefaultView) {
            console.log(`Screen size changed, switching to: ${newDefaultView}`);
            setActiveView(newDefaultView);
        }
    }
}

/**
 * Initialize dropdown menus
 */
function initializeDropdowns() {
    document.addEventListener('click', function(e) {
        // Close all dropdowns when clicking outside
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown').forEach(dropdown => {
                dropdown.classList.remove('open');
            });
        }

        // Toggle dropdown when clicking toggle button
        if (e.target.closest('.dropdown-toggle')) {
            e.preventDefault();
            const dropdown = e.target.closest('.dropdown');

            // Close other dropdowns
            document.querySelectorAll('.dropdown').forEach(d => {
                if (d !== dropdown) d.classList.remove('open');
            });

            dropdown.classList.toggle('open');
        }
    });
}

/**
 * Initialize modal functionality
 */
function initializeModals() {
    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });
    
    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const openModals = document.querySelectorAll('.modal[style*="block"]');
            openModals.forEach(modal => {
                closeModal(modal.id);
            });
        }
    });
}

/**
 * Close modal by ID
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * Show tooltip
 */
function showTooltip(e) {
    const text = e.target.getAttribute('data-tooltip');
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = text;
    tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        pointer-events: none;
    `;
    
    document.body.appendChild(tooltip);
    
    const rect = e.target.getBoundingClientRect();
    tooltip.style.left = rect.left + 'px';
    tooltip.style.top = (rect.bottom + 5) + 'px';
}

/**
 * Hide tooltip
 */
function hideTooltip() {
    const tooltip = document.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

/**
 * Initialize auto-refresh functionality
 */
function initializeAutoRefresh() {
    // Auto-refresh every 30 seconds on dashboard
    if (window.location.pathname.includes('index.php') || window.location.pathname.endsWith('/')) {
        setInterval(function() {
            // Only refresh if user is still active
            if (document.visibilityState === 'visible') {
                updateSystemStats();
            }
        }, 30000);
    }
}

/**
 * Update system statistics
 */
function updateSystemStats() {
    // This would normally be an AJAX call to get fresh stats
    // For now, we'll just add a visual indicator
    const statsElements = document.querySelectorAll('.stat-value');
    statsElements.forEach(element => {
        element.style.opacity = '0.5';
        setTimeout(() => {
            element.style.opacity = '1';
        }, 200);
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(form)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(input);
            });
        });
    });
}

/**
 * Validate entire form
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * Validate individual field
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Required field validation
    if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = 'This field is required';
    }
    
    // Email validation
    if (field.type === 'email' && value && !isValidEmail(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid email address';
    }
    
    // Pattern validation
    if (field.hasAttribute('pattern') && value && !new RegExp(field.pattern).test(value)) {
        isValid = false;
        errorMessage = 'Please enter a valid format';
    }
    
    // Show/hide error
    showFieldError(field, isValid ? '' : errorMessage);
    
    return isValid;
}

/**
 * Show field error message
 */
function showFieldError(field, message) {
    // Remove existing error
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add new error if needed
    if (message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        errorDiv.style.cssText = `
            color: var(--danger-color);
            font-size: 0.8rem;
            margin-top: 0.25rem;
        `;
        field.parentNode.appendChild(errorDiv);
        field.style.borderColor = 'var(--danger-color)';
    } else {
        field.style.borderColor = '';
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Initialize keyboard shortcuts
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K for search
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('input[type="search"]');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            window.location.reload();
        }
    });
}

/**
 * Show loading state
 */
function showLoading(element) {
    if (element) {
        element.style.opacity = '0.5';
        element.style.pointerEvents = 'none';
    }
}

/**
 * Hide loading state
 */
function hideLoading(element) {
    if (element) {
        element.style.opacity = '1';
        element.style.pointerEvents = 'auto';
    }
}

/**
 * Show notification
 */
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: var(--${type}-color);
        color: white;
        padding: 1rem;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow);
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto-remove
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, duration);
}

/**
 * Confirm action with custom message
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Copy text to clipboard
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!', 'success', 2000);
    }).catch(() => {
        showNotification('Failed to copy to clipboard', 'error', 3000);
    });
}

/**
 * Format file size
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format date
 */
function formatDate(date) {
    const options = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return new Date(date).toLocaleDateString('en-US', options);
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Throttle function
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * Generate random ID
 */
function generateId(prefix = 'id') {
    return prefix + '_' + Math.random().toString(36).substr(2, 9);
}

/**
 * Cleanup logs via AJAX (if implemented)
 */
function cleanupLogs() {
    if (confirm('Clean up old log files?')) {
        // This would normally be an AJAX call
        showNotification('Log cleanup initiated...', 'info');
        
        // Simulate cleanup
        setTimeout(() => {
            showNotification('Log cleanup completed', 'success');
        }, 2000);
    }
}

/**
 * Export system data
 */
function exportSystemData() {
    const data = {
        timestamp: new Date().toISOString(),
        stats: getSystemStats(),
        tenants: getTenantData()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'system_data_' + new Date().toISOString().split('T')[0] + '.json';
    a.click();
    URL.revokeObjectURL(url);
}

/**
 * Get system stats from DOM
 */
function getSystemStats() {
    const stats = {};
    const statElements = document.querySelectorAll('.stat-item');
    
    statElements.forEach(element => {
        const label = element.querySelector('.stat-label');
        const value = element.querySelector('.stat-value');
        
        if (label && value) {
            stats[label.textContent] = value.textContent;
        }
    });
    
    return stats;
}

/**
 * Get tenant data from DOM
 */
function getTenantData() {
    const tenants = [];
    const tenantElements = document.querySelectorAll('.tenant-card');
    
    tenantElements.forEach(element => {
        const name = element.querySelector('h3');
        const subdomain = element.querySelector('.tenant-subdomain');
        const status = element.querySelector('.status-badge');
        
        if (name) {
            tenants.push({
                name: name.textContent,
                subdomain: subdomain ? subdomain.textContent : '',
                status: status ? status.textContent : ''
            });
        }
    });
    
    return tenants;
}

/**
 * Dark mode toggle (if implemented)
 */
function toggleDarkMode() {
    document.body.classList.toggle('dark-mode');
    const isDark = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDark);
}

/**
 * Initialize dark mode from localStorage
 */
function initializeDarkMode() {
    const isDark = localStorage.getItem('darkMode') === 'true';
    if (isDark) {
        document.body.classList.add('dark-mode');
    }
}

/**
 * Handle connection status
 */
function handleConnectionStatus() {
    window.addEventListener('online', () => {
        showNotification('Connection restored', 'success', 3000);
    });
    
    window.addEventListener('offline', () => {
        showNotification('Connection lost', 'warning', 0);
    });
}

// Initialize connection status monitoring
handleConnectionStatus();

// Global error handler
window.addEventListener('error', function(e) {
    console.error('System error:', e.error);
    showNotification('An error occurred. Please check the console.', 'error', 5000);
});

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    // console.error('Unhandled promise rejection:', e.reason);
    showNotification('An error occurred. Please try again.', 'error', 5000);
});

/**
 * Bulk operations
 */
function bulkDeleteTenants(tenantIds) {
    showLoading(document.body);
    showNotification(`Deleting ${tenantIds.length} tenant(s)...`, 'info');

    // Simulate API call
    setTimeout(() => {
        hideLoading(document.body);
        showNotification(`Successfully deleted ${tenantIds.length} tenant(s)`, 'success');
        clearSelection();
        // Refresh page or remove elements
        window.location.reload();
    }, 2000);
}

function bulkUpdateStatus(tenantIds, status) {
    showLoading(document.body);
    showNotification(`Updating status for ${tenantIds.length} tenant(s)...`, 'info');

    // Simulate API call
    setTimeout(() => {
        hideLoading(document.body);
        showNotification(`Successfully updated ${tenantIds.length} tenant(s) to ${status}`, 'success');
        clearSelection();
        // Update UI elements
        tenantIds.forEach(id => {
            const statusBadge = document.querySelector(`[data-tenant-id="${id}"] .status-badge`);
            if (statusBadge) {
                statusBadge.className = `status-badge ${status}`;
                statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        });
    }, 1500);
}

function bulkBackupTenants(tenantIds) {
    showLoading(document.body);
    showNotification(`Creating backups for ${tenantIds.length} tenant(s)...`, 'info');

    // Simulate API call
    setTimeout(() => {
        hideLoading(document.body);
        showNotification(`Successfully created backups for ${tenantIds.length} tenant(s)`, 'success');
        clearSelection();
    }, 3000);
}

function exportSelectedTenants(tenantIds) {
    const tenantData = tenantIds.map(id => {
        const card = document.querySelector(`[data-tenant-id="${id}"]`);
        if (card) {
            return {
                id: id,
                name: card.querySelector('.tenant-title')?.textContent || '',
                subdomain: card.querySelector('.tenant-subdomain')?.textContent || '',
                status: card.querySelector('.status-badge')?.textContent || ''
            };
        }
        return null;
    }).filter(Boolean);

    const blob = new Blob([JSON.stringify(tenantData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `selected_tenants_${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);

    showNotification(`Exported ${tenantData.length} tenant(s)`, 'success');
}

/**
 * Clear selection
 */
function clearSelection() {
    SystemCore.selectedTenants.clear();
    document.querySelectorAll('.tenant-select').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.querySelectorAll('.tenant-card, .table tr').forEach(element => {
        element.classList.remove('selected');
    });
    updateBulkActionsVisibility();
    updateSelectAllState();
}

/**
 * Load saved preferences
 */
function loadSavedPreferences() {
    // Load sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        SystemCore.sidebarCollapsed = true;
        document.querySelector('.sidebar')?.classList.add('collapsed');
    }

    // View preferences are now handled by initializeViewToggle()
    // This ensures responsive defaults work properly

    // Load dark mode
    initializeDarkMode();
}

/**
 * Enhanced notification system
 */
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    const icon = getNotificationIcon(type);
    notification.innerHTML = `
        <div class="notification-icon">${icon}</div>
        <div class="notification-content">
            <div class="notification-message">${message}</div>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">×</button>
    `;

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border: 1px solid var(--border-color);
        border-left: 4px solid var(--${type === 'error' ? 'danger' : type}-color);
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-lg);
        padding: 16px;
        max-width: 400px;
        z-index: 1000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        display: flex;
        align-items: center;
        gap: 12px;
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Auto-remove
    if (duration > 0) {
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, duration);
    }
}

function getNotificationIcon(type) {
    const icons = {
        success: '✓',
        error: '✗',
        warning: '⚠',
        info: 'ℹ'
    };
    return icons[type] || icons.info;
}

// Export enhanced functions for global use
window.SystemCore = {
    ...SystemCore,
    showNotification,
    confirmAction,
    copyToClipboard,
    formatFileSize,
    formatDate,
    exportSystemData,
    toggleDarkMode,
    cleanupLogs,
    showLoading,
    hideLoading,
    closeModal,
    toggleSidebar,
    clearSelection,
    filterTenants
};
