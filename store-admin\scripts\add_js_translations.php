<?php
/**
 * Add JavaScript Translation Keys to Database
 * Populates database with all the translation keys used in JavaScript
 */

require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

try {
    // Initialize tenant context
    TenantManager::init();
    $db = TenantManager::getDatabase();
    
    echo "Adding JavaScript translation keys...\n";
    
    // All JavaScript translation keys with Greek defaults
    $jsTranslations = [
        'select_category' => 'Επιλέξτε Κατηγορία',
        'select_category_subtitle' => 'Επιλέξτε τον τύπο υπηρεσίας που αναζητάτε',
        'select_service' => 'Επιλέξτε Υπηρεσία',
        'select_service_subtitle' => 'Επιλέξτε την υπηρεσία που θέλετε να κλείσετε',
        'select_date' => 'Επιλέξτε Ημερομηνία',
        'select_date_subtitle' => 'Επιλέξτε την προτιμώμενη ημερομηνία',
        'select_time' => 'Επιλέξτε Ώρα',
        'select_time_staff' => 'Επιλέξτε Ώρα & Προσωπικό',
        'choose_preferred_time' => 'Επιλέξτε την προτιμώμενη ώρα',
        'choose_preferred_time_staff' => 'Επιλέξτε την προτιμώμενη ώρα και μέλος προσωπικού',
        'contact_details' => 'Στοιχεία Επικοινωνίας',
        'contact_details_subtitle' => 'Παρακαλώ παρέχετε τα στοιχεία επικοινωνίας σας',
        'verify_email' => 'Επιβεβαιώστε το Email σας',
        'name' => 'Όνομα',
        'email' => 'Email',
        'phone' => 'Τηλέφωνο',
        'enter_full_name' => 'Εισάγετε το πλήρες όνομά σας',
        'additional_notes' => 'Επιπλέον Σημειώσεις',
        'optional' => 'Προαιρετικό',
        'next' => 'Επόμενο',
        'back' => 'Πίσω',
        'continue' => 'Συνέχεια',
        'loading' => 'Φόρτωση...',
        'please_wait' => 'Παρακαλώ περιμένετε...',
        'booking_confirmed' => 'Το ραντεβού επιβεβαιώθηκε!',
        'booking_success_message' => 'Το ραντεβού σας κλείστηκε επιτυχώς.<br>Θα λάβετε email επιβεβαίωσης σύντομα.',
        'book_another' => 'Κλείσιμο Άλλου Ραντεβού',
        'available' => 'Διαθέσιμο',
        'unavailable' => 'Μη διαθέσιμο',
        'closed' => 'Κλειστό',
        'required_field' => 'Αυτό το πεδίο είναι υποχρεωτικό',
        'invalid_email' => 'Παρακαλώ εισάγετε έγκυρο email',
        'invalid_phone' => 'Παρακαλώ εισάγετε έγκυρο τηλέφωνο',
        'enter_verification_code' => 'Εισάγετε τον κωδικό επιβεβαίωσης',
        'verification_code_sent' => 'Ο κωδικός επιβεβαίωσης στάλθηκε',
        'resend_code' => 'Επαναποστολή κωδικού',
        'verify_booking' => 'Επιβεβαίωση Ραντεβού',
        'minutes' => 'λεπτά',
        'any_staff' => 'Οποιοδήποτε προσωπικό',
        'auto_assign' => 'Αυτόματη ανάθεση',
        'no_available_times' => 'Δεν υπάρχουν διαθέσιμες ώρες',
        'select_different_date' => 'Επιλέξτε διαφορετική ημερομηνία',
        'booking_summary' => 'Περίληψη Ραντεβού',
        'service' => 'Υπηρεσία',
        'date' => 'Ημερομηνία',
        'time' => 'Ώρα',
        'staff' => 'Προσωπικό',
        'duration' => 'Διάρκεια',
        'price' => 'Τιμή',
        'online_booking' => 'Online Booking',
        'book_appointment_online' => 'Κλείστε το ραντεβού σας online'
    ];
    
    $timestamp = date('Y-m-d H:i:s');
    $added = 0;
    
    foreach ($jsTranslations as $key => $greekValue) {
        // Check if already exists
        $existing = $db->fetchRow("
            SELECT id FROM texts 
            WHERE text_key = :key AND language = 'el'
        ", [':key' => $key]);
        
        if (!$existing) {
            // Create Greek entry
            $db->query("
                INSERT INTO texts (id, text_key, text_value, language, category, auto_discovered, created_at, updated_at) 
                VALUES (:id, :key, :value, 'el', 'client', 1, :created_at, :updated_at)
            ", [
                ':id' => 'TXT' . strtoupper(substr(uniqid(), -8)),
                ':key' => $key,
                ':value' => $greekValue,
                ':created_at' => $timestamp,
                ':updated_at' => $timestamp
            ]);
            
            // Create English entry (empty)
            $db->query("
                INSERT INTO texts (id, text_key, text_value, language, category, auto_discovered, created_at, updated_at) 
                VALUES (:id, :key, '', 'en', 'client', 1, :created_at, :updated_at)
            ", [
                ':id' => 'TXT' . strtoupper(substr(uniqid(), -8)),
                ':key' => $key,
                ':created_at' => $timestamp,
                ':updated_at' => $timestamp
            ]);
            
            $added++;
        }
    }
    
    echo "Added {$added} new JavaScript translation keys\n";
    echo "Total keys processed: " . count($jsTranslations) . "\n";
    echo "Translation system is ready!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
