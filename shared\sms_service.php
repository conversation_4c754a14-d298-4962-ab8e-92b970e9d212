<?php

/**
 * SMS Service
 * Unified SMS sending functionality for all SMS providers
 */

require_once __DIR__ . '/database.php';

class SMSService
{
    private Database $db;
    
    public function __construct(Database $db = null)
    {
        $this->db = $db ?: TenantManager::getDatabase();
    }
    
    /**
     * Send SMS using the configured provider
     */
    public function sendSMS(string $phone, string $message): bool
    {
        try {
            // Get SMS settings
            $smsEnabled = $this->getSetting('sms_enabled', '0') === '1';
            if (!$smsEnabled) {
                error_log("SMS sending failed: SMS not enabled");
                return false;
            }
            
            $provider = $this->getSetting('sms_provider', 'none');
            if ($provider === 'none') {
                error_log("SMS sending failed: No SMS provider configured");
                return false;
            }
            
            // Send using the configured provider
            return $this->sendUsingProvider($provider, $phone, $message);
            
        } catch (Exception $e) {
            error_log("SMS sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send SMS using specific provider
     */
    private function sendUsingProvider(string $provider, string $phone, string $message): bool
    {
        switch ($provider) {
            case 'twilio':
                return $this->sendTwilioSMS($phone, $message);
            case 'nexmo':
                return $this->sendNexmoSMS($phone, $message);
            case 'apifon':
                return $this->sendApifonSMS($phone, $message);
            case 'smstools':
                return $this->sendSMSToolsSMS($phone, $message);
            case 'smsto':
                return $this->sendSMSToSMS($phone, $message);
            default:
                error_log("Unknown SMS provider: $provider");
                return false;
        }
    }
    
    /**
     * Send SMS via Twilio
     */
    private function sendTwilioSMS(string $phone, string $message): bool
    {
        $accountSid = $this->getSetting('twilio_account_sid', '');
        $authToken = $this->getSetting('twilio_auth_token', '');
        $fromNumber = $this->getSetting('twilio_from_number', '');
        
        if (empty($accountSid) || empty($authToken) || empty($fromNumber)) {
            error_log("Twilio configuration incomplete");
            return false;
        }
        
        $url = "https://api.twilio.com/2010-04-01/Accounts/$accountSid/Messages.json";
        
        $data = [
            'From' => $fromNumber,
            'To' => $phone,
            'Body' => $message
        ];
        
        return $this->makeHttpRequest($url, $data, [
            'Authorization: Basic ' . base64_encode("$accountSid:$authToken")
        ]);
    }
    
    /**
     * Send SMS via SMS.to
     */
    private function sendSMSToSMS(string $phone, string $message): bool
    {
        $apiKey = $this->getSetting('smsto_api_key', '');
        $sender = $this->getSetting('smsto_sender', 'SMS');
        
        if (empty($apiKey)) {
            error_log("SMS.to configuration incomplete");
            return false;
        }
        
        $url = "https://api.sms.to/sms/send";
        
        $data = [
            'message' => $message,
            'to' => $phone,
            'sender_id' => $sender
        ];
        
        return $this->makeHttpRequest($url, $data, [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ], true);
    }
    
    /**
     * Send SMS via Apifon
     */
    private function sendApifonSMS(string $phone, string $message): bool
    {
        $apiKey = $this->getSetting('apifon_api_key', '');
        $sender = $this->getSetting('apifon_sender', 'SMS');
        
        if (empty($apiKey)) {
            error_log("Apifon configuration incomplete");
            return false;
        }
        
        $url = "https://ars.apifon.com/services/api/v1/sms/send";
        $cleanPhone = ltrim($phone, '+');
        
        $data = [
            'subscribers' => [
                ['number' => $cleanPhone]
            ],
            'message' => [
                'text' => $message,
                'sender_id' => $sender
            ]
        ];
        
        return $this->makeHttpRequest($url, $data, [
            'Authorization: Bearer ' . $apiKey,
            'Content-Type: application/json'
        ], true);
    }
    
    /**
     * Placeholder methods for other providers
     */
    private function sendNexmoSMS(string $phone, string $message): bool
    {
        // Implementation would go here
        error_log("Nexmo SMS not implemented yet");
        return false;
    }
    
    private function sendSMSToolsSMS(string $phone, string $message): bool
    {
        // Implementation would go here
        error_log("SMSTools SMS not implemented yet");
        return false;
    }
    
    /**
     * Make HTTP request for SMS sending
     */
    private function makeHttpRequest(string $url, array $data, array $headers, bool $json = false): bool
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $json ? json_encode($data) : http_build_query($data),
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("SMS HTTP request error: $error");
            return false;
        }
        
        if ($httpCode >= 200 && $httpCode < 300) {
            error_log("SMS sent successfully. Response: $response");
            return true;
        } else {
            error_log("SMS sending failed. HTTP Code: $httpCode, Response: $response");
            return false;
        }
    }
    
    /**
     * Get setting value
     */
    private function getSetting(string $key, string $default = ''): string
    {
        $setting = $this->db->fetchRow(
            "SELECT value FROM settings WHERE key = :key",
            [':key' => $key]
        );
        
        return $setting ? $setting['value'] : $default;
    }
}
