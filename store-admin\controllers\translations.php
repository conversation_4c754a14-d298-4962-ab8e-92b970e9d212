<?php
/**
 * Translation Management Router
 * Routes translation requests to the new TranslationController
 */

require_once __DIR__ . '/TranslationController.php';

// This controller is called by admin index.php handleFormSubmission()
// The $data variable is passed from handleFormSubmission function
// Return result array instead of handling redirect directly

if (!isset($data)) {
    return [
        'redirect' => "/store-admin/?page=translations",
        'message' => 'No form data received',
        'type' => 'error'
    ];
}

$db = Application::getDb();
$result = TranslationController::handleRequest($data, $db);

// Preserve current state (category and search)
$currentCategory = $data['current_category'] ?? 'all';
$currentSearch = $data['current_search'] ?? '';

$redirectUrl = "/store-admin/?page=translations";
if ($currentCategory !== 'all') {
    $redirectUrl .= "&category=" . urlencode($currentCategory);
}
if (!empty($currentSearch)) {
    $redirectUrl .= "&search=" . urlencode($currentSearch);
}

if ($result['success']) {
    return [
        'redirect' => $redirectUrl,
        'message' => $result['message'],
        'type' => 'success'
    ];
} else {
    return [
        'redirect' => $redirectUrl,
        'message' => $result['error'],
        'type' => 'error'
    ];
}
