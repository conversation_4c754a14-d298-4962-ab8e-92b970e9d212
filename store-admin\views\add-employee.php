<?php

/**
 * Add Employee Page
 * Dedicated page for adding new employees
 */

// Handle form submission
if ($_POST && !isset($_POST['ajax'])) {
    require_once __DIR__ . '/../controllers/employees.php';
    $result = handleEmployeesForm($_POST, $db);
    if ($result['success']) {
        Application::redirect('/store-admin/?page=employees', $result['message'], 'success');
    } else {
        $error = $result['error'];
    }
}

// Get services
$services = $db->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC");

$days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
?>

<div class="page-header">
    <div class="page-header-left">
        <h1 class="page-title">Add Employee</h1>
        <div class="breadcrumb">
            <a href="/store-admin/?page=employees">Employees</a>
            <span class="breadcrumb-separator">/</span>
            <span>Add Employee</span>
        </div>
    </div>
    <div class="page-header-right">
        <a href="/store-admin/?page=employees" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Employees
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" class="entity-form">
        <input type="hidden" name="action" value="save">
        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

        <div class="form-section">
            <h3 class="form-section-title">Employee Information</h3>

            <div class="form-group">
                <label for="name">Employee Name *</label>
                <input type="text" id="name" name="name" required
                    value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : ''; ?>"
                    placeholder="Enter employee's full name">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email"
                        value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                        placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="phone">Phone</label>
                    <input type="tel" id="phone" name="phone"
                        value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>"
                        placeholder="69XXXXXXXX">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="position">Position</label>
                    <input type="text" id="position" name="position"
                        value="<?php echo isset($_POST['position']) ? htmlspecialchars($_POST['position']) : ''; ?>"
                        placeholder="e.g., Hair Stylist, Nail Technician">
                </div>
                <div class="form-group">
                    <label for="color">Color</label>
                    <input type="color" id="color" name="color"
                        value="<?php echo isset($_POST['color']) ? $_POST['color'] : '#3498db'; ?>">
                    <small class="help-text">This color will be used in the calendar and scheduling</small>
                </div>
            </div>
        </div>

        <!-- Working Hours -->
        <div class="form-section">
            <h3 class="form-section-title">Working Hours</h3>
            <div class="working-days">
                <?php foreach ($days as $day): ?>
                    <?php
                    $isWorking = isset($_POST['working_days']) && in_array($day, $_POST['working_days']);
                    ?>
                    <div class="day-hours">
                        <label class="day-checkbox">
                            <input type="checkbox" name="working_days[]" value="<?php echo $day; ?>"
                                <?php echo $isWorking ? 'checked' : ''; ?>
                                onchange="toggleDayHours('<?php echo $day; ?>')">
                            <strong><?php echo ucfirst($day); ?></strong>
                        </label>

                        <div class="day-times" id="<?php echo $day; ?>_times"
                            style="<?php echo $isWorking ? '' : 'display: none;'; ?>">
                            <div class="periods-container" id="<?php echo $day; ?>_employee_periods">
                                <div class="time-period" data-period="0">
                                    <input type="time" name="<?php echo $day; ?>_start[]"
                                        value="<?php echo isset($_POST[$day . '_start']) ? $_POST[$day . '_start'][0] : '09:00'; ?>" required>
                                    <span>to</span>
                                    <input type="time" name="<?php echo $day; ?>_end[]"
                                        value="<?php echo isset($_POST[$day . '_end']) ? $_POST[$day . '_end'][0] : '17:00'; ?>" required>
                                    <button type="button" class="btn-remove-period" onclick="removeEmployeePeriod('<?php echo $day; ?>', 0)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="addEmployeePeriod('<?php echo $day; ?>')">
                                <i class="fas fa-plus"></i> Add Period
                            </button>
                            <small class="help-text">Add multiple periods for breaks (e.g., 9-14 and 17-21)</small>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Service Assignment -->
        <div class="form-section">
            <h3 class="form-section-title">Assign Services</h3>
            <div class="service-checkboxes">
                <?php if (empty($services)): ?>
                    <p class="text-muted">No services available. <a href="/store-admin/?page=add-service">Add services first</a>.</p>
                <?php else: ?>
                    <?php foreach ($services as $service): ?>
                        <label class="service-checkbox">
                            <input type="checkbox" name="service_ids[]" value="<?php echo $service['id']; ?>"
                                <?php echo (isset($_POST['service_ids']) && in_array($service['id'], $_POST['service_ids'])) ? 'checked' : ''; ?>>
                            <div class="service-info">
                                <span><?php echo htmlspecialchars($service['name']); ?></span>
                                <small><?php echo $service['duration']; ?> min - €<?php echo number_format($service['price'], 2); ?></small>
                            </div>
                        </label>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="form-section">
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" name="is_active"
                        <?php echo (!isset($_POST['is_active']) || $_POST['is_active']) ? 'checked' : ''; ?>>
                    <span class="checkmark"></span>
                    Active
                </label>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Create Employee
            </button>
            <a href="/store-admin/?page=employees" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .page-header-left h1 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
    }

    .breadcrumb {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .breadcrumb a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
    }

    .content-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        margin: 0 0 1.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        font-size: 1.125rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .help-text {
        display: block;
        font-size: 0.75rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
    }

    .working-days {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .day-hours {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        padding: 1rem;
    }

    .day-checkbox {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        cursor: pointer;
    }

    .day-checkbox input[type="checkbox"] {
        margin-right: 0.5rem;
        width: auto;
    }

    .day-times {
        margin-left: 1.5rem;
    }

    .periods-container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .time-period {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .time-period input[type="time"] {
        width: auto;
        padding: 0.5rem;
    }

    .btn-remove-period {
        background: #dc2626;
        color: white;
        border: none;
        border-radius: var(--border-radius);
        padding: 0.25rem 0.5rem;
        cursor: pointer;
        font-size: 0.75rem;
    }

    .btn-remove-period:hover {
        background: #b91c1c;
    }

    .service-checkboxes {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .service-checkbox {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .service-checkbox:hover {
        border-color: var(--primary-color);
        background-color: var(--light-color);
    }

    .service-checkbox input[type="checkbox"] {
        margin-right: 0.75rem;
        width: auto;
    }

    .service-info span {
        font-weight: 500;
        display: block;
    }

    .service-info small {
        color: var(--text-muted);
        font-size: 0.75rem;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: 500;
    }

    .checkbox-label input[type="checkbox"] {
        margin-right: 0.5rem;
        width: auto;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }

    .alert {
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .alert-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    .text-muted {
        color: var(--text-muted);
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .service-checkboxes {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .content-card {
            padding: 1rem;
        }

        .time-period {
            flex-wrap: wrap;
        }
    }
</style>

<script>
    function toggleDayHours(day) {
        const checkbox = document.querySelector(`input[name="working_days[]"][value="${day}"]`);
        const timesDiv = document.getElementById(`${day}_times`);

        if (checkbox.checked) {
            timesDiv.style.display = 'block';
        } else {
            timesDiv.style.display = 'none';
        }
    }

    function addEmployeePeriod(day) {
        const container = document.getElementById(`${day}_employee_periods`);
        const periodCount = container.children.length;

        const periodDiv = document.createElement('div');
        periodDiv.className = 'time-period';
        periodDiv.setAttribute('data-period', periodCount);

        periodDiv.innerHTML = `
        <input type="time" name="${day}_start[]" value="09:00" required>
        <span>to</span>
        <input type="time" name="${day}_end[]" value="17:00" required>
        <button type="button" class="btn-remove-period" onclick="removeEmployeePeriod('${day}', ${periodCount})">
            <i class="fas fa-times"></i>
        </button>
    `;

        container.appendChild(periodDiv);
    }

    function removeEmployeePeriod(day, periodIndex) {
        const container = document.getElementById(`${day}_employee_periods`);
        const period = container.querySelector(`[data-period="${periodIndex}"]`);

        if (period && container.children.length > 1) {
            period.remove();
        }
    }
</script>