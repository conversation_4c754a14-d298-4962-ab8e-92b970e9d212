<?php
/**
 * Category Form
 * Add/Edit category form
 */

$isEdit = !empty($item);
$category = $item;
?>

<form method="POST" class="modal-form">
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
    <?php if ($isEdit): ?>
        <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
    <?php endif; ?>
    
    <div class="form-group">
        <label for="name">Category Name *</label>
        <input type="text" id="name" name="name" required
               value="<?php echo $isEdit ? htmlspecialchars($category['name']) : ''; ?>">
    </div>
    
    <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" rows="3"><?php echo $isEdit ? htmlspecialchars($category['description']) : ''; ?></textarea>
    </div>
    
    <div class="form-row">
        <div class="form-group">
            <label for="icon">Icon Class</label>
            <input type="text" id="icon" name="icon" 
                   placeholder="e.g., fas fa-cut"
                   value="<?php echo $isEdit ? htmlspecialchars($category['icon']) : ''; ?>">
            <small>Use Font Awesome icon classes</small>
        </div>
        <div class="form-group">
            <label for="color">Color</label>
            <input type="color" id="color" name="color" 
                   value="<?php echo $isEdit ? htmlspecialchars($category['color'] ?: '#3498db') : '#3498db'; ?>">
        </div>
    </div>
    
    <div class="form-row">
        <div class="form-group">
            <label for="sort_order">Sort Order</label>
            <input type="number" id="sort_order" name="sort_order" min="0"
                   value="<?php echo $isEdit ? $category['sort_order'] : 0; ?>">
        </div>
        <div class="form-group">
            <label>
                <input type="checkbox" name="is_active" 
                       <?php echo (!$isEdit || $category['is_active']) ? 'checked' : ''; ?>>
                Active
            </label>
        </div>
    </div>
    
    <div class="form-actions">
        <button type="submit" class="btn btn-primary">
            <?php echo $isEdit ? 'Update' : 'Create'; ?> Category
        </button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">
            Cancel
        </button>
    </div>
</form>

<style>
.modal-form {
    max-width: 500px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
