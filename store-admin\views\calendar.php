<?php

/**
 * Calendar View for Reservations
 * Shows reservations in a calendar format
 */

// Get current month/year or from URL parameters
$currentYear = isset($_GET['year']) ? (int)$_GET['year'] : date('Y');
$currentMonth = isset($_GET['month']) ? (int)$_GET['month'] : date('n');

// Validate month/year
if ($currentMonth < 1 || $currentMonth > 12) {
    $currentMonth = date('n');
}
if ($currentYear < 2020 || $currentYear > 2030) {
    $currentYear = date('Y');
}

$db = Application::getDb();

// Get reservations for the current month
$startDate = sprintf('%04d-%02d-01', $currentYear, $currentMonth);
$endDate = date('Y-m-t', strtotime($startDate)); // Last day of month

$reservations = $db->fetchAll(
    "SELECT r.*, c.name as customer_name, s.name as service_name, s.duration, e.name as employee_name, e.color as employee_color
     FROM reservations r
     JOIN customers c ON r.customer_id = c.id
     JOIN services s ON r.service_id = s.id
     LEFT JOIN employees e ON r.employee_id = e.id
     WHERE r.date BETWEEN :start_date AND :end_date
     AND r.status != 'cancelled'
     ORDER BY r.date, r.start_time",
    [':start_date' => $startDate, ':end_date' => $endDate]
);

// Group reservations by date
$reservationsByDate = [];
foreach ($reservations as $reservation) {
    $date = $reservation['date'];
    if (!isset($reservationsByDate[$date])) {
        $reservationsByDate[$date] = [];
    }
    $reservationsByDate[$date][] = $reservation;
}

// Calendar navigation
$prevMonth = $currentMonth - 1;
$prevYear = $currentYear;
if ($prevMonth < 1) {
    $prevMonth = 12;
    $prevYear--;
}

$nextMonth = $currentMonth + 1;
$nextYear = $currentYear;
if ($nextMonth > 12) {
    $nextMonth = 1;
    $nextYear++;
}

$monthNames = [
    1 => 'January',
    2 => 'February',
    3 => 'March',
    4 => 'April',
    5 => 'May',
    6 => 'June',
    7 => 'July',
    8 => 'August',
    9 => 'September',
    10 => 'October',
    11 => 'November',
    12 => 'December'
];

// Get first day of month and number of days
$firstDay = new DateTime($startDate);
$lastDay = new DateTime($endDate);
$daysInMonth = (int)$lastDay->format('d');
$startingDayOfWeek = (int)$firstDay->format('w'); // 0 = Sunday

// Convert to Monday-first (0 = Monday, 6 = Sunday)
$startingDayOfWeek = ($startingDayOfWeek === 0) ? 6 : $startingDayOfWeek - 1;
?>



<!-- Calendar Navigation -->
<div class="calendar-navigation">
    <div class="calendar-nav-controls">
        <button class="btn btn-outline calendar-nav-btn" onclick="navigateMonth(<?php echo $prevYear; ?>, <?php echo $prevMonth; ?>)">
            <i class="fas fa-chevron-left"></i>
            <span class="nav-text">Previous</span>
        </button>
        <h2 class="calendar-title"><?php echo $monthNames[$currentMonth] . ' ' . $currentYear; ?></h2>
        <button class="btn btn-outline calendar-nav-btn" onclick="navigateMonth(<?php echo $nextYear; ?>, <?php echo $nextMonth; ?>)">
            <span class="nav-text">Next</span>
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>
    <div class="calendar-actions">
        <button class="btn btn-secondary" onclick="goToToday()">
            <i class="fas fa-calendar-day"></i>
            <span class="btn-text">Today</span>
        </button>
    </div>
</div>

<!-- Calendar Grid -->
<div class="calendar-container">
    <div class="calendar-grid">
        <!-- Day headers -->
        <div class="calendar-day-header">
            <span class="day-short">M</span>
            <span class="day-full">Mon</span>
        </div>
        <div class="calendar-day-header">
            <span class="day-short">T</span>
            <span class="day-full">Tue</span>
        </div>
        <div class="calendar-day-header">
            <span class="day-short">W</span>
            <span class="day-full">Wed</span>
        </div>
        <div class="calendar-day-header">
            <span class="day-short">T</span>
            <span class="day-full">Thu</span>
        </div>
        <div class="calendar-day-header">
            <span class="day-short">F</span>
            <span class="day-full">Fri</span>
        </div>
        <div class="calendar-day-header">
            <span class="day-short">S</span>
            <span class="day-full">Sat</span>
        </div>
        <div class="calendar-day-header">
            <span class="day-short">S</span>
            <span class="day-full">Sun</span>
        </div>

        <?php
        // Add empty cells for days before the first day of the month
        for ($i = 0; $i < $startingDayOfWeek; $i++) {
            echo '<div class="calendar-day empty"></div>';
        }

        // Add days of the month
        $today = date('Y-m-d');
        for ($day = 1; $day <= $daysInMonth; $day++) {
            $currentDate = sprintf('%04d-%02d-%02d', $currentYear, $currentMonth, $day);
            $isToday = ($currentDate === $today);
            $isPast = ($currentDate < $today);
            $dayReservations = $reservationsByDate[$currentDate] ?? [];

            $dayClass = 'calendar-day';
            if ($isToday) $dayClass .= ' today';
            if ($isPast) $dayClass .= ' past';
            if (!empty($dayReservations)) $dayClass .= ' has-reservations';
        ?>
            <div class="<?php echo $dayClass; ?>" data-date="<?php echo $currentDate; ?>">
                <div class="calendar-day-header-row">
                    <div class="calendar-day-number"><?php echo $day; ?></div>
                    <div class="calendar-day-actions">
                        <?php if (!$isPast): ?>
                            <button class="calendar-add-btn" onclick="event.stopPropagation(); addReservationForDate('<?php echo $currentDate; ?>')" title="Add reservation">
                                <i class="fas fa-plus"></i>
                            </button>
                        <?php endif; ?>
                        <?php if (!empty($dayReservations)): ?>
                            <button class="calendar-view-btn" onclick="event.stopPropagation(); showDayDetails('<?php echo $currentDate; ?>')" title="View reservations">
                                <i class="fas fa-eye"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="calendar-day-content" onclick="showDayDetails('<?php echo $currentDate; ?>')">
                    <?php if (!empty($dayReservations)): ?>
                        <div class="reservation-count"><?php echo count($dayReservations); ?> booking<?php echo count($dayReservations) > 1 ? 's' : ''; ?></div>
                        <?php foreach (array_slice($dayReservations, 0, 3) as $reservation): ?>
                            <div class="calendar-reservation"
                                style="border-left-color: <?php echo $reservation['employee_color'] ?? '#007bff'; ?>"
                                onclick="event.stopPropagation(); viewReservation('<?php echo $reservation['id']; ?>')">
                                <div class="reservation-time"><?php echo date('H:i', strtotime($reservation['start_time'])); ?></div>
                                <div class="reservation-customer"><?php echo htmlspecialchars(substr($reservation['customer_name'], 0, 15)); ?></div>
                            </div>
                        <?php endforeach; ?>
                        <?php if (count($dayReservations) > 3): ?>
                            <div class="more-reservations">+<?php echo count($dayReservations) - 3; ?> more</div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php
        }
        ?>
    </div>
</div>

<!-- Day Details Modal (will be populated by JavaScript) -->
<div id="dayDetailsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="dayDetailsTitle">Day Details</h3>
            <button class="modal-close" onclick="closeDayDetails()">&times;</button>
        </div>
        <div class="modal-body" id="dayDetailsContent">
            <!-- Content will be loaded dynamically -->
        </div>
    </div>
</div>

<style>
    /* CSS Variables for Calendar */
    :root {
        --space-1: 4px;
        --space-2: 8px;
        --space-3: 12px;
        --space-4: 16px;
        --space-5: 20px;
        --space-6: 24px;
        --text-xs: 12px;
        --text-sm: 14px;
        --text-base: 16px;
        --text-lg: 18px;
        --text-xl: 20px;
        --text-2xl: 24px;
        --radius: 4px;
        --radius-lg: 8px;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --white: #ffffff;
        --gray-50: #f9fafb;
        --gray-100: #f3f4f6;
        --gray-200: #e5e7eb;
        --gray-500: #6b7280;
        --gray-600: #4b5563;
        --gray-700: #374151;
        --gray-800: #1f2937;
        --primary-50: #eff6ff;
        --primary-200: #bfdbfe;
        --primary-500: #3b82f6;
        --primary-600: #2563eb;
        --primary-700: #1d4ed8;
        --success: #10b981;
        --warning: #f59e0b;
        --transition: all 0.2s ease;
    }

    /* Mobile-First Calendar Navigation */
    .calendar-navigation {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
        margin-bottom: var(--space-5);
        padding: var(--space-4);
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
    }

    .calendar-nav-controls {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: var(--space-3);
    }

    .calendar-title {
        margin: 0;
        font-size: var(--text-lg);
        color: var(--primary-600);
        text-align: center;
        flex: 1;
    }

    .calendar-actions {
        display: flex;
        justify-content: center;
    }

    /* Mobile-specific navigation styles */
    .calendar-nav-btn {
        min-width: 44px;
        min-height: 44px;
        padding: var(--space-3);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--space-2);
        border: 1px solid var(--gray-200);
        background: var(--white);
        border-radius: var(--radius);
        color: var(--gray-700);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: var(--transition);
    }

    .calendar-nav-btn:hover,
    .calendar-nav-btn:focus {
        background: var(--gray-50);
        border-color: var(--primary-200);
        color: var(--primary-600);
    }

    .calendar-nav-btn:active {
        transform: scale(0.95);
        background: var(--primary-50);
    }

    .calendar-nav-btn .nav-text {
        display: none;
    }

    .btn-text {
        margin-left: var(--space-2);
    }

    /* Mobile: Hide text on small screens */
    @media (max-width: 480px) {
        .btn-text {
            display: none;
        }

        .calendar-title {
            font-size: var(--text-base);
        }

        .calendar-nav-btn {
            min-width: 40px;
            min-height: 40px;
            padding: var(--space-2);
        }
    }

    /* Tablet and up */
    @media (min-width: 768px) {
        .calendar-navigation {
            flex-direction: row;
            justify-content: space-between;
            align-items: center;
            gap: var(--space-5);
        }

        .calendar-title {
            font-size: var(--text-2xl);
            text-align: left;
        }

        .calendar-nav-controls {
            gap: var(--space-5);
        }

        .calendar-nav-btn .nav-text {
            display: inline;
        }

        .btn-text {
            display: inline;
            margin-left: var(--space-2);
        }
    }

    /* Mobile-First Calendar Container */
    .calendar-container {
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
        margin-bottom: var(--space-6);
    }

    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 1px;
        background: var(--gray-200);
    }

    .calendar-day-header {
        background: var(--primary-600);
        color: var(--white);
        padding: var(--space-2) var(--space-1);
        text-align: center;
        font-weight: 600;
        font-size: var(--text-xs);
        position: relative;
    }

    /* Mobile: Show single letter */
    .day-short {
        display: inline;
    }

    .day-full {
        display: none;
    }

    /* Tablet: Better layout */
    @media (min-width: 480px) and (max-width: 768px) {
        .calendar-day {
            min-height: 80px;
            padding: 6px 4px;
        }

        .calendar-reservation {
            font-size: 10px;
            padding: 2px 4px;
        }

        .reservation-time {
            font-size: 9px;
        }

        .calendar-header {
            flex-direction: row;
            align-items: center;
        }
    }

    /* Tablet and up: Show full day names */
    @media (min-width: 480px) {
        .day-short {
            display: none;
        }

        .day-full {
            display: inline;
        }

        .calendar-nav-btn .nav-text {
            display: inline;
        }
    }

    .calendar-day {
        background: var(--white);
        min-height: 80px;
        padding: var(--space-2);
        cursor: pointer;
        transition: all var(--transition);
        position: relative;
        border: 2px solid transparent;
    }

    /* Touch-friendly interactions */
    .calendar-day:hover,
    .calendar-day:focus {
        background: var(--gray-50);
        border-color: var(--primary-200);
    }

    .calendar-day:active {
        transform: scale(0.98);
        background: var(--primary-50);
    }

    /* Tablet and up - larger calendar cells */
    @media (min-width: 768px) {
        .calendar-day-header {
            padding: var(--space-4) var(--space-2);
            font-size: var(--text-sm);
        }

        .calendar-day {
            min-height: 120px;
            padding: var(--space-3);
        }
    }

    /* Desktop - even larger cells */
    @media (min-width: 1024px) {
        .calendar-day {
            min-height: 140px;
            padding: var(--space-4);
        }
    }

    /* Calendar Day States */
    .calendar-day.empty {
        background: var(--gray-100);
        cursor: default;
    }

    .calendar-day.empty:hover,
    .calendar-day.empty:focus {
        background: var(--gray-100);
        border-color: transparent;
        transform: none;
    }

    .calendar-day.today {
        background: var(--primary-50);
        border-color: var(--primary-500);
    }

    .calendar-day.today .calendar-day-number {
        color: var(--primary-700);
        font-weight: 700;
    }

    .calendar-day.past {
        opacity: 0.6;
    }

    .calendar-day.has-reservations {
        border-left: 4px solid var(--success);
    }

    /* Calendar Day Content */
    .calendar-day-header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--space-1);
    }

    .calendar-day-number {
        font-weight: 600;
        font-size: var(--text-sm);
        color: var(--gray-800);
    }

    .calendar-day-actions {
        display: flex;
        gap: var(--space-1);
    }

    .calendar-add-btn,
    .calendar-view-btn {
        background: none;
        border: none;
        padding: 4px;
        border-radius: var(--radius);
        cursor: pointer;
        transition: all var(--transition);
        font-size: 10px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .calendar-add-btn {
        color: var(--primary-600);
        background: var(--primary-50);
    }

    .calendar-add-btn:hover {
        background: var(--primary-100);
        color: var(--primary-700);
    }

    .calendar-view-btn {
        color: var(--gray-600);
        background: var(--gray-100);
    }

    .calendar-view-btn:hover {
        background: var(--gray-200);
        color: var(--gray-700);
    }

    .calendar-day-content {
        font-size: var(--text-xs);
        line-height: 1.3;
    }

    .reservation-count {
        color: var(--primary-600);
        font-weight: 600;
        margin-bottom: var(--space-1);
        font-size: var(--text-xs);
    }

    /* Mobile-optimized reservation items */
    .calendar-reservation {
        background: var(--gray-50);
        border-radius: var(--radius);
        padding: var(--space-1);
        margin-bottom: var(--space-1);
        border-left: 3px solid var(--primary-500);
        cursor: pointer;
        transition: all var(--transition);
        min-height: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
    }

    .calendar-reservation:hover,
    .calendar-reservation:focus {
        background: var(--primary-100);
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .calendar-reservation:active {
        transform: scale(0.95);
    }

    .reservation-time {
        font-weight: 600;
        color: var(--primary-700);
        font-size: 10px;
        line-height: 1.1;
    }

    .reservation-customer {
        color: var(--gray-600);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 9px;
        line-height: 1.1;
        max-width: 100%;
    }

    /* Mobile: Even more compact reservations */
    @media (max-width: 480px) {
        .calendar-container {
            margin: 0 -16px;
            border-radius: 0;
        }

        .calendar-header {
            padding: 12px 16px;
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
        }

        .calendar-nav-controls {
            justify-content: space-between;
        }

        .calendar-nav-btn {
            min-width: 44px;
            padding: 8px 12px;
        }

        .calendar-title {
            font-size: 1.25rem;
            text-align: center;
        }

        .calendar-day {
            min-height: 60px;
            padding: 4px 2px;
        }

        .calendar-day-number {
            font-size: 12px;
            margin-bottom: 2px;
        }

        .calendar-reservation {
            min-height: 14px;
            padding: 1px 3px;
            margin-bottom: 1px;
            font-size: 8px;
        }

        .reservation-time {
            font-size: 8px;
        }

        .reservation-customer {
            display: none;
            /* Hide customer name on very small screens */
        }

        .calendar-day-content {
            font-size: 9px;
        }

        .reservation-count {
            font-size: 8px;
            margin-bottom: 2px;
            padding: 1px 3px;
        }

        .more-reservations {
            font-size: 8px;
            padding: 2px 4px;
        }
    }

    .more-reservations {
        color: var(--gray-500);
        font-style: italic;
        text-align: center;
        padding: var(--space-1);
        font-size: var(--text-xs);
        background: var(--gray-100);
        border-radius: var(--radius-sm);
        margin-top: var(--space-1);
    }

    /* Tablet and up - larger content */
    @media (min-width: 768px) {
        .calendar-day-number {
            font-size: var(--text-base);
            margin-bottom: var(--space-2);
        }

        .calendar-day-content {
            font-size: var(--text-sm);
        }

        .reservation-count {
            font-size: var(--text-sm);
            margin-bottom: var(--space-2);
        }

        .calendar-reservation {
            padding: var(--space-2);
            margin-bottom: var(--space-2);
            min-height: 32px;
        }

        .reservation-time,
        .reservation-customer {
            font-size: var(--text-sm);
        }

        .more-reservations {
            font-size: var(--text-sm);
            padding: var(--space-2);
        }
    }

    /* Desktop - full size content */
    @media (min-width: 1024px) {
        .calendar-day-number {
            font-size: var(--text-lg);
        }

        .calendar-reservation {
            padding: var(--space-2) var(--space-3);
            min-height: 36px;
        }
    }

    /* Day Details Modal Styles */
    .day-reservations-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .day-reservation-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
        gap: 15px;
    }

    .day-reservation-item:last-child {
        border-bottom: none;
    }

    .reservation-time-slot {
        min-width: 120px;
        text-align: center;
    }

    .reservation-time-slot .time {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 5px;
    }

    .reservation-details {
        flex: 1;
    }

    .customer-name {
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 5px;
    }

    .service-info,
    .employee-info {
        color: #666;
        font-size: 0.9rem;
    }

    .reservation-actions {
        display: flex;
        gap: 8px;
    }

    .day-actions {
        padding: 15px;
        border-top: 1px solid #eee;
        text-align: center;
    }

    @media (max-width: 768px) {
        .calendar-grid {
            font-size: 0.8rem;
        }

        .calendar-day {
            min-height: 80px;
            padding: 5px;
        }

        .calendar-navigation {
            flex-direction: column;
            gap: 15px;
        }

        .calendar-nav-controls {
            flex-direction: column;
            gap: 10px;
        }

        .day-reservation-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .reservation-time-slot {
            min-width: auto;
            text-align: left;
        }

        .reservation-actions {
            width: 100%;
            justify-content: flex-end;
        }
    }
</style>

<script>
    function navigateMonth(year, month) {
        window.location.href = `/store-admin/?page=calendar&year=${year}&month=${month}`;
    }

    function goToToday() {
        const today = new Date();
        const year = today.getFullYear();
        const month = today.getMonth() + 1;
        window.location.href = `/store-admin/?page=calendar&year=${year}&month=${month}`;
    }

    function showDayDetails(date) {
        console.log('Showing details for date:', date);

        const formData = new FormData();
        formData.append('action', 'get_day_reservations');
        formData.append('date', date);

        makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
            console.log('AJAX response:', data);
            if (data.success) {
                document.getElementById('dayDetailsTitle').textContent = `Reservations for ${formatDateForDisplay(date)}`;
                document.getElementById('dayDetailsContent').innerHTML = generateDayDetailsHTML(data.reservations, date);
                const modal = document.getElementById('dayDetailsModal');
                modal.style.display = 'flex';
                modal.classList.add('show');
                document.body.style.overflow = 'hidden'; // Prevent background scrolling
            } else {
                console.error('Error loading day details:', data.error);
                showNotification('Error loading day details: ' + (data.error || 'Unknown error'), 'error');
            }
        }, function(error) {
            console.error('AJAX call failed:', error);
            showNotification('Failed to load day details', 'error');
        });
    }

    function closeDayDetails() {
        const modal = document.getElementById('dayDetailsModal');
        modal.classList.remove('show');
        setTimeout(() => {
            modal.style.display = 'none';
        }, 300); // Wait for transition to complete
        document.body.style.overflow = ''; // Restore scrolling
    }

    function formatDateForDisplay(dateStr) {
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    function generateDayDetailsHTML(reservations, date) {
        if (!reservations || reservations.length === 0) {
            return `
            <div class="empty-state">
                <i class="fas fa-calendar-times fa-2x text-muted"></i>
                <h4>No reservations for this day</h4>
                <button class="btn btn-primary" onclick="addReservationForDate('${date}')">
                    <i class="fas fa-plus"></i> Add Reservation
                </button>
            </div>
        `;
        }

        let html = '<div class="day-reservations-list">';

        reservations.forEach(reservation => {
            const statusClass = `status-${reservation.status}`;
            html += `
            <div class="day-reservation-item">
                <div class="reservation-time-slot">
                    <div class="time">${reservation.start_time} - ${reservation.end_time || 'N/A'}</div>
                    <span class="status-badge ${statusClass}">${reservation.status}</span>
                </div>
                <div class="reservation-details">
                    <div class="customer-name">${reservation.customer_name}</div>
                    <div class="service-info">${reservation.service_name} (${reservation.duration} min)</div>
                    ${reservation.employee_name ? `<div class="employee-info">with ${reservation.employee_name}</div>` : ''}
                </div>
                <div class="reservation-actions">
                    <button class="btn btn-sm btn-outline" onclick="viewReservation('${reservation.id}')">View</button>
                    <button class="btn btn-sm btn-primary" onclick="editReservation('${reservation.id}')">Edit</button>
                </div>
            </div>
        `;
        });

        html += '</div>';
        html += `
        <div class="day-actions">
            <button class="btn btn-primary" onclick="addReservationForDate('${date}')">
                <i class="fas fa-plus"></i> Add Reservation
            </button>
        </div>
    `;

        return html;
    }

    function addReservationForDate(date) {
        closeDayDetails();
        // Redirect to add reservation with pre-filled date
        window.location.href = `/store-admin/?page=add-reservation&date=${date}`;
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('dayDetailsModal');
        if (event.target === modal) {
            closeDayDetails();
        }
    });
</script>