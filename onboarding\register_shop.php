<?php
session_start();

// Initialize form data
$form_data = [
    'business_name' => '',
    'business_type' => '',
    'owner_name' => '',
    'email' => '',
    'phone' => '',
    'address' => '',
    'city' => '',
    'state' => '',
    'zip' => '',
    'country' => '',
    'plan' => isset($_GET['plan']) ? $_GET['plan'] : 'professional',
    'payment_method' => '',
    'agree_terms' => false
];

$errors = [];
$success_message = '';

// Handle form submission
if ($_POST) {
    // Validate form data
    $form_data = array_merge($form_data, $_POST);
    
    // Validation rules
    if (empty($form_data['business_name'])) {
        $errors['business_name'] = 'Business name is required';
    }
    
    if (empty($form_data['business_type'])) {
        $errors['business_type'] = 'Business type is required';
    }
    
    if (empty($form_data['owner_name'])) {
        $errors['owner_name'] = 'Owner name is required';
    }
    
    if (empty($form_data['email'])) {
        $errors['email'] = 'Email is required';
    } elseif (!filter_var($form_data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors['email'] = 'Please enter a valid email address';
    }
    
    if (empty($form_data['phone'])) {
        $errors['phone'] = 'Phone number is required';
    }
    
    if (empty($form_data['address'])) {
        $errors['address'] = 'Address is required';
    }
    
    if (empty($form_data['city'])) {
        $errors['city'] = 'City is required';
    }
    
    if (empty($form_data['state'])) {
        $errors['state'] = 'State is required';
    }
    
    if (empty($form_data['zip'])) {
        $errors['zip'] = 'ZIP code is required';
    }
    
    if (empty($form_data['country'])) {
        $errors['country'] = 'Country is required';
    }
    
    if (empty($form_data['payment_method'])) {
        $errors['payment_method'] = 'Payment method is required';
    }
    
    if (!isset($form_data['agree_terms']) || !$form_data['agree_terms']) {
        $errors['agree_terms'] = 'You must agree to the terms and conditions';
    }
    
    // If no errors, process registration
    if (empty($errors)) {
        // Store form data in session for payment processing
        $_SESSION['registration_data'] = $form_data;
        
        // Redirect to payment handler
        header('Location: payment_handler.php');
        exit;
    }
}

// Pricing plans
$plans = [
    'starter' => [
        'name' => 'Starter',
        'price' => 29,
        'features' => [
            'Up to 100 bookings/month',
            'Basic inventory management',
            'Customer management',
            'Email support',
            'Mobile app access'
        ]
    ],
    'professional' => [
        'name' => 'Professional',
        'price' => 79,
        'features' => [
            'Unlimited bookings',
            'Advanced inventory & analytics',
            'CRM & marketing tools',
            'Priority support',
            'Custom branding',
            'API access'
        ]
    ],
    'enterprise' => [
        'name' => 'Enterprise',
        'price' => 199,
        'features' => [
            'Everything in Professional',
            'Multi-location support',
            'Advanced integrations',
            'Dedicated account manager',
            'Custom development',
            'White-label solution'
        ]
    ]
];

$selected_plan = $plans[$form_data['plan']] ?? $plans['professional'];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register Your Business - GK Radevou</title>
    <link rel="stylesheet" href="assets/landing.css">
    <style>
        .registration-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-top: 100px;
            margin-bottom: 2rem;
        }
        
        .registration-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .registration-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .registration-header p {
            color: #666;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .form-section {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
        }
        
        .form-section h2 {
            color: #333;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
        }
        
        .form-group.error input,
        .form-group.error select {
            border-color: #dc3545;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .plan-summary {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            border: 2px solid #007bff;
            position: sticky;
            top: 2rem;
        }
        
        .plan-summary h3 {
            color: #007bff;
            margin-bottom: 1rem;
        }
        
        .plan-price {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 1rem;
        }
        
        .plan-features {
            list-style: none;
            margin-bottom: 1.5rem;
        }
        
        .plan-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
            color: #666;
        }
        
        .plan-features li:last-child {
            border-bottom: none;
        }
        
        .plan-features li:before {
            content: "✓";
            color: #28a745;
            margin-right: 0.5rem;
            font-weight: bold;
        }
        
        .payment-methods {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .payment-option {
            padding: 1rem;
            border: 2px solid #ddd;
            border-radius: 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .payment-option:hover {
            border-color: #007bff;
        }
        
        .payment-option.selected {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .payment-option input[type="radio"] {
            display: none;
        }
        
        .payment-option img {
            width: 40px;
            height: 40px;
            margin-bottom: 0.5rem;
        }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-top: 0.25rem;
        }
        
        .checkbox-group label {
            font-weight: normal;
            margin-bottom: 0;
        }
        
        .submit-section {
            grid-column: span 2;
            text-align: center;
            margin-top: 2rem;
        }
        
        .security-notice {
            background: #e8f5e8;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #155724;
        }
        
        .trial-notice {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .payment-methods {
                grid-template-columns: 1fr;
            }
            
            .submit-section {
                grid-column: span 1;
            }
            
            .registration-container {
                margin-top: 80px;
                margin-left: 1rem;
                margin-right: 1rem;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>GK Radevou</h2>
            </div>
            <div class="nav-actions">
                <a href="index.html" class="btn-secondary">Back to Home</a>
                <a href="demo_preview.php" class="btn-primary">Try Demo</a>
            </div>
        </div>
    </nav>

    <div class="registration-container">
        <div class="registration-header">
            <h1>Start Your Free Trial</h1>
            <p>Join thousands of businesses already using GK Radevou to grow their operations</p>
        </div>

        <form method="POST" class="registration-form">
            <div class="form-grid">
                <div class="form-content">
                    <div class="form-section">
                        <h2>Business Information</h2>
                        
                        <div class="form-group <?php echo isset($errors['business_name']) ? 'error' : ''; ?>">
                            <label for="business_name">Business Name *</label>
                            <input type="text" id="business_name" name="business_name" 
                                   value="<?php echo htmlspecialchars($form_data['business_name']); ?>" required>
                            <?php if (isset($errors['business_name'])): ?>
                                <div class="error-message"><?php echo $errors['business_name']; ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group <?php echo isset($errors['business_type']) ? 'error' : ''; ?>">
                            <label for="business_type">Business Type *</label>
                            <select id="business_type" name="business_type" required>
                                <option value="">Select Business Type</option>
                                <option value="salon" <?php echo $form_data['business_type'] === 'salon' ? 'selected' : ''; ?>>Salon & Spa</option>
                                <option value="restaurant" <?php echo $form_data['business_type'] === 'restaurant' ? 'selected' : ''; ?>>Restaurant</option>
                                <option value="fitness" <?php echo $form_data['business_type'] === 'fitness' ? 'selected' : ''; ?>>Fitness Center</option>
                                <option value="medical" <?php echo $form_data['business_type'] === 'medical' ? 'selected' : ''; ?>>Medical Practice</option>
                                <option value="consulting" <?php echo $form_data['business_type'] === 'consulting' ? 'selected' : ''; ?>>Consulting</option>
                                <option value="retail" <?php echo $form_data['business_type'] === 'retail' ? 'selected' : ''; ?>>Retail Store</option>
                                <option value="other" <?php echo $form_data['business_type'] === 'other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                            <?php if (isset($errors['business_type'])): ?>
                                <div class="error-message"><?php echo $errors['business_type']; ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="form-group <?php echo isset($errors['owner_name']) ? 'error' : ''; ?>">
                            <label for="owner_name">Owner/Manager Name *</label>
                            <input type="text" id="owner_name" name="owner_name" 
                                   value="<?php echo htmlspecialchars($form_data['owner_name']); ?>" required>
                            <?php if (isset($errors['owner_name'])): ?>
                                <div class="error-message"><?php echo $errors['owner_name']; ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="form-row">
                            <div class="form-group <?php echo isset($errors['email']) ? 'error' : ''; ?>">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($form_data['email']); ?>" required>
                                <?php if (isset($errors['email'])): ?>
                                    <div class="error-message"><?php echo $errors['email']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-group <?php echo isset($errors['phone']) ? 'error' : ''; ?>">
                                <label for="phone">Phone Number *</label>
                                <input type="tel" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($form_data['phone']); ?>" required>
                                <?php if (isset($errors['phone'])): ?>
                                    <div class="error-message"><?php echo $errors['phone']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Business Address</h2>
                        
                        <div class="form-group <?php echo isset($errors['address']) ? 'error' : ''; ?>">
                            <label for="address">Street Address *</label>
                            <input type="text" id="address" name="address" 
                                   value="<?php echo htmlspecialchars($form_data['address']); ?>" required>
                            <?php if (isset($errors['address'])): ?>
                                <div class="error-message"><?php echo $errors['address']; ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="form-row">
                            <div class="form-group <?php echo isset($errors['city']) ? 'error' : ''; ?>">
                                <label for="city">City *</label>
                                <input type="text" id="city" name="city" 
                                       value="<?php echo htmlspecialchars($form_data['city']); ?>" required>
                                <?php if (isset($errors['city'])): ?>
                                    <div class="error-message"><?php echo $errors['city']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-group <?php echo isset($errors['state']) ? 'error' : ''; ?>">
                                <label for="state">State/Province *</label>
                                <input type="text" id="state" name="state" 
                                       value="<?php echo htmlspecialchars($form_data['state']); ?>" required>
                                <?php if (isset($errors['state'])): ?>
                                    <div class="error-message"><?php echo $errors['state']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group <?php echo isset($errors['zip']) ? 'error' : ''; ?>">
                                <label for="zip">ZIP/Postal Code *</label>
                                <input type="text" id="zip" name="zip" 
                                       value="<?php echo htmlspecialchars($form_data['zip']); ?>" required>
                                <?php if (isset($errors['zip'])): ?>
                                    <div class="error-message"><?php echo $errors['zip']; ?></div>
                                <?php endif; ?>
                            </div>

                            <div class="form-group <?php echo isset($errors['country']) ? 'error' : ''; ?>">
                                <label for="country">Country *</label>
                                <select id="country" name="country" required>
                                    <option value="">Select Country</option>
                                    <option value="US" <?php echo $form_data['country'] === 'US' ? 'selected' : ''; ?>>United States</option>
                                    <option value="CA" <?php echo $form_data['country'] === 'CA' ? 'selected' : ''; ?>>Canada</option>
                                    <option value="UK" <?php echo $form_data['country'] === 'UK' ? 'selected' : ''; ?>>United Kingdom</option>
                                    <option value="AU" <?php echo $form_data['country'] === 'AU' ? 'selected' : ''; ?>>Australia</option>
                                    <option value="other" <?php echo $form_data['country'] === 'other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                                <?php if (isset($errors['country'])): ?>
                                    <div class="error-message"><?php echo $errors['country']; ?></div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Payment Method</h2>
                        
                        <div class="payment-methods">
                            <div class="payment-option <?php echo $form_data['payment_method'] === 'paypal' ? 'selected' : ''; ?>" 
                                 onclick="selectPayment('paypal')">
                                <input type="radio" id="paypal" name="payment_method" value="paypal" 
                                       <?php echo $form_data['payment_method'] === 'paypal' ? 'checked' : ''; ?>>
                                <div>💳</div>
                                <strong>PayPal</strong>
                            </div>

                            <div class="payment-option <?php echo $form_data['payment_method'] === 'stripe' ? 'selected' : ''; ?>" 
                                 onclick="selectPayment('stripe')">
                                <input type="radio" id="stripe" name="payment_method" value="stripe" 
                                       <?php echo $form_data['payment_method'] === 'stripe' ? 'checked' : ''; ?>>
                                <div>💰</div>
                                <strong>Credit Card</strong>
                            </div>
                        </div>

                        <?php if (isset($errors['payment_method'])): ?>
                            <div class="error-message"><?php echo $errors['payment_method']; ?></div>
                        <?php endif; ?>

                        <div class="trial-notice">
                            <strong>14-Day Free Trial</strong><br>
                            No payment required to start your trial. You'll be charged only after the trial period ends.
                        </div>

                        <div class="security-notice">
                            <strong>🔒 Secure Payment</strong><br>
                            Your payment information is encrypted and secure. We never store your card details.
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Terms & Conditions</h2>
                        
                        <div class="form-group <?php echo isset($errors['agree_terms']) ? 'error' : ''; ?>">
                            <div class="checkbox-group">
                                <input type="checkbox" id="agree_terms" name="agree_terms" value="1" 
                                       <?php echo $form_data['agree_terms'] ? 'checked' : ''; ?> required>
                                <label for="agree_terms">
                                    I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                    <a href="#" target="_blank">Privacy Policy</a> *
                                </label>
                            </div>
                            <?php if (isset($errors['agree_terms'])): ?>
                                <div class="error-message"><?php echo $errors['agree_terms']; ?></div>
                            <?php endif; ?>
                        </div>

                        <div class="checkbox-group">
                            <input type="checkbox" id="marketing_emails" name="marketing_emails" value="1">
                            <label for="marketing_emails">
                                I would like to receive marketing emails and product updates
                            </label>
                        </div>
                    </div>
                </div>

                <div class="plan-summary">
                    <h3><?php echo $selected_plan['name']; ?> Plan</h3>
                    <div class="plan-price">
                        $<?php echo $selected_plan['price']; ?><span style="font-size: 1rem; color: #666;">/month</span>
                    </div>
                    <ul class="plan-features">
                        <?php foreach ($selected_plan['features'] as $feature): ?>
                            <li><?php echo htmlspecialchars($feature); ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <p style="font-size: 0.9rem; color: #666; margin-bottom: 1rem;">
                        Start with a 14-day free trial. Cancel anytime.
                    </p>
                    <a href="#" onclick="document.querySelector('form').scrollIntoView();" class="btn-secondary" style="width: 100%; text-align: center;">
                        Different Plan?
                    </a>
                </div>

                <div class="submit-section">
                    <button type="submit" class="btn-primary btn-large">
                        Start Free Trial
                    </button>
                    <p style="margin-top: 1rem; color: #666; font-size: 0.9rem;">
                        You'll be redirected to secure payment processing
                    </p>
                </div>
            </div>

            <input type="hidden" name="plan" value="<?php echo htmlspecialchars($form_data['plan']); ?>">
        </form>
    </div>

    <script>
        function selectPayment(method) {
            document.querySelectorAll('.payment-option').forEach(option => {
                option.classList.remove('selected');
            });
            document.querySelector(`[onclick="selectPayment('${method}')"]`).classList.add('selected');
            document.getElementById(method).checked = true;
        }

        // Form validation
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.registration-form');
            const inputs = form.querySelectorAll('input[required], select[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
            });

            form.addEventListener('submit', function(e) {
                let isValid = true;
                inputs.forEach(input => {
                    if (!validateField(input)) {
                        isValid = false;
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                }
            });
        });

        function validateField(field) {
            const group = field.closest('.form-group');
            const errorDiv = group.querySelector('.error-message');
            
            if (field.value.trim() === '') {
                group.classList.add('error');
                if (!errorDiv) {
                    const error = document.createElement('div');
                    error.className = 'error-message';
                    error.textContent = 'This field is required';
                    group.appendChild(error);
                }
                return false;
            } else {
                group.classList.remove('error');
                if (errorDiv) {
                    errorDiv.remove();
                }
                return true;
            }
        }
    </script>
</body>
</html>
