RewriteEngine On

# Remove .php extension from URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^.]+)$ $1.php [NC,L]

# Friendly URLs for subsystems
RewriteRule ^client/?$ client/index.php [L]
RewriteRule ^store-admin/?$ store-admin/index.php [L]
RewriteRule ^system-admin/?$ system-admin/index.php [L]
RewriteRule ^onboarding/?$ onboarding/index.html [L]

# API routes
RewriteRule ^api/(.*)$ api/index.php [L]

# Security headers (only if mod_headers is available)
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Protect sensitive files
<Files "*.db">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "config.php">
    Require all denied
</Files>

# Block access to hidden files
<Files ".*">
    Require all denied
</Files>

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Error pages
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# Default directory index
DirectoryIndex index.php index.html
