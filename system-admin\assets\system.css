/* System Core Dashboard Styles - Enhanced UX/UI */

:root {
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #06b6d4;
  --light-color: #f8fafc;
  --dark-color: #1e293b;
  --border-color: #e2e8f0;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --border-radius: 8px;
  --border-radius-lg: 12px;
  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue",
    Arial, sans-serif;
  background-color: var(--light-color);
  color: var(--dark-color);
  line-height: 1.6;
  font-size: 14px;
  overflow-x: hidden;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Layout System */
.layout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: var(--sidebar-width);
  background: white;
  border-right: 1px solid var(--border-color);
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 100;
  transition: var(--transition);
  overflow-y: auto;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: var(--transition);
  min-height: 100vh;
  background: var(--light-color);
}

.sidebar.collapsed + .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* Sidebar Navigation */
.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-header h1 {
  color: var(--primary-color);
  font-size: 20px;
  font-weight: 700;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .sidebar-header h1 {
  opacity: 0;
  width: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.sidebar-toggle:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.sidebar-nav {
  padding: 16px 0;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  padding: 0 20px 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--secondary-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: var(--transition);
}

.sidebar.collapsed .nav-section-title {
  opacity: 0;
  height: 0;
  padding: 0;
  margin: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: var(--dark-color);
  text-decoration: none;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.nav-link.active {
  background: var(--primary-color);
  color: white;
}

.nav-link.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--primary-hover);
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-text {
  font-weight: 500;
  white-space: nowrap;
  transition: var(--transition);
}

.sidebar.collapsed .nav-text {
  opacity: 0;
  width: 0;
}

.nav-badge {
  background: var(--danger-color);
  color: white;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  transition: var(--transition);
}

.sidebar.collapsed .nav-badge {
  opacity: 0;
  width: 0;
}

/* Header */
.header {
  background: white;
  border-bottom: 1px solid var(--border-color);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 50;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--dark-color);
  margin: 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--secondary-color);
  font-size: 14px;
}

.breadcrumb-separator {
  color: var(--border-color);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-search {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  padding: 8px 12px 8px 40px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-color);
  width: 16px;
  height: 16px;
}

/* Main Content */
.main {
  padding: 24px;
  min-height: calc(100vh - 80px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-header-content h1 {
  color: var(--dark-color);
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: var(--secondary-color);
  font-size: 16px;
  margin: 0;
}

.page-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* Toolbar */
.toolbar {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-bar {
  position: relative;
  min-width: 300px;
}

.search-bar input {
  width: 100%;
  padding: 10px 16px 10px 44px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  transition: var(--transition);
}

.search-bar input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-bar .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-color);
  width: 16px;
  height: 16px;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--dark-color);
}

.view-toggle {
  display: flex;
  background: var(--light-color);
  border-radius: var(--border-radius);
  padding: 4px;
}

.view-toggle button {
  padding: 8px 12px;
  border: none;
  background: none;
  color: var(--secondary-color);
  border-radius: calc(var(--border-radius) - 2px);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  white-space: nowrap;
}

.view-toggle button.active {
  background: white;
  color: var(--primary-color);
  box-shadow: var(--shadow);
}

.view-toggle button:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
  color: var(--dark-color);
}

.bulk-actions {
  display: none;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  background: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  margin-bottom: 16px;
}

.bulk-actions.show {
  display: flex;
}

.bulk-actions-text {
  font-weight: 500;
}

.bulk-actions-buttons {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

/* Cards */
.card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  padding: 24px;
  margin-bottom: 24px;
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--primary-color);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card-title {
  color: var(--dark-color);
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.card-subtitle {
  color: var(--secondary-color);
  font-size: 14px;
  margin: 4px 0 0 0;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-body {
  margin-bottom: 20px;
}

.card-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: var(--primary-color);
}

.stat-label {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* Health Checks */
.health-checks {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.health-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.health-status {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
}

.health-status.healthy {
  background: var(--success-color);
}

.health-status.error {
  background: var(--danger-color);
}

/* Tenant Lists */
.tenant-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tenant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: var(--light-color);
  border-radius: var(--border-radius);
}

.tenant-info h3 {
  color: var(--dark-color);
  margin-bottom: 0.25rem;
}

.tenant-subdomain {
  color: var(--primary-color);
  font-size: 0.9rem;
}

.tenant-created {
  color: var(--secondary-color);
  font-size: 0.8rem;
}

/* Tenant Grid */
.tenant-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

.tenant-card {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  padding: 0;
  transition: var(--transition);
  overflow: inherit;
  position: relative;
}

.tenant-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.tenant-card.selected {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.tenant-card-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--border-color);
  position: relative;
}

.tenant-select {
  position: absolute;
  top: 16px;
  left: 16px;
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.tenant-status-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: var(--shadow);
}

.tenant-status-indicator.active {
  background: var(--success-color);
}

.tenant-status-indicator.inactive {
  background: var(--secondary-color);
}

.tenant-status-indicator.error {
  background: var(--danger-color);
}

.tenant-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--dark-color);
  margin: 0 40px 8px 40px;
  line-height: 1.3;
}

.tenant-subdomain {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 500;
  margin: 0 40px;
  text-decoration: none;
  transition: var(--transition);
}

.tenant-subdomain:hover {
  color: var(--primary-hover);
}

.tenant-card-body {
  padding: 20px 24px;
}

.tenant-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.info-label {
  color: var(--secondary-color);
  font-weight: 500;
}

.info-value {
  color: var(--dark-color);
  font-weight: 500;
  text-align: right;
}

.tenant-stats {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  margin: 20px 0;
  padding: 20px 0;
  border-top: 1px solid var(--border-color);
  border-bottom: 1px solid var(--border-color);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--secondary-color);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.tenant-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.tenant-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: var(--border-radius);
  margin: 16px 24px;
  font-size: 14px;
}

.tenant-footer {
  padding: 16px 24px;
  background: var(--light-color);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--secondary-color);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #f1f5f9;
  color: #475569;
}

.status-badge.error {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.warning {
  background: #fffbeb;
  color: #d97706;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background: white;
  color: var(--secondary-color);
  border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--light-color);
  border-color: var(--secondary-color);
}

.btn-success {
  background: var(--success-color);
  color: white;
  border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
  background: #059669;
  border-color: #059669;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
  border-color: var(--danger-color);
}

.btn-danger:hover:not(:disabled) {
  background: #dc2626;
  border-color: #dc2626;
}

.btn-warning {
  background: var(--warning-color);
  color: white;
  border-color: var(--warning-color);
}

.btn-warning:hover:not(:disabled) {
  background: #d97706;
  border-color: #d97706;
}

.btn-info {
  background: var(--info-color);
  color: white;
  border-color: var(--info-color);
}

.btn-info:hover:not(:disabled) {
  background: #0891b2;
  border-color: #0891b2;
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-color);
  color: white;
}

.btn-ghost {
  background: transparent;
  color: var(--secondary-color);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--light-color);
  color: var(--dark-color);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-lg {
  padding: 14px 24px;
  font-size: 16px;
}

.btn-icon {
  padding: 10px;
  width: 40px;
  height: 40px;
}

.btn-icon.btn-sm {
  padding: 6px;
  width: 32px;
  height: 32px;
}

/* Forms */
.form-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  margin-bottom: 2rem;
}

.form-section {
  margin-bottom: 2rem;
}

.form-section h2 {
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: var(--dark-color);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.2);
}

.input-group {
  display: flex;
}

.input-group input {
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group-text {
  background: var(--light-color);
  border: 1px solid var(--border-color);
  border-left: none;
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
  padding: 0.75rem;
  color: var(--secondary-color);
}

.form-text {
  color: var(--secondary-color);
  font-size: 0.8rem;
  margin-top: 0.25rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkbox-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

/* Alerts */
.alert {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.alert-info {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.alert-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Info Table */
.info-table table {
  width: 100%;
  border-collapse: collapse;
}

.info-table td {
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.info-table td:first-child {
  font-weight: 500;
  color: var(--secondary-color);
}

/* Dropdown */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  background: white;
  border: 1px solid var(--border-color);
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-toggle:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.dropdown-toggle::after {
  content: "▼";
  font-size: 10px;
  transition: var(--transition);
}

.dropdown.open .dropdown-toggle::after {
  transform: rotate(180deg);
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: calc(100% + 4px);
  right: 0;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  min-width: 180px;
  z-index: 1000;
  padding: 8px 0;
}

.dropdown.open .dropdown-menu {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  color: var(--dark-color);
  text-decoration: none;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  transition: var(--transition);
}

.dropdown-item:hover {
  background: var(--light-color);
  color: var(--primary-color);
}

.dropdown-item.text-danger {
  color: var(--danger-color);
}

.dropdown-item.text-danger:hover {
  background: #fef2f2;
  color: var(--danger-color);
}

.dropdown-divider {
  height: 1px;
  background: var(--border-color);
  border: none;
  margin: 8px 0;
}

.dropdown-header {
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  color: var(--secondary-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  margin: 5% auto;
  padding: 0;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--secondary-color);
}

.modal-body {
  padding: 1rem;
  max-height: 400px;
  overflow-y: auto;
}

/* Tools */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.tool-section {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
}

.tool-section h2 {
  color: var(--dark-color);
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.tool-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.stats-table {
  width: 100%;
  border-collapse: collapse;
}

.stats-table th,
.stats-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.stats-table th {
  background: var(--light-color);
  font-weight: 500;
  color: var(--dark-color);
}

.text-danger {
  color: var(--danger-color);
}

/* Help Section */
.help-section {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  margin-top: 2rem;
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.help-item {
  margin-bottom: 1rem;
}

.help-item h3 {
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.help-item p {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

.info-section {
  margin-top: 2rem;
}

.info-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.info-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 1.5rem;
}

.info-card h3 {
  color: var(--dark-color);
  margin-bottom: 1rem;
}

.info-card ul {
  list-style: none;
  padding-left: 0;
}

.info-card li {
  padding: 0.25rem 0;
  color: var(--secondary-color);
}

.info-card li:before {
  content: "•";
  color: var(--primary-color);
  margin-right: 0.5rem;
}

/* Success Info */
.success-info {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  padding: 2rem;
  margin-bottom: 2rem;
  border-left: 4px solid var(--success-color);
}

.success-info h2 {
  color: var(--success-color);
  margin-bottom: 1rem;
}

.tenant-details {
  margin: 1.5rem 0;
}

.tenant-details table {
  width: 100%;
  border-collapse: collapse;
}

.tenant-details td {
  padding: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.quick-actions {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--secondary-color);
}

.empty-state h2 {
  color: var(--dark-color);
  margin-bottom: 1rem;
}

/* Footer */
.footer {
  background: white;
  border-top: 1px solid var(--border-color);
  padding: 1rem 0;
  margin-top: 2rem;
}

.footer p {
  color: var(--secondary-color);
  text-align: center;
  margin: 0;
}

/* Log entries */
.log-entry {
  font-family: monospace;
  background: var(--light-color);
  padding: 0.25rem 0.5rem;
  margin-bottom: 0.25rem;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
}

/* Table View */
.table-view {
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background: var(--light-color);
  font-weight: 600;
  color: var(--dark-color);
  font-size: 14px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  font-size: 14px;
  color: var(--dark-color);
}

.table tr:hover {
  background: var(--light-color);
}

.table tr.selected {
  background: rgba(37, 99, 235, 0.05);
}

.table-checkbox {
  width: 40px;
  text-align: center;
}

.table-actions {
  width: 120px;
  text-align: right;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 80px 40px;
  color: var(--secondary-color);
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 24px;
  opacity: 0.5;
}

.empty-state h2 {
  color: var(--dark-color);
  margin-bottom: 12px;
  font-size: 20px;
  font-weight: 600;
}

.empty-state p {
  margin-bottom: 24px;
  font-size: 16px;
}

/* Loading States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .tenant-grid {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  .tenant-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  .main {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: stretch;
  }

  .view-toggle {
    order: -1; /* Move view toggle before other toolbar items on mobile */
    margin-bottom: 12px;
  }

  .view-toggle button {
    padding: 6px 10px;
    font-size: 13px;
  }

  .toolbar-right {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-bar {
    min-width: auto;
  }

  .tenant-grid {
    grid-template-columns: 1fr;
  }

  .tenant-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .page-actions {
    flex-direction: column;
  }

  .bulk-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .bulk-actions-buttons {
    margin-left: 0;
    margin-top: 12px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 12px;
  }

  .main {
    padding: 12px;
  }

  .card {
    padding: 16px;
  }

  .tenant-card-header,
  .tenant-card-body {
    padding: 16px;
  }

  .tenant-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .page-header-content h1 {
    font-size: 24px;
  }

  .btn {
    padding: 12px 16px;
    font-size: 14px;
  }

  .btn-sm {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* Factory Reset Specific Styles */
.help-text {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: var(--border-radius);
}

.help-text ul {
  margin-top: 0.5rem;
  margin-left: 1.5rem;
}

.help-text li {
  margin-bottom: 0.25rem;
  color: #856404;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 124, 186, 0.25);
}
