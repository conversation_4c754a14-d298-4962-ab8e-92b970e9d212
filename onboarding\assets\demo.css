* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    background: #f8f9fa;
    color: #333;
}

.demo-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.demo-header {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.demo-nav {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.demo-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.demo-logo h2 {
    color: #007bff;
    font-size: 1.5rem;
}

.demo-badge {
    background: #ff6b6b;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
}

.demo-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Content Layout */
.demo-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.demo-sidebar {
    width: 250px;
    background: white;
    box-shadow: 2px 0 4px rgba(0,0,0,0.1);
    padding: 1rem 0;
}

.demo-menu ul {
    list-style: none;
}

.menu-item {
    display: block;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    color: #666;
    transition: all 0.3s;
    border-left: 3px solid transparent;
}

.menu-item:hover {
    background: #f8f9fa;
    color: #007bff;
}

.menu-item.active {
    background: #e3f2fd;
    color: #007bff;
    border-left-color: #007bff;
}

.demo-main {
    flex: 1;
    overflow-y: auto;
    padding: 2rem;
}

/* Sections */
.demo-section {
    display: none;
}

.demo-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h1 {
    font-size: 2rem;
    color: #333;
}

.section-header p {
    color: #666;
    margin-top: 0.5rem;
}

/* Buttons */
.btn-primary {
    background: #007bff;
    color: white;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    cursor: pointer;
    display: inline-block;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: white;
    color: #007bff;
    padding: 0.75rem 1.5rem;
    border: 2px solid #007bff;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    cursor: pointer;
    display: inline-block;
}

.btn-secondary:hover {
    background: #007bff;
    color: white;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-small:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.btn-danger {
    color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    background: #dc3545;
    color: white;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stat-info h3 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

/* Dashboard Charts */
.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.chart-placeholder {
    display: flex;
    align-items: end;
    gap: 0.5rem;
    height: 200px;
}

.chart-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    flex: 1;
    min-height: 20px;
    border-radius: 4px 4px 0 0;
    animation: growBar 1s ease-out;
}

@keyframes growBar {
    from { height: 0; }
    to { height: var(--final-height); }
}

.service-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.service-count {
    font-weight: 600;
    color: #007bff;
}

/* Table */
.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.demo-table {
    width: 100%;
    border-collapse: collapse;
}

.demo-table th,
.demo-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.demo-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.demo-table tr:hover {
    background: #f8f9fa;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Inventory Grid */
.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.inventory-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.inventory-info h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.inventory-info p {
    color: #666;
    margin-bottom: 1rem;
}

.inventory-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #007bff;
}

.stock {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.stock-in_stock {
    background: #d4edda;
    color: #155724;
}

.stock-low_stock {
    background: #fff3cd;
    color: #856404;
}

.stock-out_of_stock {
    background: #f8d7da;
    color: #721c24;
}

.inventory-actions {
    display: flex;
    gap: 0.5rem;
}

/* Customers Grid */
.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.customer-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.customer-info h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.customer-info p {
    color: #666;
    margin-bottom: 0.25rem;
}

.customer-stats {
    margin: 1rem 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.stat {
    text-align: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.stat-value {
    display: block;
    font-weight: 600;
    color: #333;
}

.customer-actions {
    display: flex;
    gap: 0.5rem;
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.report-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.report-card h3 {
    margin-bottom: 0.5rem;
    color: #333;
}

.report-card p {
    color: #666;
    margin-bottom: 1rem;
}

.report-preview {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.report-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.settings-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.settings-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

.setting-item {
    margin-bottom: 1rem;
}

.setting-item label {
    display: block;
    margin-bottom: 0.5rem;
    color: #333;
    font-weight: 500;
}

.setting-item input[type="text"],
.setting-item input[type="email"],
.setting-item input[type="tel"] {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
}

.setting-item input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Modal */
.demo-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
}

.modal-content {
    background: white;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #333;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.close-modal:hover {
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body ul {
    margin-left: 1.5rem;
    margin-top: 1rem;
}

.modal-body li {
    margin-bottom: 0.5rem;
    color: #666;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Notification */
.demo-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.notification-close:hover {
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .demo-nav {
        padding: 0 1rem;
    }
    
    .demo-content {
        flex-direction: column;
    }
    
    .demo-sidebar {
        width: 100%;
        padding: 0.5rem 0;
    }
    
    .demo-menu ul {
        display: flex;
        overflow-x: auto;
        padding: 0 1rem;
    }
    
    .menu-item {
        white-space: nowrap;
        border-left: none;
        border-bottom: 3px solid transparent;
    }
    
    .menu-item.active {
        border-left: none;
        border-bottom-color: #007bff;
    }
    
    .demo-main {
        padding: 1rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-charts {
        grid-template-columns: 1fr;
    }
    
    .demo-table {
        font-size: 0.875rem;
    }
    
    .demo-table th,
    .demo-table td {
        padding: 0.75rem 0.5rem;
    }
    
    .inventory-grid,
    .customers-grid,
    .reports-grid,
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        margin: 5% auto;
        width: 95%;
    }
    
    .modal-footer {
        flex-direction: column;
    }
    
    .demo-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        font-size: 0.9rem;
    }
}
