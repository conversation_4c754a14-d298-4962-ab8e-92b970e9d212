<?php
session_start();

// Demo data
$demo_data = [
    'bookings' => [
        [
            'id' => 1,
            'customer' => '<PERSON>',
            'service' => 'Haircut & Styling',
            'date' => '2024-01-15',
            'time' => '10:00 AM',
            'status' => 'confirmed',
            'price' => '$45.00'
        ],
        [
            'id' => 2,
            'customer' => '<PERSON>',
            'service' => 'Manicure',
            'date' => '2024-01-15',
            'time' => '2:30 PM',
            'status' => 'pending',
            'price' => '$35.00'
        ],
        [
            'id' => 3,
            'customer' => '<PERSON>',
            'service' => 'Beard Trim',
            'date' => '2024-01-16',
            'time' => '11:15 AM',
            'status' => 'confirmed',
            'price' => '$25.00'
        ]
    ],
    'inventory' => [
        [
            'id' => 1,
            'name' => 'Premium Shampoo',
            'category' => 'Hair Care',
            'stock' => 15,
            'price' => '$12.99',
            'status' => 'in_stock'
        ],
        [
            'id' => 2,
            'name' => 'Styling Gel',
            'category' => 'Hair Care',
            'stock' => 3,
            'price' => '$8.50',
            'status' => 'low_stock'
        ],
        [
            'id' => 3,
            'name' => 'Nail Polish Set',
            'category' => 'Nail Care',
            'stock' => 0,
            'price' => '$24.99',
            'status' => 'out_of_stock'
        ]
    ],
    'customers' => [
        [
            'id' => 1,
            'name' => 'John Smith',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'visits' => 12,
            'total_spent' => '$540.00',
            'last_visit' => '2024-01-10'
        ],
        [
            'id' => 2,
            'name' => 'Sarah Johnson',
            'email' => '<EMAIL>',
            'phone' => '******-0456',
            'visits' => 8,
            'total_spent' => '$320.00',
            'last_visit' => '2024-01-08'
        ]
    ],
    'stats' => [
        'total_bookings' => 24,
        'pending_bookings' => 5,
        'revenue_today' => '$340.00',
        'revenue_month' => '$12,450.00',
        'customers_total' => 145,
        'inventory_items' => 89
    ]
];

// Handle AJAX requests for demo actions
if (isset($_GET['action']) && $_GET['action'] === 'demo_action') {
    header('Content-Type: application/json');
    
    $response = [
        'success' => true,
        'message' => 'Demo action completed successfully!',
        'data' => $demo_data
    ];
    
    echo json_encode($response);
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GK Radevou - Live Demo</title>
    <link rel="stylesheet" href="assets/demo.css">
</head>
<body>
    <div class="demo-container">
        <header class="demo-header">
            <div class="demo-nav">
                <div class="demo-logo">
                    <h2>GK Radevou</h2>
                    <span class="demo-badge">DEMO</span>
                </div>
                <div class="demo-actions">
                    <button class="btn-secondary" onclick="resetDemo()">Reset Demo</button>
                    <a href="register_shop.php" class="btn-primary">Sign Up Now</a>
                </div>
            </div>
        </header>

        <div class="demo-content">
            <aside class="demo-sidebar">
                <nav class="demo-menu">
                    <ul>
                        <li><a href="#" class="menu-item active" data-section="dashboard">📊 Dashboard</a></li>
                        <li><a href="#" class="menu-item" data-section="bookings">📅 Bookings</a></li>
                        <li><a href="#" class="menu-item" data-section="inventory">📦 Inventory</a></li>
                        <li><a href="#" class="menu-item" data-section="customers">👥 Customers</a></li>
                        <li><a href="#" class="menu-item" data-section="reports">📈 Reports</a></li>
                        <li><a href="#" class="menu-item" data-section="settings">⚙️ Settings</a></li>
                    </ul>
                </nav>
            </aside>

            <main class="demo-main">
                <!-- Dashboard Section -->
                <section id="dashboard" class="demo-section active">
                    <div class="section-header">
                        <h1>Dashboard Overview</h1>
                        <p>Welcome to your business management dashboard</p>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📅</div>
                            <div class="stat-info">
                                <h3><?php echo $demo_data['stats']['total_bookings']; ?></h3>
                                <p>Total Bookings</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⏰</div>
                            <div class="stat-info">
                                <h3><?php echo $demo_data['stats']['pending_bookings']; ?></h3>
                                <p>Pending Bookings</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-info">
                                <h3><?php echo $demo_data['stats']['revenue_today']; ?></h3>
                                <p>Today's Revenue</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">👥</div>
                            <div class="stat-info">
                                <h3><?php echo $demo_data['stats']['customers_total']; ?></h3>
                                <p>Total Customers</p>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-charts">
                        <div class="chart-card">
                            <h3>Monthly Revenue</h3>
                            <div class="chart-placeholder">
                                <div class="chart-bar" style="height: 60%"></div>
                                <div class="chart-bar" style="height: 80%"></div>
                                <div class="chart-bar" style="height: 45%"></div>
                                <div class="chart-bar" style="height: 90%"></div>
                                <div class="chart-bar" style="height: 75%"></div>
                            </div>
                        </div>
                        <div class="chart-card">
                            <h3>Popular Services</h3>
                            <div class="service-list">
                                <div class="service-item">
                                    <span>Haircut & Styling</span>
                                    <span class="service-count">45%</span>
                                </div>
                                <div class="service-item">
                                    <span>Manicure</span>
                                    <span class="service-count">25%</span>
                                </div>
                                <div class="service-item">
                                    <span>Beard Trim</span>
                                    <span class="service-count">20%</span>
                                </div>
                                <div class="service-item">
                                    <span>Facial</span>
                                    <span class="service-count">10%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Bookings Section -->
                <section id="bookings" class="demo-section">
                    <div class="section-header">
                        <h1>Booking Management</h1>
                        <button class="btn-primary" onclick="showDemo('add_booking')">Add New Booking</button>
                    </div>
                    
                    <div class="table-container">
                        <table class="demo-table">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Service</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Price</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($demo_data['bookings'] as $booking): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($booking['customer']); ?></td>
                                    <td><?php echo htmlspecialchars($booking['service']); ?></td>
                                    <td><?php echo htmlspecialchars($booking['date']); ?></td>
                                    <td><?php echo htmlspecialchars($booking['time']); ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $booking['status']; ?>">
                                            <?php echo ucfirst($booking['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($booking['price']); ?></td>
                                    <td>
                                        <button class="btn-small" onclick="showDemo('edit_booking')">Edit</button>
                                        <button class="btn-small btn-danger" onclick="showDemo('cancel_booking')">Cancel</button>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Inventory Section -->
                <section id="inventory" class="demo-section">
                    <div class="section-header">
                        <h1>Inventory Management</h1>
                        <button class="btn-primary" onclick="showDemo('add_product')">Add Product</button>
                    </div>
                    
                    <div class="inventory-grid">
                        <?php foreach ($demo_data['inventory'] as $item): ?>
                        <div class="inventory-card">
                            <div class="inventory-info">
                                <h3><?php echo htmlspecialchars($item['name']); ?></h3>
                                <p><?php echo htmlspecialchars($item['category']); ?></p>
                                <div class="inventory-details">
                                    <span class="price"><?php echo htmlspecialchars($item['price']); ?></span>
                                    <span class="stock stock-<?php echo $item['status']; ?>">
                                        Stock: <?php echo $item['stock']; ?>
                                    </span>
                                </div>
                            </div>
                            <div class="inventory-actions">
                                <button class="btn-small" onclick="showDemo('edit_product')">Edit</button>
                                <button class="btn-small" onclick="showDemo('restock')">Restock</button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </section>

                <!-- Customers Section -->
                <section id="customers" class="demo-section">
                    <div class="section-header">
                        <h1>Customer Management</h1>
                        <button class="btn-primary" onclick="showDemo('add_customer')">Add Customer</button>
                    </div>
                    
                    <div class="customers-grid">
                        <?php foreach ($demo_data['customers'] as $customer): ?>
                        <div class="customer-card">
                            <div class="customer-info">
                                <h3><?php echo htmlspecialchars($customer['name']); ?></h3>
                                <p><?php echo htmlspecialchars($customer['email']); ?></p>
                                <p><?php echo htmlspecialchars($customer['phone']); ?></p>
                            </div>
                            <div class="customer-stats">
                                <div class="stat">
                                    <span class="stat-label">Visits:</span>
                                    <span class="stat-value"><?php echo $customer['visits']; ?></span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">Total Spent:</span>
                                    <span class="stat-value"><?php echo htmlspecialchars($customer['total_spent']); ?></span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">Last Visit:</span>
                                    <span class="stat-value"><?php echo htmlspecialchars($customer['last_visit']); ?></span>
                                </div>
                            </div>
                            <div class="customer-actions">
                                <button class="btn-small" onclick="showDemo('edit_customer')">Edit</button>
                                <button class="btn-small" onclick="showDemo('view_history')">History</button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </section>

                <!-- Reports Section -->
                <section id="reports" class="demo-section">
                    <div class="section-header">
                        <h1>Reports & Analytics</h1>
                        <button class="btn-primary" onclick="showDemo('generate_report')">Generate Report</button>
                    </div>
                    
                    <div class="reports-grid">
                        <div class="report-card">
                            <h3>Monthly Revenue Report</h3>
                            <p>Detailed breakdown of monthly earnings</p>
                            <div class="report-preview">
                                <div class="report-stat">
                                    <span>This Month:</span>
                                    <strong><?php echo $demo_data['stats']['revenue_month']; ?></strong>
                                </div>
                                <div class="report-stat">
                                    <span>Growth:</span>
                                    <strong style="color: #28a745;">+15.2%</strong>
                                </div>
                            </div>
                        </div>
                        <div class="report-card">
                            <h3>Customer Analytics</h3>
                            <p>Customer behavior and retention insights</p>
                            <div class="report-preview">
                                <div class="report-stat">
                                    <span>Retention Rate:</span>
                                    <strong>85%</strong>
                                </div>
                                <div class="report-stat">
                                    <span>New Customers:</span>
                                    <strong>12</strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settings" class="demo-section">
                    <div class="section-header">
                        <h1>Settings</h1>
                        <p>Customize your business settings</p>
                    </div>
                    
                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Business Information</h3>
                            <div class="setting-item">
                                <label>Business Name:</label>
                                <input type="text" value="Demo Salon & Spa" readonly>
                            </div>
                            <div class="setting-item">
                                <label>Email:</label>
                                <input type="email" value="<EMAIL>" readonly>
                            </div>
                            <div class="setting-item">
                                <label>Phone:</label>
                                <input type="tel" value="******-DEMO" readonly>
                            </div>
                        </div>
                        <div class="settings-card">
                            <h3>Booking Settings</h3>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" checked disabled>
                                    Enable online booking
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" checked disabled>
                                    Send reminder emails
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" disabled>
                                    Require prepayment
                                </label>
                            </div>
                        </div>
                    </div>
                </section>
            </main>
        </div>

        <!-- Demo Modal -->
        <div id="demoModal" class="demo-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Demo Action</h2>
                    <button class="close-modal" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <p>This is a demo version. In the full version, you would be able to:</p>
                    <ul id="demoFeatures">
                        <li>Add, edit, and manage bookings</li>
                        <li>Track inventory and receive alerts</li>
                        <li>Manage customer information</li>
                        <li>Generate detailed reports</li>
                        <li>Customize settings and preferences</li>
                    </ul>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="closeModal()">Continue Demo</button>
                    <a href="register_shop.php" class="btn-primary">Get Full Access</a>
                </div>
            </div>
        </div>

        <!-- Demo Notification -->
        <div class="demo-notification">
            <div class="notification-content">
                <span class="notification-icon">ℹ️</span>
                <span class="notification-text">This is a demo environment. Your actions won't be saved.</span>
                <button class="notification-close" onclick="hideNotification()">&times;</button>
            </div>
        </div>
    </div>

    <script src="assets/demo.js"></script>
</body>
</html>
