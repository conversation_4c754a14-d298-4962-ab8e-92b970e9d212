<?php
/**
 * Add Missing Client Translations
 * Adds the client translations that were not detected by the discovery system
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

function addMissingClientTranslations(): void {
    echo "➕ Adding Missing Client Translations\n";
    echo str_repeat("=", 60) . "\n";

    try {
        Config::init();
        TenantManager::init();
        Translation::init();

        // Missing translations identified from the client interface
        $missingTranslations = [
            // Step navigation labels
            'step_category' => ['el' => 'Κατηγορία', 'en' => 'Category'],
            'step_service' => ['el' => 'Υπηρεσία', 'en' => 'Service'],
            'step_date' => ['el' => 'Ημερομηνία', 'en' => 'Date'],
            'step_time' => ['el' => 'Ώρα', 'en' => 'Time'],
            'step_details' => ['el' => 'Στοιχεία', 'en' => 'Details'],
            'step_verify' => ['el' => 'Επιβεβαίωση', 'en' => 'Verify'],
            'step_confirm' => ['el' => 'Ολοκλήρωση', 'en' => 'Confirm'],

            // Category selection
            'select_category' => ['el' => 'Επιλέξτε Κατηγορία', 'en' => 'Select Category'],
            'select_service_type' => ['el' => 'Επιλέξτε τον τύπο υπηρεσίας που αναζητάτε', 'en' => 'Select the type of service you are looking for'],

            // Service selection
            'select_service_you_want' => ['el' => 'Επιλέξτε την υπηρεσία που θέλετε να κλείσετε', 'en' => 'Select the service you want to book'],

            // Date selection
            'select_date' => ['el' => 'Επιλέξτε Ημερομηνία', 'en' => 'Select Date'],
            'select_preferred_date' => ['el' => 'Επιλέξτε την προτιμώμενη ημερομηνία', 'en' => 'Select your preferred date'],

            // Time selection
            'select_time' => ['el' => 'Επιλέξτε Ώρα', 'en' => 'Select Time'],
            'choose_preferred_time_slot' => ['el' => 'Επιλέξτε την προτιμώμενη ώρα', 'en' => 'Choose your preferred time slot'],
            'insufficient_time' => ['el' => 'Ανεπαρκής χρόνος', 'en' => 'Insufficient time'],
            'time_slot_unavailable' => ['el' => 'Η ώρα δεν είναι διαθέσιμη', 'en' => 'Time slot unavailable'],

            // Contact details
            'provide_contact_details' => ['el' => 'Παρακαλώ παρέχετε τα στοιχεία επικοινωνίας σας', 'en' => 'Please provide your contact details'],

            // Email verification
            'confirm_your_email' => ['el' => 'Επιβεβαιώστε το Email σας', 'en' => 'Confirm your Email'],
            'verification_code_sent' => ['el' => 'Στείλαμε κωδικό επιβεβαίωσης στο email', 'en' => 'We sent a verification code to your email'],
            'didnt_receive_code' => ['el' => 'Δεν λάβατε τον κωδικό;', 'en' => 'Didn\'t receive the code?'],
            'resend_code' => ['el' => 'Επαναποστολή κωδικού', 'en' => 'Resend code'],
            'verify' => ['el' => 'Επιβεβαίωση', 'en' => 'Verify'],

            // Success confirmation
            'appointment_booked_successfully' => ['el' => 'Το ραντεβού σας κλείστηκε επιτυχώς. Θα λάβετε email επιβεβαίωσης σύντομα.', 'en' => 'Your appointment has been booked successfully. You will receive a confirmation email shortly.'],
            'book_another_appointment' => ['el' => 'Κλείστε άλλο ραντεβού', 'en' => 'Book Another Appointment'],

            // Additional common texts
            'available_slots' => ['el' => 'Διαθέσιμες ώρες', 'en' => 'Available slots'],
            'no_slots_available' => ['el' => 'Δεν υπάρχουν διαθέσιμες ώρες', 'en' => 'No slots available'],
            'select_different_date' => ['el' => 'Επιλέξτε διαφορετική ημερομηνία', 'en' => 'Select a different date'],
            'booking_details' => ['el' => 'Στοιχεία ραντεβού', 'en' => 'Booking details'],
            'selected_service' => ['el' => 'Επιλεγμένη υπηρεσία', 'en' => 'Selected service'],
            'selected_date' => ['el' => 'Επιλεγμένη ημερομηνία', 'en' => 'Selected date'],
            'selected_time' => ['el' => 'Επιλεγμένη ώρα', 'en' => 'Selected time'],
            'total_duration' => ['el' => 'Συνολική διάρκεια', 'en' => 'Total duration'],
            'total_cost' => ['el' => 'Συνολικό κόστος', 'en' => 'Total cost'],
            'customer_information' => ['el' => 'Στοιχεία πελάτη', 'en' => 'Customer information'],
            'special_requests' => ['el' => 'Ειδικές απαιτήσεις', 'en' => 'Special requests'],
            'terms_and_conditions' => ['el' => 'Όροι και προϋποθέσεις', 'en' => 'Terms and conditions'],
            'privacy_policy' => ['el' => 'Πολιτική απορρήτου', 'en' => 'Privacy policy'],
            'cancel_booking' => ['el' => 'Ακύρωση ραντεβού', 'en' => 'Cancel booking'],
            'modify_booking' => ['el' => 'Τροποποίηση ραντεβού', 'en' => 'Modify booking'],
        ];

        $added = 0;
        $updated = 0;
        $skipped = 0;

        echo "Processing " . count($missingTranslations) . " missing translations...\n\n";

        foreach ($missingTranslations as $key => $values) {
            try {
                // Check if translation already exists
                $db = TenantManager::getDatabase();
                $existing = $db->fetchRow("
                    SELECT * FROM translations 
                    WHERE key = :key AND category = 'client'
                ", [':key' => $key]);

                if ($existing) {
                    // Update if values are different
                    if ($existing['value_el'] !== $values['el'] || $existing['value_en'] !== $values['en']) {
                        $success = Translation::save($key, $values['el'], $values['en'], 'client');
                        if ($success) {
                            echo "🔄 Updated: {$key}\n";
                            echo "   EL: {$values['el']}\n";
                            echo "   EN: {$values['en']}\n";
                            $updated++;
                        }
                    } else {
                        echo "⏭️  Skipped: {$key} (already exists with same values)\n";
                        $skipped++;
                    }
                } else {
                    // Add new translation
                    $success = Translation::save($key, $values['el'], $values['en'], 'client');
                    if ($success) {
                        echo "➕ Added: {$key}\n";
                        echo "   EL: {$values['el']}\n";
                        echo "   EN: {$values['en']}\n";
                        $added++;
                    } else {
                        echo "❌ Failed to add: {$key}\n";
                    }
                }

            } catch (Exception $e) {
                echo "❌ Error processing {$key}: " . $e->getMessage() . "\n";
            }
        }

        echo "\n📊 Results Summary\n";
        echo str_repeat("=", 60) . "\n";
        echo "Added: {$added}\n";
        echo "Updated: {$updated}\n";
        echo "Skipped: {$skipped}\n";
        echo "Total processed: " . ($added + $updated + $skipped) . "\n";

        // Verify translations work
        echo "\n🧪 Testing added translations...\n";
        $testKeys = ['step_category', 'select_category', 'appointment_booked_successfully'];
        
        foreach (['el', 'en'] as $lang) {
            Translation::setLanguage($lang);
            echo "Language: {$lang}\n";
            
            foreach ($testKeys as $testKey) {
                $value = t($testKey, 'NOT_FOUND', 'client');
                if ($value !== 'NOT_FOUND') {
                    echo "  ✅ {$testKey}: {$value}\n";
                } else {
                    echo "  ❌ {$testKey}: NOT FOUND\n";
                }
            }
        }

        echo "\n✅ Missing client translations have been added!\n";
        echo "These translations are now available in:\n";
        echo "- Client booking interface\n";
        echo "- Store Admin > Translations (for editing)\n";
        echo "- JavaScript translation system\n";

    } catch (Exception $e) {
        echo "❌ Failed to add missing translations: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Run the script
addMissingClientTranslations();
?>
