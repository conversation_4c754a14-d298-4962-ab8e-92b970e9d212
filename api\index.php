<?php

/**
 * API Router
 * Central routing system for all API endpoints with proper initialization
 */

// Include dependencies
require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/functions.php';
require_once __DIR__ . '/../store-admin/core/Application.php';

// Initialize systems
Config::init();
TenantManager::init();
Application::init();

// Set JSON response headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Get the requested endpoint from URL path
    $path = $_SERVER['REQUEST_URI'];
    $path = parse_url($path, PHP_URL_PATH);

    // Remove project path and /api/ prefix - handle different path formats
    $endpoint = preg_replace('#^.*/api/#', '', $path);
    $endpoint = trim($endpoint, '/');

    // Handle case where path might be just the filename
    if (empty($endpoint)) {
        $endpoint = 'index';
    }

    // Log API request
    error_log("API Router: endpoint=$endpoint, path=$path, REQUEST_URI=" . $_SERVER['REQUEST_URI']);
    logActivity("API request: $endpoint", 'info');

    // Route to appropriate handler
    switch ($endpoint) {
        case 'availability':
            include __DIR__ . '/availability.php';
            break;
            
        case 'services':
            include __DIR__ . '/services.php';
            break;
            
        case 'timeslots':
            error_log("API Router: Including timeslots.php");
            include __DIR__ . '/timeslots.php';
            break;

        case 'employees':
            include __DIR__ . '/employees.php';
            break;

        case 'categories':
            include __DIR__ . '/categories.php';
            break;
            
        case 'booking':
            include __DIR__ . '/booking.php';
            break;
            
        case 'verification':
            include __DIR__ . '/verification.php';
            break;

        case 'settings':
            include __DIR__ . '/settings.php';
            break;

        case 'debug':
            include __DIR__ . '/debug.php';
            break;

        case 'cleanup':
            include __DIR__ . '/cleanup.php';
            break;

        case 'test':
            successResponse([
                'message' => 'API Router is working',
                'request_uri' => $_SERVER['REQUEST_URI'],
                'parsed_path' => $path,
                'endpoint' => $endpoint,
                'server_name' => $_SERVER['SERVER_NAME'] ?? 'unknown',
                'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'unknown'
            ]);
            break;

        default:
            errorResponse('Endpoint not found', 404);
    }
    
} catch (Exception $e) {
    logActivity("API Error: " . $e->getMessage(), 'error');
    errorResponse('Internal server error', 500);
}
