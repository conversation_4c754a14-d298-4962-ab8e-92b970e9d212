<?php

/**
 * Availability API
 * Handles availability checking for services
 */

require_once __DIR__ . '/../shared/api_base.php';

class AvailabilityApi extends ApiBase
{
    private $checker;

    public function __construct()
    {
        parent::__construct();
        $this->initializeChecker();
    }

    private function initializeChecker(): void
    {
        try {
            // Include availability checker dependencies
            require_once __DIR__ . '/../shared/time_calculator.php';
            require_once __DIR__ . '/../shared/availability_checker.php';

            $this->checker = new AvailabilityChecker($this->db);
            error_log("AvailabilityChecker initialized successfully");
        } catch (Exception $e) {
            error_log("Failed to initialize AvailabilityChecker: " . $e->getMessage());
            $this->handleFatalError('Failed to initialize availability checker', $e);
        }
    }

    public function handleRequest(): void
    {
        try {
            $this->validateMethod(['GET', 'POST']);

            $params = $this->getParams();
            error_log("Availability API: handleRequest called with params: " . json_encode($params));
            $this->logActivity("Availability API called with: " . json_encode($params));

            $serviceId = $params['service_id'] ?? null;
            $date = $params['date'] ?? null;
            $startDate = $params['start_date'] ?? null;
            $year = $params['year'] ?? null;
            $month = $params['month'] ?? null;

            // Validate parameters
            if (!$serviceId || (!$date && !$startDate && !($year && $month))) {
                errorResponse('Missing required parameters: service_id and either date, start_date, or year+month');
            }

            // Validate service exists
            $service = $this->safeFetchRow(
                "SELECT * FROM services WHERE id = :id AND is_active = 1",
                [':id' => $serviceId]
            );

            if (!$service) {
                $this->logActivity("Service not found: $serviceId", 'warning');
                errorResponse("Service not found or inactive: $serviceId", 404);
            }

            // Handle different request types
            if ($year && $month) {
                $this->handleMonthlyAvailability($serviceId, $year, $month);
            } elseif ($date || $startDate) {
                $this->handleDailyAvailability($serviceId, $date ?: $startDate);
            }

        } catch (Exception $e) {
            $this->logActivity("Availability API error: " . $e->getMessage(), 'error');
            $this->handleFatalError('Failed to check availability', $e);
        }
    }

    private function handleMonthlyAvailability(string $serviceId, string $year, string $month): void
    {
        error_log("API: handleMonthlyAvailability called for service $serviceId, $year-$month");

        try {
            error_log("API: About to call checker->getMonthlyAvailability");
            // Use the proper monthly availability method from checker
            $availability = $this->checker->getMonthlyAvailability($serviceId, $year, $month);

            error_log("API: getMonthlyAvailability returned, checking format");
            // Debug: Check if we're getting the right format
            $firstEntry = array_slice($availability, 0, 1, true);
            error_log("Availability API: First entry format: " . json_encode($firstEntry));

            successResponse([
                'service_id' => $serviceId,
                'year' => $year,
                'month' => $month,
                'availability' => $availability
            ]);

        } catch (Exception $e) {
            error_log("Monthly availability error: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            $this->logActivity("Monthly availability error: " . $e->getMessage(), 'error');
            // Fallback to simple availability
            error_log("API: Using fallback method");
            $this->fallbackMonthlyAvailability($serviceId, $year, $month);
        }
    }

    private function handleDailyAvailability(string $serviceId, string $date): void
    {
        try {
            $timeSlots = $this->checker->getDetailedTimeSlotAvailability($serviceId, $date);

            successResponse([
                'service_id' => $serviceId,
                'date' => $date,
                'time_slots' => $timeSlots
            ]);

        } catch (Exception $e) {
            $this->logActivity("Daily availability error: " . $e->getMessage(), 'error');
            // Fallback to basic availability
            $this->fallbackDailyAvailability($serviceId, $date);
        }
    }

    private function fallbackMonthlyAvailability(string $serviceId, string $year, string $month): void
    {
        // Simple fallback - all days available (return boolean format expected by client)
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, (int)$month, (int)$year);
        $availability = [];

        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = sprintf('%04d-%02d-%02d', $year, $month, $day);
            // Return boolean true/false as expected by client JavaScript
            $availability[$date] = true;
        }

        successResponse([
            'service_id' => $serviceId,
            'year' => $year,
            'month' => $month,
            'availability' => $availability
        ]);
    }

    private function fallbackDailyAvailability(string $serviceId, string $date): void
    {
        // Simple fallback - basic time slots
        $timeSlots = [];
        for ($hour = 9; $hour <= 17; $hour++) {
            $time = sprintf('%02d:00', $hour);
            $timeSlots[$time] = ['available' => true, 'employee_id' => null];
        }

        successResponse([
            'service_id' => $serviceId,
            'date' => $date,
            'time_slots' => $timeSlots
        ]);
    }
}

// Initialize and handle request
try {
    $api = new AvailabilityApi();
    $api->handleRequest();
} catch (Exception $e) {
    error_log("Availability API fatal error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'API temporarily unavailable']);
}

// Initialize and handle request
try {
    $api = new AvailabilityApi();
    $api->handleRequest();
} catch (Exception $e) {
    error_log("Availability API fatal error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'API temporarily unavailable']);
}
