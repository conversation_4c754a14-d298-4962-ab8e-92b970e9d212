<?php

/**
 * Tenant Management System
 * Handles multi-tenant routing, context, and database management
 */

require_once __DIR__ . '/database.php';

class TenantManager
{
    private static ?string $currentTenant = null;
    private static ?Database $database = null;
    private static bool $initialized = false;

    /**
     * Initialize the tenant management system
     */
    public static function init(): void
    {
        if (self::$initialized) {
            return;
        }

        self::detectTenant();
        self::$initialized = true;
    }

    /**
     * Detect current tenant from hostname or query parameter
     */
    private static function detectTenant(): void
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Remove port if present
        $host = explode(':', $host)[0];

        // Check for subdomain
        $parts = explode('.', $host);

        // Handle subdomain detection (works for both production and localhost)
        if (count($parts) >= 3 || (count($parts) >= 2 && str_contains($host, 'localhost'))) {
            // Subdomain detected (e.g., realma.localhost or realma.skrtz.gr)
            // For localhost: 2 parts is enough (tenant.localhost)
            // For production: need 3 parts (tenant.domain.tld)
            $detectedTenant = $parts[0];

            // Skip 'www' as it's not a tenant
            if ($detectedTenant === 'www') {
                // www.domain.tld - treat as root domain
                self::$currentTenant = 'demo';

                // Redirect www.domain.tld to demo.domain.tld
                if (!str_contains($host, 'localhost')) {
                    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                    $baseDomain = implode('.', array_slice($parts, 1)); // Remove 'www'
                    $redirectUrl = $protocol . '://demo.' . $baseDomain;
                    header('Location: ' . $redirectUrl);
                    exit;
                }
            } else {
                // Check if tenant exists in database before setting it
                if (self::tenantExists($detectedTenant)) {
                    self::$currentTenant = $detectedTenant;
                } else {
                    // Tenant doesn't exist, use demo as fallback
                    self::$currentTenant = 'demo';

                    // Only redirect in production (not localhost)
                    if (!str_contains($host, 'localhost') && $detectedTenant !== 'demo') {
                        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                        $redirectUrl = $protocol . '://demo.' . implode('.', array_slice($parts, 1));
                        header('Location: ' . $redirectUrl);
                        exit;
                    }
                }
            }
        } elseif (isset($_GET['tenant'])) {
            // URL parameter fallback
            $paramTenant = $_GET['tenant'];
            if (self::tenantExists($paramTenant)) {
                self::$currentTenant = $paramTenant;
            } else {
                self::$currentTenant = 'demo';
            }
        } else {
            // Root domain access (e.g., skrtz.gr, domain.com)
            self::$currentTenant = 'demo';

            // For root domain access, redirect to demo subdomain
            // Only redirect in production (not localhost)
            if (!str_contains($host, 'localhost') && count($parts) >= 2) {
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                $redirectUrl = $protocol . '://demo.' . $host;
                header('Location: ' . $redirectUrl);
                exit;
            }
        }

        // Validate tenant name for security
        if (!self::isValidTenantName(self::$currentTenant)) {
            self::$currentTenant = 'demo';
        }
    }

    /**
     * Validate tenant name format and length
     */
    private static function isValidTenantName(string $name): bool
    {
        return preg_match('/^[a-zA-Z0-9_-]+$/', $name) && strlen($name) <= 50;
    }

    /**
     * Get the current base domain (without subdomain)
     */
    public static function getBaseDomain(): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Remove port if present
        $host = explode(':', $host)[0];

        // Split domain parts
        $parts = explode('.', $host);

        // For localhost or single-part domains, return as-is
        if (count($parts) <= 1 || str_contains($host, 'localhost')) {
            return $host;
        }

        // For subdomains, return the base domain (last 2 parts for most cases)
        if (count($parts) >= 2) {
            // Handle cases like subdomain.domain.com or subdomain.domain.co.uk
            // For now, assume simple case of domain.tld
            return implode('.', array_slice($parts, -2));
        }

        return $host;
    }

    /**
     * Get the current full domain with subdomain
     */
    public static function getCurrentDomain(): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        return explode(':', $host)[0]; // Remove port if present
    }

    /**
     * Check if tenant exists in system database (private version for internal use)
     */
    private static function tenantExists(string $tenantName): bool
    {
        if ($tenantName === 'demo') {
            return true; // Demo tenant always exists
        }

        try {
            return self::checkTenantInDatabase($tenantName);
        } catch (Exception $e) {
            // If system database doesn't exist or has issues, only allow demo tenant
            return false;
        }
    }

    /**
     * Helper method to check tenant in database
     */
    private static function checkTenantInDatabase(string $tenantName): bool
    {
        $systemDb = Database::getInstance('system');
        $result = $systemDb->fetchRow(
            "SELECT id FROM tenants WHERE subdomain = :subdomain AND status = 'active'",
            [':subdomain' => $tenantName]
        );
        return $result !== null;
    }

    /**
     * Get current tenant identifier
     */
    public static function getCurrentTenant(): ?string
    {
        return self::$currentTenant;
    }

    /**
     * Force re-detection of tenant (for API calls)
     */
    public static function forceRedetect(): void
    {
        self::$initialized = false;
        self::$currentTenant = null;
        self::init();
    }

    /**
     * Get database instance for current tenant
     */
    public static function getDatabase(): Database
    {
        if (self::$database === null) {
            self::$database = Database::tenantSafe(self::$currentTenant);
        }

        return self::$database;
    }

    /**
     * Get system database instance
     */
    public static function getSystemDatabase(): Database
    {
        return Database::getInstance('system');
    }

    public static function createTenant(array $tenantData, bool $installDummyData = false): array
    {
        $subdomain = $tenantData['subdomain'] ?? '';
        $businessName = $tenantData['business_name'] ?? '';
        $ownerName = $tenantData['owner_name'] ?? '';
        $ownerEmail = $tenantData['owner_email'] ?? '';
        $adminUsername = $tenantData['admin_username'] ?? $subdomain;
        $adminPassword = $tenantData['admin_password'] ?? $subdomain . '123';

        if (!self::isValidTenantName($subdomain)) {
            return ['success' => false, 'error' => 'Invalid subdomain format'];
        }

        if (empty($businessName)) {
            return ['success' => false, 'error' => 'Business name is required'];
        }

        try {
            $systemDb = self::getSystemDatabase();

            // Check if tenant already exists
            $existing = $systemDb->fetchRow(
                "SELECT id FROM tenants WHERE subdomain = :subdomain",
                [':subdomain' => $subdomain]
            );

            if ($existing) {
                return ['success' => false, 'error' => 'Subdomain already exists'];
            }

            // Create tenant record
            $systemDb->query(
                "INSERT INTO tenants (subdomain, business_name, owner_name, owner_email, status, created_at) VALUES (:subdomain, :business_name, :owner_name, :owner_email, :status, :created_at)",
                [
                    ':subdomain' => $subdomain,
                    ':business_name' => $businessName,
                    ':owner_name' => $ownerName,
                    ':owner_email' => $ownerEmail,
                    ':status' => 'active',
                    ':created_at' => date('Y-m-d H:i:s')
                ]
            );

            $tenantId = $systemDb->lastInsertId();

            // Create tenant database
            $tenantDb = Database::getInstance($subdomain);

            // Initialize default settings
            self::initializeTenantDefaults($tenantDb, $businessName, $adminUsername, $adminPassword);

            // Initialize complete translation system only if requested
            if ($installDummyData) {
                self::initializeTenantTranslations($subdomain);
            }

            return [
                'success' => true,
                'tenant_id' => $tenantId,
                'subdomain' => $subdomain,
                'admin_username' => $adminUsername,
                'admin_password' => $adminPassword
            ];
        } catch (Exception $e) {
            error_log("Failed to create tenant: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    private static function initializeTenantDefaults(Database $db, string $businessName = 'My Business', string $adminUsername = 'admin', string $adminPassword = 'admin123'): void
    {
        // Default settings
        $defaultSettings = [
            'business_name' => $businessName,
            'business_phone' => '',
            'business_email' => '',
            'business_address' => '',
            'default_language' => 'el',
            'timezone' => 'Europe/Athens',
            'booking_enabled' => '1',
            'verification_required' => '1',
            'email_notifications' => '1',
            'admin_username' => $adminUsername,
            'admin_password' => password_hash($adminPassword, PASSWORD_DEFAULT)
        ];

        foreach ($defaultSettings as $key => $value) {
            $db->query(
                "INSERT INTO settings (key, value) VALUES (:key, :value)",
                [':key' => $key, ':value' => $value]
            );
        }

        // Default business working hours (stored in settings)
        // Format: each day has an array of periods with start/end times
        $businessWorkingHours = [
            'monday' => [['start' => '09:00', 'end' => '17:00']],
            'tuesday' => [['start' => '09:00', 'end' => '17:00']],
            'wednesday' => [['start' => '09:00', 'end' => '17:00']],
            'thursday' => [['start' => '09:00', 'end' => '17:00']],
            'friday' => [['start' => '09:00', 'end' => '17:00']],
            'saturday' => [['start' => '09:00', 'end' => '15:00']],
            'sunday' => [] // Closed (empty array)
        ];

        $db->query(
            "INSERT INTO settings (key, value) VALUES (:key, :value)",
            [':key' => 'business_hours', ':value' => json_encode($businessWorkingHours)]
        );

        // Create translations table for new translation system
        $db->query("
            CREATE TABLE IF NOT EXISTS translations (
                id TEXT PRIMARY KEY,
                key TEXT NOT NULL,
                value_el TEXT NOT NULL DEFAULT '',
                value_en TEXT NOT NULL DEFAULT '',
                category TEXT NOT NULL DEFAULT 'general',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(key, category)
            )
        ");

        $db->query("CREATE INDEX IF NOT EXISTS idx_translations_category ON translations(category)");
        $db->query("CREATE INDEX IF NOT EXISTS idx_translations_key ON translations(key)");

        // Default admin user
        $hashedPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
        $db->query(
            "INSERT INTO admin_users (username, password_hash, email, created_at) VALUES (:username, :password, :email, :created_at)",
            [
                ':username' => $adminUsername,
                ':password' => $hashedPassword,
                ':email' => '<EMAIL>',
                ':created_at' => date('Y-m-d H:i:s')
            ]
        );
    }

    public static function deleteTenant($identifier): bool
    {
        try {
            $systemDb = self::getSystemDatabase();

            // Get tenant info - identifier could be ID or subdomain
            if (is_numeric($identifier)) {
                $tenant = $systemDb->fetchRow(
                    "SELECT * FROM tenants WHERE id = :id",
                    [':id' => $identifier]
                );
            } else {
                $tenant = $systemDb->fetchRow(
                    "SELECT * FROM tenants WHERE subdomain = :subdomain",
                    [':subdomain' => $identifier]
                );
            }

            if (!$tenant) {
                return false;
            }

            // Delete tenant record
            $systemDb->query(
                "DELETE FROM tenants WHERE id = :id",
                [':id' => $tenant['id']]
            );

            // Close any open database connections for this tenant
            Database::closeInstance($tenant['subdomain']);

            // Delete tenant database file
            $dbPath = __DIR__ . "/../data/tenants/{$tenant['subdomain']}.db";
            if (file_exists($dbPath)) {
                // On Windows, we might need to wait a moment for the file handle to be released
                $attempts = 0;
                while ($attempts < 5) {
                    if (@unlink($dbPath)) {
                        break;
                    }
                    $attempts++;
                    usleep(100000); // Wait 100ms
                }

                if (file_exists($dbPath)) {
                    error_log("Warning: Could not delete database file: $dbPath");
                }
            }

            return true;
        } catch (Exception $e) {
            error_log("Failed to delete tenant: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Initialize complete dummy data for new tenant
     */
    private static function initializeTenantTranslations(string $tenantId): void
    {
        try {
            // Use the dummy data system
            require_once __DIR__ . '/DummyData.php';

            // Get the tenant database directly without changing HTTP_HOST
            $tenantDb = Database::getInstance($tenantId);
            $dummyData = new DummyData();

            // Install complete business data with translations
            $businessResults = $dummyData->generateAll($tenantDb);

            $totalItems = array_sum($businessResults);

            error_log("Initialized complete dummy data for tenant '$tenantId': {$totalItems} total items");
        } catch (Exception $e) {
            error_log("Failed to initialize dummy data for tenant '$tenantId': " . $e->getMessage());
            // Don't fail tenant creation if dummy data initialization fails
        }
    }

    public static function listTenants(): array
    {
        try {
            $systemDb = self::getSystemDatabase();
            return $systemDb->fetchAll("SELECT * FROM tenants ORDER BY business_name");
        } catch (Exception $e) {
            error_log("Failed to list tenants: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Public method to check if tenant exists (for external use)
     */
    public static function tenantExistsPublic(string $subdomain): bool
    {
        try {
            return self::checkTenantInDatabase($subdomain);
        } catch (Exception $e) {
            return false;
        }
    }
}
