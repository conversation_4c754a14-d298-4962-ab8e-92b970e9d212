// Demo JavaScript functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize demo
    initializeDemo();
    
    // Setup menu navigation
    setupMenuNavigation();
    
    // Setup demo actions
    setupDemoActions();
    
    // Setup animations
    setupAnimations();
});

function initializeDemo() {
    // Show welcome message
    setTimeout(() => {
        showWelcomeMessage();
    }, 1000);
    
    // Initialize dashboard charts
    initializeCharts();
    
    // Setup auto-refresh for demo data
    setInterval(updateDemoData, 30000); // Update every 30 seconds
}

function setupMenuNavigation() {
    const menuItems = document.querySelectorAll('.menu-item');
    const sections = document.querySelectorAll('.demo-section');
    
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all menu items
            menuItems.forEach(menuItem => {
                menuItem.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Hide all sections
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            // Show target section
            const targetSection = this.getAttribute('data-section');
            const section = document.getElementById(targetSection);
            if (section) {
                section.classList.add('active');
                
                // Animate section entrance
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    section.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, 50);
            }
            
            // Track section view
            trackDemoEvent('section_view', targetSection);
        });
    });
}

function setupDemoActions() {
    // Demo action buttons
    const actionButtons = document.querySelectorAll('[onclick*="showDemo"]');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.getAttribute('onclick').match(/showDemo\('(.+?)'\)/)[1];
            showDemoModal(action);
        });
    });
}

function setupAnimations() {
    // Animate stats cards
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    });
    
    document.querySelectorAll('.stat-card, .inventory-card, .customer-card').forEach(card => {
        observer.observe(card);
    });
    
    // Animate chart bars
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach((bar, index) => {
        setTimeout(() => {
            bar.style.animation = `growBar 1s ease-out forwards`;
        }, index * 200);
    });
}

function showDemo(action) {
    const modal = document.getElementById('demoModal');
    const features = document.getElementById('demoFeatures');
    
    // Configure modal content based on action
    const actionConfig = {
        'add_booking': {
            title: 'Add New Booking',
            features: [
                'Create bookings with customer details',
                'Set service type and duration',
                'Schedule appointments with calendar integration',
                'Send automated confirmation emails',
                'Set up recurring appointments'
            ]
        },
        'edit_booking': {
            title: 'Edit Booking',
            features: [
                'Modify booking details',
                'Reschedule appointments',
                'Update service information',
                'Change booking status',
                'Add notes and comments'
            ]
        },
        'cancel_booking': {
            title: 'Cancel Booking',
            features: [
                'Cancel appointments with reason',
                'Automatic refund processing',
                'Send cancellation notifications',
                'Update calendar availability',
                'Track cancellation statistics'
            ]
        },
        'add_product': {
            title: 'Add Product',
            features: [
                'Add new inventory items',
                'Set pricing and categories',
                'Upload product images',
                'Configure stock alerts',
                'Set up supplier information'
            ]
        },
        'edit_product': {
            title: 'Edit Product',
            features: [
                'Update product information',
                'Modify pricing and availability',
                'Change product categories',
                'Update stock levels',
                'Manage product variants'
            ]
        },
        'restock': {
            title: 'Restock Product',
            features: [
                'Add inventory quantities',
                'Track supplier orders',
                'Set reorder points',
                'Generate purchase orders',
                'Update cost information'
            ]
        },
        'add_customer': {
            title: 'Add Customer',
            features: [
                'Create customer profiles',
                'Store contact information',
                'Set customer preferences',
                'Add loyalty program details',
                'Import customer data'
            ]
        },
        'edit_customer': {
            title: 'Edit Customer',
            features: [
                'Update customer details',
                'Modify contact information',
                'Change customer preferences',
                'Update loyalty status',
                'Add customer notes'
            ]
        },
        'view_history': {
            title: 'View Customer History',
            features: [
                'See booking history',
                'View purchase records',
                'Track customer preferences',
                'Generate customer reports',
                'Analyze customer behavior'
            ]
        },
        'generate_report': {
            title: 'Generate Report',
            features: [
                'Create custom reports',
                'Export to PDF/Excel',
                'Schedule automatic reports',
                'Set up report templates',
                'Share reports with team'
            ]
        }
    };
    
    const config = actionConfig[action] || {
        title: 'Demo Feature',
        features: [
            'Full functionality available in paid version',
            'Advanced customization options',
            'Integration with third-party services',
            'Real-time data synchronization',
            'Comprehensive reporting tools'
        ]
    };
    
    // Update modal content
    modal.querySelector('.modal-header h2').textContent = config.title;
    features.innerHTML = config.features.map(feature => `<li>${feature}</li>`).join('');
    
    // Show modal
    modal.style.display = 'block';
    
    // Track demo action
    trackDemoEvent('demo_action', action);
}

function showDemoModal(action) {
    showDemo(action);
}

function closeModal() {
    const modal = document.getElementById('demoModal');
    modal.style.display = 'none';
}

function resetDemo() {
    // Reset to dashboard
    document.querySelectorAll('.menu-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector('.menu-item[data-section="dashboard"]').classList.add('active');
    
    document.querySelectorAll('.demo-section').forEach(section => {
        section.classList.remove('active');
    });
    document.getElementById('dashboard').classList.add('active');
    
    // Show reset notification
    showNotification('Demo has been reset!', 'success');
    
    // Track reset action
    trackDemoEvent('demo_reset', 'dashboard');
}

function hideNotification() {
    const notification = document.querySelector('.demo-notification');
    if (notification) {
        notification.style.animation = 'slideOut 0.3s ease-out forwards';
        setTimeout(() => {
            notification.style.display = 'none';
        }, 300);
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `demo-notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            <span class="notification-text">${message}</span>
            <button class="notification-close" onclick="hideNotification()">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-out forwards';
        setTimeout(() => {
            if (notification.parentNode) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function showWelcomeMessage() {
    const welcomeModal = document.createElement('div');
    welcomeModal.className = 'demo-modal';
    welcomeModal.style.display = 'block';
    welcomeModal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Welcome to the GK Radevou Demo!</h2>
                <button class="close-modal" onclick="this.parentElement.parentElement.parentElement.remove()">&times;</button>
            </div>
            <div class="modal-body">
                <p>This is a fully interactive demo of the GK Radevou business management platform.</p>
                <p>Explore all the features:</p>
                <ul>
                    <li>📊 Dashboard with real-time analytics</li>
                    <li>📅 Booking and appointment management</li>
                    <li>📦 Inventory tracking and alerts</li>
                    <li>👥 Customer relationship management</li>
                    <li>📈 Detailed reports and insights</li>
                    <li>⚙️ Customizable settings</li>
                </ul>
                <p><strong>Note:</strong> This is a demo environment - your actions won't be saved.</p>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove()">Start Exploring</button>
                <a href="register_shop.php" class="btn-primary">Sign Up Now</a>
            </div>
        </div>
    `;
    
    document.body.appendChild(welcomeModal);
    
    // Track welcome view
    trackDemoEvent('welcome_shown', 'demo_start');
}

function initializeCharts() {
    // Animate chart bars with random heights
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach((bar, index) => {
        const height = Math.random() * 80 + 20; // Random height between 20-100%
        bar.style.setProperty('--final-height', `${height}%`);
        
        setTimeout(() => {
            bar.style.height = `${height}%`;
        }, index * 200);
    });
}

function updateDemoData() {
    // Simulate real-time data updates
    const stats = document.querySelectorAll('.stat-info h3');
    stats.forEach(stat => {
        const currentValue = parseInt(stat.textContent);
        const change = Math.floor(Math.random() * 10) - 5; // Random change between -5 and +5
        const newValue = Math.max(0, currentValue + change);
        
        if (change !== 0) {
            stat.textContent = newValue;
            stat.style.color = change > 0 ? '#28a745' : '#dc3545';
            
            setTimeout(() => {
                stat.style.color = '';
            }, 2000);
        }
    });
    
    // Update chart bars
    const chartBars = document.querySelectorAll('.chart-bar');
    chartBars.forEach(bar => {
        const newHeight = Math.random() * 80 + 20;
        bar.style.transition = 'height 0.5s ease';
        bar.style.height = `${newHeight}%`;
    });
}

function trackDemoEvent(action, category, label = '', value = 0) {
    // Demo analytics tracking
    const eventData = {
        action: action,
        category: category,
        label: label,
        value: value,
        timestamp: new Date().toISOString()
    };
    
    // Send to analytics service (in real implementation)
    // console.log('Demo Event:', eventData);
    
    // Store in localStorage for demo purposes
    const demoEvents = JSON.parse(localStorage.getItem('demoEvents') || '[]');
    demoEvents.push(eventData);
    localStorage.setItem('demoEvents', JSON.stringify(demoEvents.slice(-50))); // Keep last 50 events
}

// Table row interactions
document.addEventListener('click', function(e) {
    if (e.target.closest('.demo-table tr')) {
        const row = e.target.closest('tr');
        if (row && !row.querySelector('th')) {
            // Highlight selected row
            document.querySelectorAll('.demo-table tr').forEach(r => {
                r.classList.remove('selected');
            });
            row.classList.add('selected');
        }
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + 1-6 for quick section navigation
    if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '6') {
        e.preventDefault();
        const sections = ['dashboard', 'bookings', 'inventory', 'customers', 'reports', 'settings'];
        const sectionIndex = parseInt(e.key) - 1;
        
        if (sections[sectionIndex]) {
            const menuItem = document.querySelector(`[data-section="${sections[sectionIndex]}"]`);
            if (menuItem) {
                menuItem.click();
            }
        }
    }
    
    // Escape key to close modal
    if (e.key === 'Escape') {
        closeModal();
    }
});

// Modal click outside to close
document.addEventListener('click', function(e) {
    const modal = document.getElementById('demoModal');
    if (e.target === modal) {
        closeModal();
    }
});

// Simulate live notifications
setInterval(() => {
    const notifications = [
        'New booking received from John Doe',
        'Low stock alert: Premium Shampoo (3 left)',
        'Payment received: $45.00',
        'Appointment reminder sent to Sarah Johnson',
        'Customer review received: 5 stars!'
    ];
    
    if (Math.random() > 0.7) { // 30% chance every interval
        const randomNotification = notifications[Math.floor(Math.random() * notifications.length)];
        showNotification(randomNotification, 'info');
    }
}, 15000); // Check every 15 seconds

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .animate-in {
        animation: fadeInUp 0.5s ease-out;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .demo-table tr.selected {
        background-color: #e3f2fd !important;
        border-left: 3px solid #007bff;
    }
    
    .demo-table tr {
        transition: all 0.2s ease;
    }
    
    .stat-info h3 {
        transition: color 0.3s ease;
    }
    
    .chart-bar {
        transition: height 0.5s ease;
    }
`;
document.head.appendChild(style);

// Export demo utilities for external use
window.DemoUtils = {
    showNotification: showNotification,
    trackEvent: trackDemoEvent,
    resetDemo: resetDemo,
    closeModal: closeModal
};
