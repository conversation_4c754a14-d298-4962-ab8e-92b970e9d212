<?php

/**
 * Translation Management
 * Clean table interface for editing translations
 */

require_once __DIR__ . '/../controllers/TranslationController.php';

// Get current filters
$currentCategory = $_GET['category'] ?? 'all';
$searchQuery = $_GET['search'] ?? '';

// Debug: Log the current filters
error_log("Translations page - Category: {$currentCategory}, Search: {$searchQuery}");

// Get all categories
$categories = Translation::getCategories();

// Define client-only categories (editable in admin) - include dynamic content
$clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];

// Filter categories to show only client categories
$categories = array_filter($categories, function ($category) use ($clientCategories) {
    return in_array($category['category'], $clientCategories);
});

// Get translations based on filters (only client categories)
if ($currentCategory === 'all') {
    $translations = Translation::getClientTranslations();
} else if (in_array($currentCategory, $clientCategories)) {
    $translations = Translation::getAll($currentCategory);
} else {
    // If invalid category, show all client translations
    $translations = Translation::getClientTranslations();
    $currentCategory = 'all';
}

// Apply search filter
if (!empty($searchQuery)) {
    $translations = array_filter($translations, function ($translation) use ($searchQuery) {
        $search = strtolower($searchQuery);
        return strpos(strtolower($translation['key']), $search) !== false ||
            strpos(strtolower($translation['value_el']), $search) !== false ||
            strpos(strtolower($translation['value_en']), $search) !== false ||
            strpos(strtolower($translation['category']), $search) !== false;
    });
}

// Get current tenant for display
$currentTenant = TenantManager::getCurrentTenant();

// Debug mode
$debugMode = isset($_GET['debug']);
?>

<?php if ($debugMode): ?>
    <!DOCTYPE html>
    <html>

    <head>
        <title>Translations Debug</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
            }

            .debug {
                background: #f8f9fa;
                padding: 15px;
                margin: 10px 0;
                border: 1px solid #dee2e6;
            }

            .success {
                color: #28a745;
            }

            .error {
                color: #dc3545;
            }

            table {
                border-collapse: collapse;
                width: 100%;
                margin: 20px 0;
            }

            th,
            td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }

            th {
                background: #f8f9fa;
            }
        </style>
    </head>

    <body>
        <h1>Translations Debug Mode</h1>

        <div class="debug">
            <h3>System Status</h3>
            <p><strong>Tenant:</strong> <?= htmlspecialchars($currentTenant) ?></p>
            <p><strong>Categories:</strong> <?= count($categories) ?></p>
            <p><strong>Current Category:</strong> <?= htmlspecialchars($currentCategory) ?></p>
            <p><strong>Translations Found:</strong> <?= count($translations) ?></p>
            <p><strong>Search Query:</strong> "<?= htmlspecialchars($searchQuery) ?>"</p>
        </div>

        <?php if (!empty($translations)): ?>
            <h2>Translations Table</h2>
            <table>
                <thead>
                    <tr>
                        <th>Key</th>
                        <th>Category</th>
                        <th>Greek</th>
                        <th>English</th>
                    </tr>
                </thead>
                <tbody id="translationTableBody">
                    <?php foreach ($translations as $translation): ?>
                        <tr data-category="<?= htmlspecialchars($translation['category']) ?>">
                            <td><?= htmlspecialchars($translation['key']) ?></td>
                            <td><?= htmlspecialchars($translation['category']) ?></td>
                            <td><?= htmlspecialchars($translation['value_el']) ?></td>
                            <td><?= htmlspecialchars($translation['value_en']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php else: ?>
            <div class="debug error">
                <h3>No Translations Found</h3>
                <p>Current filter settings are not returning any results.</p>
            </div>
        <?php endif; ?>

        <div class="debug">
            <h3>Available Categories</h3>
            <ul>
                <?php foreach ($categories as $category): ?>
                    <li><?= htmlspecialchars($category['category']) ?> (<?= $category['count'] ?> translations)</li>
                <?php endforeach; ?>
            </ul>
        </div>

        <p><a href="?page=translations">← Back to normal view</a></p>
    </body>

    </html>
<?php exit;
endif; ?>

<!-- Page Header -->
<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-language"></i> <?= at('client_translations', 'Client Translations') ?></h1>
        <p><?= at('manage_client_translations', 'Manage translations that appear on your client booking interface') ?></p>
    </div>

    <div class="page-actions">
        <button class="btn btn-primary" onclick="saveAllTranslations()">
            <i class="fas fa-save"></i>
            <?= at('save_all_changes', 'Save All Changes') ?>
        </button>
    </div>
</div>

<!-- Filters -->
<div class="filters-section">
    <div class="filter-group">
        <label for="categoryFilter"><?= at('category', 'Category') ?>:</label>
        <select id="categoryFilter" class="form-control" onchange="applyFilter('category', this.value)">
            <option value="all" <?= $currentCategory === 'all' ? 'selected' : '' ?>><?= at('all_categories', 'All Categories') ?></option>
            <?php foreach ($categories as $category): ?>
                <option value="<?= htmlspecialchars($category['category']) ?>"
                    <?= $currentCategory === $category['category'] ? 'selected' : '' ?>>
                    <?= ucfirst(str_replace('_', ' ', $category['category'])) ?> (<?= $category['count'] ?>)
                </option>
            <?php endforeach; ?>
        </select>
    </div>

    <div class="filter-group">
        <label for="searchInput"><?= at('search', 'Search') ?>:</label>
        <input type="text" id="searchInput" class="form-control" placeholder="<?= at('search_translations', 'Search translations...') ?>"
            value="<?= htmlspecialchars($searchQuery) ?>" onkeyup="handleSearchInput()"
            onkeypress="if(event.key==='Enter') performSearch()">
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="performSearch()" style="margin-left: 5px;">
            <i class="fas fa-search"></i>
        </button>
        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSearch()" style="margin-left: 5px;" title="Clear search">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <div class="filter-stats">
        <span class="stats-item">
            <i class="fas fa-list"></i>
            <?= count($translations) ?> translations shown
        </span>
    </div>
</div>



<!-- Translation Table -->
<?php if (!empty($translations)): ?>
    <form method="POST" id="translationForm">
        <input type="hidden" name="action" value="save">
        <input type="hidden" name="current_category" value="<?= htmlspecialchars($currentCategory) ?>">
        <input type="hidden" name="current_search" value="<?= htmlspecialchars($searchQuery) ?>">
        <input type="hidden" name="csrf_token" value="<?= Application::generateCsrfToken() ?>">

        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th class="col-category">Category</th>
                        <th class="col-key">Key</th>
                        <th class="col-greek">Greek (Ελληνικά)</th>
                        <th class="col-english">English</th>
                        <th class="col-actions">Actions</th>
                    </tr>
                </thead>
                <tbody id="translationTableBody">
                    <?php foreach ($translations as $translation): ?>
                        <tr data-key="<?= htmlspecialchars($translation['key']) ?>"
                            data-category="<?= htmlspecialchars($translation['category']) ?>">
                            <td class="col-category">
                                <span class="category-badge category-<?= htmlspecialchars($translation['category']) ?>">
                                    <?= ucfirst(str_replace('_', ' ', $translation['category'])) ?>
                                </span>
                            </td>
                            <td class="col-key">
                                <code><?= htmlspecialchars($translation['key']) ?></code>
                            </td>
                            <td class="col-greek">
                                <textarea
                                    name="el_<?= htmlspecialchars($translation['key']) ?>"
                                    class="translation-input"
                                    rows="1"
                                    oninput="autoResize(this)"
                                    placeholder="Greek translation..."><?= htmlspecialchars($translation['value_el']) ?></textarea>
                                <!-- Hidden field to store category for this translation -->
                                <input type="hidden" name="category_<?= htmlspecialchars($translation['key']) ?>" value="<?= htmlspecialchars($translation['category']) ?>">
                            </td>
                            <td class="col-english">
                                <textarea
                                    name="en_<?= htmlspecialchars($translation['key']) ?>"
                                    class="translation-input"
                                    rows="1"
                                    oninput="autoResize(this)"
                                    placeholder="English translation..."><?= htmlspecialchars($translation['value_en']) ?></textarea>
                            </td>
                            <td class="col-actions">
                                <button type="button" class="btn btn-sm btn-outline-danger"
                                    onclick="deleteTranslation('<?= htmlspecialchars($translation['key']) ?>', '<?= htmlspecialchars($translation['category']) ?>')"
                                    title="Delete translation">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </form>
<?php else: ?>
    <div class="empty-state">
        <i class="fas fa-language fa-2x text-muted"></i>
        <h3>No translations found</h3>
        <p>No translations found for the selected category "<?= htmlspecialchars($currentCategory) ?>".</p>

    </div>
<?php endif; ?>



<script>
    // Client-side only category filter (no page refresh)
    function filterByCategoryClientSide(category) {
        try {
            // Clear search when changing category
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
            }

            // Filter rows by category (only client categories) - include all client-facing categories
            const rows = document.querySelectorAll('#translationTableBody tr');
            const clientCategories = ['general', 'booking_system', 'booking_flow', 'user_interface', 'services', 'categories', 'client', 'ui', 'dates'];
            let visibleCount = 0;

            rows.forEach(row => {
                const categoryCell = row.querySelector('.col-category .category-badge');
                if (!categoryCell) return; // Skip rows without category

                // Get actual category from data attribute or fallback to class name parsing
                const rowCategory = row.getAttribute('data-category') ||
                    categoryCell.textContent.trim().toLowerCase().replace(/\s+/g, '_');
                const targetCategory = category.toLowerCase();

                // Only show client categories
                const isClientCategory = clientCategories.includes(rowCategory);
                const matches = isClientCategory && ((targetCategory === 'all') || (rowCategory === targetCategory));

                row.style.display = matches ? '' : 'none';
                if (matches) visibleCount++;
            });

            updateFilterStats(visibleCount);

            // Update URL without refresh
            const currentUrl = new URL(window.location);
            if (category === 'all') {
                currentUrl.searchParams.delete('category');
            } else {
                currentUrl.searchParams.set('category', category);
            }
            currentUrl.searchParams.delete('search'); // Clear search when filtering
            window.history.replaceState({}, '', currentUrl.toString());

        } catch (error) {
            console.error('Error in filterByCategoryClientSide:', error);
        }
    }



    // Debounce function to prevent excessive URL updates
    let searchTimeout;

    function handleSearchInput() {
        clearTimeout(searchTimeout);

        // Immediate client-side filtering for responsive UI
        searchTranslations();

        // Debounced URL update to preserve search state
        searchTimeout = setTimeout(() => {
            updateSearchUrl();
        }, 1000); // Wait 1 second after user stops typing
    }

    function searchTranslations() {
        const searchInput = document.getElementById('searchInput');
        const searchTerm = searchInput.value.toLowerCase();
        const rows = document.querySelectorAll('#translationTableBody tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const key = row.querySelector('.col-key code').textContent.toLowerCase();
            const greek = row.querySelector('.col-greek textarea').value.toLowerCase();
            const english = row.querySelector('.col-english textarea').value.toLowerCase();
            const category = row.querySelector('.col-category .category-badge').textContent.toLowerCase();

            const matches = key.includes(searchTerm) ||
                greek.includes(searchTerm) ||
                english.includes(searchTerm) ||
                category.includes(searchTerm);

            row.style.display = matches ? '' : 'none';
            if (matches) visibleCount++;
        });

        // Update stats
        updateFilterStats(visibleCount);
    }

    function updateSearchUrl() {
        const searchInput = document.getElementById('searchInput');
        const searchTerm = searchInput.value;

        const url = new URL(window.location);
        url.searchParams.set('page', 'translations');

        if (searchTerm.trim()) {
            url.searchParams.set('search', searchTerm);
        } else {
            url.searchParams.delete('search');
        }

        // Update URL without reloading page
        window.history.replaceState({}, '', url.toString());
    }

    function performSearch() {
        clearTimeout(searchTimeout);
        updateSearchUrl();
        // No need to reload page since client-side filtering is already applied
    }

    function clearSearch() {
        const searchInput = document.getElementById('searchInput');
        searchInput.value = '';
        searchTranslations();
        updateSearchUrl();
    }

    function updateFilterStats(count) {
        const statsElement = document.querySelector('.filter-stats .stats-item');
        if (statsElement) {
            statsElement.innerHTML = `<i class="fas fa-list"></i> ${count} translations shown`;
        }
    }

    // Apply filter function (similar to admin.js)
    function applyFilter(filterType, value) {
        // Show loading overlay if available
        if (typeof showLoadingOverlay === 'function') {
            showLoadingOverlay('Applying filter...');
        }

        const url = new URL(window.location);

        if (value && value !== 'all') {
            url.searchParams.set(filterType, value);
        } else {
            url.searchParams.delete(filterType);
        }

        // Reset to first page when applying filters (if pagination exists)
        url.searchParams.delete('page_num');

        window.location.href = url.toString();
    }

    function autoResize(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.max(textarea.scrollHeight, 40) + 'px';
    }

    function saveAllTranslations() {
        // Show saving indicator
        const saveBtn = document.querySelector('.btn-primary');
        const originalText = saveBtn.innerHTML;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
        saveBtn.disabled = true;

        // Collect form data
        const formData = new FormData(document.getElementById('translationForm'));

        // Add AJAX flag and change action for the AJAX controller
        formData.append('ajax', '1');
        formData.set('action', 'save_translations');

        // Send AJAX request to the main store admin with AJAX flag
        fetch(window.location.pathname + window.location.search, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                // Restore button
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;

                if (data.success) {
                    // Show success message
                    showNotification('Translations saved successfully!', 'success');
                } else {
                    // Show error message
                    showNotification('Error saving translations: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                // Restore button
                saveBtn.innerHTML = originalText;
                saveBtn.disabled = false;

                // Show error message
                showNotification('Network error: ' + error.message, 'error');
                console.error('Save error:', error);
            });
    }

    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        notification.style.minWidth = '300px';
        notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

        // Add to page
        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    function deleteTranslation(key, category) {
        if (confirm('Are you sure you want to delete this translation?')) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="key" value="${key}">
            <input type="hidden" name="category" value="${category}">
            <input type="hidden" name="csrf_token" value="<?= Application::generateCsrfToken() ?>">
        `;
            document.body.appendChild(form);
            form.submit();
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Auto-resize all textareas on load
        document.querySelectorAll('.translation-input').forEach(autoResize);

        // Set up search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // Apply initial search if there's a search query in URL
            const urlParams = new URLSearchParams(window.location.search);
            const searchQuery = urlParams.get('search');
            if (searchQuery) {
                searchInput.value = searchQuery;
                searchTranslations();
            }
        }

        // Re-enable category filter after page load
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.disabled = false;
        }
    });
</script>

<style>
    .filters-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .filter-group label {
        font-weight: 500;
        min-width: 80px;
    }

    .filter-stats {
        display: flex;
        align-items: center;
    }

    .stats-item {
        display: flex;
        align-items: center;
        gap: 5px;
        color: #6c757d;
        font-size: 14px;
    }

    /* Use admin table styles */
    .table-container {
        margin: 20px 0;
    }

    .table th {
        background: #f8f9fa;
        font-weight: 600;
        color: #495057;
        padding: 12px;
        border-bottom: 2px solid #dee2e6;
    }

    .table td {
        padding: 12px;
        border-bottom: 1px solid #dee2e6;
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background-color: #f8f9fa;
    }

    .col-category {
        width: 15%;
    }

    .col-key {
        width: 25%;
    }

    .col-greek {
        width: 25%;
    }

    .col-english {
        width: 25%;
    }

    .col-actions {
        width: 10%;
    }

    .category-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        font-weight: 500;
        text-transform: uppercase;
    }

    .category-general {
        background: #e9ecef;
        color: #495057;
    }

    .category-booking_system {
        background: #d4edda;
        color: #155724;
    }

    .category-booking_flow {
        background: #d1ecf1;
        color: #0c5460;
    }

    .category-user_interface {
        background: #f8d7da;
        color: #721c24;
    }

    .category-services {
        background: #fff3cd;
        color: #856404;
    }

    .category-categories {
        background: #e2e3e5;
        color: #383d41;
    }

    .category-client {
        background: #cfe2ff;
        color: #0a58ca;
    }

    .category-ui {
        background: #e7f3ff;
        color: #0056b3;
    }

    .category-dates {
        background: #f0fff4;
        color: #0f5132;
    }

    .category-other {
        background: #f1f3f4;
        color: #5f6368;
    }

    .translation-input {
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 8px;
        font-size: 14px;
        resize: vertical;
        min-height: 40px;
        width: 100%;
        font-family: inherit;
    }

    .translation-input:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        outline: none;
    }

    .filter-group select,
    .filter-group input {
        min-width: 200px;
    }

    .stats-item i {
        color: #007bff;
    }

    .form-actions {
        padding: 20px;
        background: #f8f9fa;
        text-align: center;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .empty-state i {
        font-size: 48px;
        color: #6c757d;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        color: #495057;
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 30px;
    }

    .empty-state-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .empty-state-actions .btn {
        min-width: 150px;
    }
</style>