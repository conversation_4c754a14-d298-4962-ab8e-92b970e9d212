<?php

/**
 * Employee Form
 * Add/Edit employee form
 */

$isEdit = !empty($item);
$employee = $item;

// Get services
$services = $db->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC");

// Get assigned services if editing
$assignedServices = [];
if ($isEdit) {
    $assignedServices = $db->fetchAll("
        SELECT service_id FROM employee_services 
        WHERE employee_id = :employee_id
    ", [':employee_id' => $employee['id']]);
    $assignedServices = array_column($assignedServices, 'service_id');
}

// Parse working hours if editing
$workingHours = [];
if ($isEdit && $employee['working_hours']) {
    $workingHours = json_decode($employee['working_hours'], true) ?: [];
}

$days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
?>

<form method="POST" class="modal-form">
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
    <?php if ($isEdit): ?>
        <input type="hidden" name="id" value="<?php echo $employee['id']; ?>">
    <?php endif; ?>

    <div class="form-group">
        <label for="name">Employee Name *</label>
        <input type="text" id="name" name="name" required
            value="<?php echo $isEdit ? htmlspecialchars($employee['name']) : ''; ?>">
    </div>

    <div class="form-row">
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email"
                value="<?php echo $isEdit ? htmlspecialchars($employee['email']) : ''; ?>">
        </div>
        <div class="form-group">
            <label for="phone">Phone</label>
            <input type="tel" id="phone" name="phone"
                value="<?php echo $isEdit ? htmlspecialchars($employee['phone']) : ''; ?>">
        </div>
    </div>

    <div class="form-row">
        <div class="form-group">
            <label for="position">Position</label>
            <input type="text" id="position" name="position"
                value="<?php echo $isEdit ? htmlspecialchars($employee['position']) : ''; ?>">
        </div>
        <div class="form-group">
            <label for="color">Color</label>
            <input type="color" id="color" name="color"
                value="<?php echo $isEdit ? htmlspecialchars($employee['color'] ?: '#3498db') : '#3498db'; ?>">
        </div>
    </div>

    <!-- Working Hours -->
    <div class="form-section">
        <h4>Working Hours</h4>
        <div class="working-days">
            <?php foreach ($days as $day): ?>
                <?php
                $dayHours = $workingHours[$day] ?? [];
                $isWorking = !empty($dayHours);
                $startTime = $isWorking && !empty($dayHours[0]['start']) ? $dayHours[0]['start'] : '09:00';
                $endTime = $isWorking && !empty($dayHours[0]['end']) ? $dayHours[0]['end'] : '17:00';
                ?>
                <div class="day-hours">
                    <label class="day-checkbox">
                        <input type="checkbox" name="working_days[]" value="<?php echo $day; ?>"
                            <?php echo $isWorking ? 'checked' : ''; ?>
                            onchange="toggleDayHours('<?php echo $day; ?>')">
                        <strong><?php echo ucfirst($day); ?></strong>
                    </label>

                    <div class="day-times" id="<?php echo $day; ?>_times"
                        style="<?php echo $isWorking ? '' : 'display: none;'; ?>">
                        <div class="periods-container" id="<?php echo $day; ?>_employee_periods">
                            <?php
                            $periods = $dayHours;
                            if (empty($periods)) {
                                $periods = [['start' => '09:00', 'end' => '17:00']];
                            }
                            foreach ($periods as $index => $period):
                            ?>
                                <div class="time-period" data-period="<?php echo $index; ?>">
                                    <input type="time" name="<?php echo $day; ?>_start[]"
                                        value="<?php echo $period['start'] ?? '09:00'; ?>" required>
                                    <span>to</span>
                                    <input type="time" name="<?php echo $day; ?>_end[]"
                                        value="<?php echo $period['end'] ?? '17:00'; ?>" required>
                                    <button type="button" class="btn-remove-period" onclick="removeEmployeePeriod('<?php echo $day; ?>', <?php echo $index; ?>)">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        <button type="button" class="btn btn-secondary btn-sm" onclick="addEmployeePeriod('<?php echo $day; ?>')">
                            <i class="fas fa-plus"></i> Add Period
                        </button>
                        <small class="help-text">Add multiple periods for breaks (e.g., 9-14 and 17-21)</small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- Service Assignment -->
    <div class="form-section">
        <h4>Assign Services</h4>
        <div class="service-checkboxes">
            <?php if (empty($services)): ?>
                <p class="text-muted">No services available. <a href="/store-admin/?page=services&action=add">Add services first</a>.</p>
            <?php else: ?>
                <?php foreach ($services as $service): ?>
                    <label class="service-checkbox">
                        <input type="checkbox" name="service_ids[]" value="<?php echo $service['id']; ?>"
                            <?php echo in_array($service['id'], $assignedServices) ? 'checked' : ''; ?>>
                        <div class="service-info">
                            <span><?php echo htmlspecialchars($service['name']); ?></span>
                            <small><?php echo $service['duration']; ?> min - €<?php echo number_format($service['price'], 2); ?></small>
                        </div>
                    </label>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <div class="form-group">
        <div class="toggle-item">
            <div class="toggle-label">
                <div class="toggle-label-text">Active Employee</div>
                <div class="toggle-label-description">Employee can receive bookings and appear in scheduling</div>
            </div>
            <label class="toggle-switch">
                <input type="checkbox" name="is_active"
                    <?php echo (!$isEdit || $employee['is_active']) ? 'checked' : ''; ?>>
                <span class="toggle-slider"></span>
            </label>
        </div>
    </div>

    <div class="form-actions">
        <button type="submit" class="btn btn-primary">
            <?php echo $isEdit ? 'Update' : 'Create'; ?> Employee
        </button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">
            Cancel
        </button>
    </div>
</form>

<script>
    function toggleDayHours(day) {
        const checkbox = document.querySelector(`input[name="working_days[]"][value="${day}"]`);
        const timesDiv = document.getElementById(`${day}_times`);

        if (checkbox.checked) {
            timesDiv.style.display = 'flex';
        } else {
            timesDiv.style.display = 'none';
        }
    }
</script>

<style>
    .modal-form {
        max-width: 600px;
    }

    .working-days {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .day-hours {
        display: flex;
        align-items: center;
        gap: 15px;
        padding: 10px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: white;
    }

    .day-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
    }

    .day-checkbox input[type="checkbox"] {
        margin: 0;
    }

    .day-times {
        display: flex;
        flex-direction: column;
        gap: 10px;
        flex: 1;
    }

    .periods-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .time-period {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px;
        background: #f8f9fa;
        border-radius: 4px;
    }

    .time-period input[type="time"] {
        width: 120px;
    }

    .btn-remove-period {
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
    }

    .btn-remove-period:hover {
        background: #c82333;
    }

    .help-text {
        color: #6c757d;
        font-size: 12px;
        margin-top: 5px;
    }

    .service-checkboxes {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-height: 200px;
        overflow-y: auto;
    }

    .service-checkbox {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        background: white;
        cursor: pointer;
        transition: all 0.3s;
    }

    .service-checkbox:hover {
        background: var(--light-color);
    }

    .service-checkbox input[type="checkbox"] {
        margin: 0;
    }

    .service-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .service-info span {
        font-weight: 500;
    }

    .service-info small {
        color: var(--text-muted);
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
        }

        .day-hours {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>