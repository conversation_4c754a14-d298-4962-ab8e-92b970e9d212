<?php

/**
 * Tenant Management Interface
 * Interface for managing existing tenant instances with comprehensive administration tools
 */

require_once __DIR__ . '/config.php';

// Require system authentication
requireSystemAuth();

// Handle actions
$message = '';
$messageType = 'info';

// Handle redirect messages
if (isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $message = "Tenant deleted successfully";
    $messageType = 'success';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete_tenant':
                if (isset($_POST['tenant_id'])) {
                    $tenantId = $_POST['tenant_id'];
                    if (TenantManager::deleteTenant($tenantId)) {
                        $message = "Tenant deleted successfully";
                        $messageType = 'success';

                        // Redirect to prevent resubmission and ensure fresh data
                        header('Location: tenant_manager.php?deleted=1');
                        exit;
                    } else {
                        $message = "Failed to delete tenant";
                        $messageType = 'error';
                    }
                }
                break;
                
            case 'toggle_status':
                if (isset($_POST['tenant_id']) && isset($_POST['status'])) {
                    $tenantId = $_POST['tenant_id'];
                    $newStatus = $_POST['status'] === 'active' ? 'inactive' : 'active';
                    
                    $master = Database::master();
                    $result = $master->query(
                        "UPDATE tenants SET status = :status, updated_at = :updated_at WHERE id = :id",
                        [
                            ':status' => $newStatus,
                            ':updated_at' => date('Y-m-d H:i:s'),
                            ':id' => $tenantId
                        ]
                    );
                    
                    if ($result) {
                        $message = "Tenant status updated successfully";
                        $messageType = 'success';
                    } else {
                        $message = "Failed to update tenant status";
                        $messageType = 'error';
                    }
                }
                break;
                
            case 'backup_tenant':
                if (isset($_POST['tenant_id'])) {
                    $tenantId = $_POST['tenant_id'];
                    if (backupDatabase($tenantId)) {
                        $message = "Tenant database backed up successfully";
                        $messageType = 'success';
                    } else {
                        $message = "Failed to backup tenant database";
                        $messageType = 'error';
                    }
                }
                break;
        }
    }
}

// Get all tenants with statistics
$master = Database::master();
$tenants = $master->fetchAll("SELECT * FROM tenants ORDER BY created_at DESC");

// Enhanced tenant data with statistics
$tenantData = [];
foreach ($tenants as $tenant) {
    $tenantStats = [
        'customers' => 0,
        'bookings' => 0,
        'services' => 0,
        'employees' => 0,
        'db_size' => 0,
        'last_booking' => null
    ];
    
    try {
        $tenantDb = Database::tenant($tenant['subdomain']);

        // Get statistics
        $customers = $tenantDb->fetchRow("SELECT COUNT(*) as count FROM customers");
        $bookings = $tenantDb->fetchRow("SELECT COUNT(*) as count FROM reservations");
        $services = $tenantDb->fetchRow("SELECT COUNT(*) as count FROM services WHERE is_active = 1");
        $employees = $tenantDb->fetchRow("SELECT COUNT(*) as count FROM employees WHERE is_active = 1");
        $lastBooking = $tenantDb->fetchRow("SELECT MAX(created_at) as last_booking FROM reservations");
        
        $tenantStats['customers'] = $customers['count'] ?? 0;
        $tenantStats['bookings'] = $bookings['count'] ?? 0;
        $tenantStats['services'] = $services['count'] ?? 0;
        $tenantStats['employees'] = $employees['count'] ?? 0;
        $tenantStats['last_booking'] = $lastBooking['last_booking'] ?? null;
        
        // Get database size
        $dbPath = Config::getTenantDbPath($tenant['subdomain']);
        $tenantStats['db_size'] = getDatabaseSize($dbPath);
        
    } catch (Exception $e) {
        // Database might be corrupted or not accessible
        $tenantStats['error'] = $e->getMessage();
    }
    
    $tenantData[] = array_merge($tenant, $tenantStats);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SYSTEM_NAME ?> - Tenant Manager</title>
    <link rel="stylesheet" href="assets/system.css">
</head>
<body>
    <div class="layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><?= SYSTEM_NAME ?></h1>
                <button class="sidebar-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="index.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="tenant_manager.php" class="nav-link active">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span class="nav-text">Tenant Manager</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <a href="create_tenant.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        <span class="nav-text">Create Tenant</span>
                    </a>
                    <a href="dummy_data.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        <span class="nav-text">Dummy Data</span>
                    </a>
                    <a href="clean_db.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 7h16"></path>
                            <path d="M10 11v6"></path>
                            <path d="M14 11v6"></path>
                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                            <path d="M9 7V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                        </svg>
                        <span class="nav-text">Database Tools</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="?logout=1" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Tenant Manager</h1>
                    <div class="breadcrumb">
                        <span>System</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Tenant Manager</span>
                    </div>
                </div>
                <div class="header-right">
                    <a href="create_tenant.php" class="btn btn-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        Create Tenant
                    </a>
                </div>
            </header>

            <main class="main">
                <div class="container">
                    <?php if ($message): ?>
                        <div class="alert alert-<?= $messageType ?>">
                            <?= htmlspecialchars($message) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Toolbar -->
                    <div class="toolbar">
                        <div class="toolbar-left">
                            <div class="search-bar">
                                <input type="text" placeholder="Search tenants..." class="search-input">
                                <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <path d="m21 21-4.35-4.35"></path>
                                </svg>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Status:</label>
                                <select class="filter-select" data-filter="status">
                                    <option value="all">All</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>

                            <div class="filter-group">
                                <label class="filter-label">Date:</label>
                                <select class="filter-select" data-filter="dateRange">
                                    <option value="all">All Time</option>
                                    <option value="today">Today</option>
                                    <option value="week">This Week</option>
                                    <option value="month">This Month</option>
                                </select>
                            </div>
                        </div>

                        <div class="toolbar-right">
                            <div class="view-toggle">
                                <button data-view="cards" class="active">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="3" y="3" width="7" height="7"></rect>
                                        <rect x="14" y="3" width="7" height="7"></rect>
                                        <rect x="14" y="14" width="7" height="7"></rect>
                                        <rect x="3" y="14" width="7" height="7"></rect>
                                    </svg>
                                </button>
                                <button data-view="table">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                                        <line x1="9" y1="9" x2="21" y2="9"></line>
                                        <line x1="9" y1="15" x2="21" y2="15"></line>
                                        <line x1="3" y1="9" x2="3" y2="21"></line>
                                    </svg>
                                </button>
                            </div>

                            <button onclick="window.location.reload()" class="btn btn-secondary">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polyline points="23,4 23,10 17,10"></polyline>
                                    <path d="M20.49,15a9,9,0,1,1-2.12-9.36L23,10"></path>
                                </svg>
                                Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Bulk Actions Bar -->
                    <div class="bulk-actions">
                        <div class="bulk-actions-text">0 tenants selected</div>
                        <div class="bulk-actions-buttons">
                            <button class="btn btn-sm btn-secondary bulk-action-btn" data-action="activate">Activate</button>
                            <button class="btn btn-sm btn-secondary bulk-action-btn" data-action="deactivate">Deactivate</button>
                            <button class="btn btn-sm btn-info bulk-action-btn" data-action="backup">Backup</button>
                            <button class="btn btn-sm btn-warning bulk-action-btn" data-action="export">Export</button>
                            <button class="btn btn-sm btn-danger bulk-action-btn" data-action="delete">Delete</button>
                        </div>
                    </div>

                    <!-- Tenant Grid View -->
                    <div class="tenant-grid">
                        <?php if (empty($tenantData)): ?>
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
                                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="9" cy="7" r="4"></circle>
                                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                    </svg>
                                </div>
                                <h2>No Tenants Found</h2>
                                <p>Create your first tenant to get started with the system.</p>
                                <a href="create_tenant.php" class="btn btn-primary">Create First Tenant</a>
                            </div>
                        <?php else: ?>
                            <?php foreach ($tenantData as $tenant): ?>
                                <div class="tenant-card" data-tenant-id="<?= $tenant['id'] ?>">
                                    <div class="tenant-card-header">
                                        <input type="checkbox" class="tenant-select" value="<?= $tenant['id'] ?>">
                                        <div class="tenant-status-indicator <?= $tenant['status'] ?>"></div>
                                        <h3 class="tenant-title"><?= htmlspecialchars($tenant['business_name']) ?></h3>
                                        <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr" target="_blank" class="tenant-subdomain">
                                            <?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr
                                        </a>
                                    </div>

                                    <div class="tenant-card-body">
                                        <div class="tenant-info">
                                            <div class="info-row">
                                                <span class="info-label">Owner:</span>
                                                <span class="info-value"><?= htmlspecialchars($tenant['owner_name'] ?: 'N/A') ?></span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Email:</span>
                                                <span class="info-value"><?= htmlspecialchars($tenant['owner_email'] ?: 'N/A') ?></span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Created:</span>
                                                <span class="info-value"><?= date('M j, Y', strtotime($tenant['created_at'])) ?></span>
                                            </div>
                                            <div class="info-row">
                                                <span class="info-label">Status:</span>
                                                <span class="info-value">
                                                    <span class="status-badge <?= $tenant['status'] ?>">
                                                        <?= ucfirst($tenant['status']) ?>
                                                    </span>
                                                </span>
                                            </div>
                                        </div>

                                        <?php if (isset($tenant['error'])): ?>
                                            <div class="tenant-error">
                                                <strong>Database Error:</strong> <?= htmlspecialchars($tenant['error']) ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="tenant-stats">
                                                <div class="stat-item">
                                                    <div class="stat-value"><?= $tenant['customers'] ?></div>
                                                    <div class="stat-label">Users</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-value"><?= $tenant['bookings'] ?></div>
                                                    <div class="stat-label">Books</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-value"><?= $tenant['services'] ?></div>
                                                    <div class="stat-label">Serv</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-value"><?= $tenant['employees'] ?></div>
                                                    <div class="stat-label">Staff</div>
                                                </div>
                                                <div class="stat-item">
                                                    <div class="stat-value"><?= formatFileSize($tenant['db_size']) ?></div>
                                                    <div class="stat-label">Size</div>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <div class="tenant-actions">
                                            <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr/store-admin" target="_blank" class="btn btn-sm btn-primary">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                                                </svg>
                                                Admin
                                            </a>
                                            <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr" target="_blank" class="btn btn-sm btn-secondary">
                                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                                                    <polyline points="15,3 21,3 21,9"></polyline>
                                                    <line x1="10" y1="14" x2="21" y2="3"></line>
                                                </svg>
                                                Visit
                                            </a>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-ghost dropdown-toggle">
                                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                        <circle cx="12" cy="12" r="1"></circle>
                                                        <circle cx="12" cy="5" r="1"></circle>
                                                        <circle cx="12" cy="19" r="1"></circle>
                                                    </svg>
                                                </button>
                                                <div class="dropdown-menu">
                                                    <div class="dropdown-header">Actions</div>
                                                    <button onclick="toggleStatus('<?= $tenant['id'] ?>', '<?= $tenant['status'] ?>')" class="dropdown-item">
                                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <path d="M9 12l2 2 4-4"></path>
                                                            <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"></path>
                                                            <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"></path>
                                                        </svg>
                                                        <?= $tenant['status'] === 'active' ? 'Deactivate' : 'Activate' ?>
                                                    </button>
                                                    <button onclick="backupTenant('<?= $tenant['id'] ?>')" class="dropdown-item">
                                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                                            <polyline points="7,10 12,15 17,10"></polyline>
                                                            <line x1="12" y1="15" x2="12" y2="3"></line>
                                                        </svg>
                                                        Backup
                                                    </button>
                                                    <button onclick="viewLogs('<?= $tenant['id'] ?>')" class="dropdown-item">
                                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                            <polyline points="14,2 14,8 20,8"></polyline>
                                                        </svg>
                                                        View Logs
                                                    </button>
                                                    <div class="dropdown-divider"></div>
                                                    <button onclick="deleteTenant('<?= $tenant['id'] ?>', '<?= htmlspecialchars($tenant['business_name']) ?>')" class="dropdown-item text-danger">
                                                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                            <polyline points="3,6 5,6 21,6"></polyline>
                                                            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                                        </svg>
                                                        Delete
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if ($tenant['last_booking']): ?>
                                        <div class="tenant-footer">
                                            Last booking: <?= date('M j, Y', strtotime($tenant['last_booking'])) ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Table View (Hidden by default) -->
                    <div class="table-view" style="display: none;">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th class="table-checkbox">
                                        <input type="checkbox" id="select-all">
                                    </th>
                                    <th>Business Name</th>
                                    <th>Subdomain</th>
                                    <th>Status</th>
                                    <th>Owner</th>
                                    <th>Created</th>
                                    <th>Stats</th>
                                    <th class="table-actions">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tenantData as $tenant): ?>
                                    <tr data-tenant-id="<?= $tenant['id'] ?>">
                                        <td class="table-checkbox">
                                            <input type="checkbox" class="tenant-select" value="<?= $tenant['id'] ?>">
                                        </td>
                                        <td>
                                            <strong><?= htmlspecialchars($tenant['business_name']) ?></strong>
                                        </td>
                                        <td>
                                            <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr" target="_blank">
                                                <?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr
                                            </a>
                                        </td>
                                        <td>
                                            <span class="status-badge <?= $tenant['status'] ?>">
                                                <?= ucfirst($tenant['status']) ?>
                                            </span>
                                        </td>
                                        <td><?= htmlspecialchars($tenant['owner_name'] ?: 'N/A') ?></td>
                                        <td><?= date('M j, Y', strtotime($tenant['created_at'])) ?></td>
                                        <td>
                                            <small>
                                                <?= $tenant['customers'] ?> users,
                                                <?= $tenant['bookings'] ?> bookings
                                            </small>
                                        </td>
                                        <td class="table-actions">
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-ghost dropdown-toggle">Actions</button>
                                                <div class="dropdown-menu">
                                                    <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr/store-admin" target="_blank" class="dropdown-item">Open Admin</a>
                                                    <a href="https://<?= htmlspecialchars($tenant['subdomain']) ?>.skrtz.gr" target="_blank" class="dropdown-item">View Site</a>
                                                    <div class="dropdown-divider"></div>
                                                    <button onclick="toggleStatus('<?= $tenant['id'] ?>', '<?= $tenant['status'] ?>')" class="dropdown-item">
                                                        <?= $tenant['status'] === 'active' ? 'Deactivate' : 'Activate' ?>
                                                    </button>
                                                    <button onclick="backupTenant('<?= $tenant['id'] ?>')" class="dropdown-item">Backup</button>
                                                    <div class="dropdown-divider"></div>
                                                    <button onclick="deleteTenant('<?= $tenant['id'] ?>', '<?= htmlspecialchars($tenant['business_name']) ?>')" class="dropdown-item text-danger">Delete</button>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Hidden forms for actions -->
    <form id="actionForm" method="post" style="display: none;">
        <input type="hidden" name="action" id="actionType">
        <input type="hidden" name="tenant_id" id="tenantId">
        <input type="hidden" name="status" id="tenantStatus">
    </form>

    <!-- Logs Modal -->
    <div id="logsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Tenant Logs</h2>
                <button class="modal-close" onclick="SystemCore.closeModal('logsModal')">&times;</button>
            </div>
            <div class="modal-body">
                <div id="logsContent">Loading...</div>
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <script src="assets/system.js"></script>
    <script>
        function toggleStatus(tenantId, currentStatus) {
            const action = currentStatus === 'active' ? 'deactivate' : 'activate';
            if (confirm(`Are you sure you want to ${action} this tenant?`)) {
                document.getElementById('actionType').value = 'toggle_status';
                document.getElementById('tenantId').value = tenantId;
                document.getElementById('tenantStatus').value = currentStatus;
                document.getElementById('actionForm').submit();
            }
        }

        function backupTenant(tenantId) {
            if (confirm('Create a backup of this tenant\'s database?')) {
                document.getElementById('actionType').value = 'backup_tenant';
                document.getElementById('tenantId').value = tenantId;
                document.getElementById('actionForm').submit();
            }
        }

        function deleteTenant(tenantId, businessName) {
            if (confirm(`Are you sure you want to DELETE tenant "${businessName}"?\n\nThis action cannot be undone and will permanently delete all data.`)) {
                if (confirm(`Last chance! This will permanently delete all data for "${businessName}". Are you absolutely sure?`)) {
                    document.getElementById('actionType').value = 'delete_tenant';
                    document.getElementById('tenantId').value = tenantId;
                    document.getElementById('actionForm').submit();
                }
            }
        }

        function viewLogs(tenantId) {
            SystemCore.openModal('logsModal');
            document.getElementById('logsContent').innerHTML = '<div class="loading"><div class="spinner"></div></div>';

            // Simulate loading logs (in real implementation, this would be an AJAX call)
            setTimeout(() => {
                document.getElementById('logsContent').innerHTML =
                    '<div class="log-entry">[' + new Date().toISOString() + '] Tenant accessed</div>' +
                    '<div class="log-entry">[' + new Date().toISOString() + '] System check performed</div>' +
                    '<div class="log-entry">No recent log entries for this tenant</div>';
            }, 1000);
        }

        // Initialize enhanced features
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-update results count
            const updateResultsCount = () => {
                const visibleCards = document.querySelectorAll('.tenant-card:not([style*="display: none"])').length;
                const resultsElement = document.querySelector('.results-count');
                if (resultsElement) {
                    resultsElement.textContent = `${visibleCards} tenant${visibleCards !== 1 ? 's' : ''}`;
                }
            };

            // Initial count
            updateResultsCount();

            // Update count when filtering
            const observer = new MutationObserver(updateResultsCount);
            document.querySelectorAll('.tenant-card').forEach(card => {
                observer.observe(card, { attributes: true, attributeFilter: ['style'] });
            });
        });
    </script>
</body>
</html>
