<?php

/**
 * Reorder Categories View
 * Drag and drop interface for reordering categories
 */

// Get all categories ordered by current sort_order
$categories = $db->fetchAll("SELECT * FROM categories ORDER BY sort_order ASC, name ASC");

?>

<div class="page-header">
    <div class="page-title">
        <h1><i class="fas fa-sort"></i> Reorder Categories</h1>
        <p>Drag and drop categories to change their order</p>
    </div>
    <div class="page-actions">
        <a href="/store-admin/?page=categories" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Categories
        </a>
    </div>
</div>

<div class="content-wrapper">
    <div class="reorder-container">
        <div class="reorder-instructions">
            <div class="instruction-card">
                <i class="fas fa-info-circle"></i>
                <div>
                    <h3>How to reorder</h3>
                    <p>Drag categories up or down to change their order. The new order will be saved automatically when you click "Save Order".</p>
                </div>
            </div>
        </div>

        <form method="POST" id="reorder-form">
            <input type="hidden" name="category_order" id="category-order-input">
            <input type="hidden" name="csrf_token" value="<?= Application::generateCsrfToken() ?>">

            <div class="reorder-list" id="sortable-categories">
                <?php foreach ($categories as $index => $category): ?>
                    <div class="reorder-item" data-id="<?= $category['id'] ?>">
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>

                        <div class="category-info">
                            <div class="category-icon">
                                <i class="<?= htmlspecialchars($category['icon'] ?? 'fas fa-tag') ?>"></i>
                            </div>
                            <div class="category-details">
                                <div class="category-name">
                                    <?= htmlspecialchars($category['name']) ?>
                                    <?php if ($category['name_en']): ?>
                                        <span class="category-name-en">(<?= htmlspecialchars($category['name_en']) ?>)</span>
                                    <?php endif; ?>
                                </div>
                                <?php if ($category['description']): ?>
                                    <div class="category-description">
                                        <?= htmlspecialchars($category['description']) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="current-order">
                            <span class="order-number"><?= $index + 1 ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="reorder-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> Save Order
                </button>
                <a href="/store-admin/?page=categories" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<style>
    .reorder-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .reorder-instructions {
        margin-bottom: 2rem;
    }

    .instruction-card {
        background: var(--blue-50);
        border: 1px solid var(--blue-200);
        border-radius: 8px;
        padding: 1rem;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .instruction-card i {
        color: var(--blue-600);
        font-size: 1.25rem;
        margin-top: 0.25rem;
    }

    .instruction-card h3 {
        margin: 0 0 0.5rem 0;
        color: var(--blue-900);
        font-size: 1rem;
        font-weight: 600;
    }

    .instruction-card p {
        margin: 0;
        color: var(--blue-700);
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .reorder-list {
        background: white;
        border-radius: 8px;
        border: 1px solid var(--gray-200);
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .reorder-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid var(--gray-100);
        cursor: move;
        transition: background-color 0.2s ease;
    }

    .reorder-item:last-child {
        border-bottom: none;
    }

    .reorder-item:hover {
        background-color: var(--gray-50);
    }

    .reorder-item.sortable-ghost {
        opacity: 0.5;
    }

    .reorder-item.sortable-chosen {
        background-color: var(--blue-50);
    }

    .drag-handle {
        color: var(--gray-400);
        margin-right: 1rem;
        cursor: grab;
    }

    .drag-handle:active {
        cursor: grabbing;
    }

    .category-info {
        display: flex;
        align-items: center;
        flex: 1;
        gap: 1rem;
    }

    .category-icon {
        width: 40px;
        height: 40px;
        background: var(--gray-100);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-600);
    }

    .category-details {
        flex: 1;
    }

    .category-name {
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: 0.25rem;
    }

    .category-name-en {
        font-weight: 400;
        color: var(--gray-600);
        font-size: 0.875rem;
    }

    .category-description {
        color: var(--gray-600);
        font-size: 0.875rem;
        line-height: 1.4;
    }

    .current-order {
        margin-left: 1rem;
    }

    .order-number {
        background: var(--gray-100);
        color: var(--gray-700);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .reorder-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    @media (max-width: 768px) {
        .reorder-item {
            padding: 0.75rem;
        }

        .category-info {
            gap: 0.75rem;
        }

        .category-icon {
            width: 32px;
            height: 32px;
        }
    }
</style>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const sortableList = document.getElementById('sortable-categories');
        const orderInput = document.getElementById('category-order-input');
        const form = document.getElementById('reorder-form');

        // Initialize Sortable
        const sortable = Sortable.create(sortableList, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'sortable-chosen',
            onEnd: function() {
                updateOrderNumbers();
                updateHiddenInput();
            }
        });

        function updateOrderNumbers() {
            const items = sortableList.querySelectorAll('.reorder-item');
            items.forEach((item, index) => {
                const orderNumber = item.querySelector('.order-number');
                orderNumber.textContent = index + 1;
            });
        }

        function updateHiddenInput() {
            const items = sortableList.querySelectorAll('.reorder-item');
            const order = Array.from(items).map(item => item.dataset.id);
            orderInput.value = JSON.stringify(order);
        }

        // Initialize the hidden input with current order
        updateHiddenInput();

        // Form submission
        form.addEventListener('submit', function(e) {
            if (typeof showLoadingOverlay === 'function') {
                showLoadingOverlay('Saving order...');
            }
        });
    });
</script>