<?php

/**
 * Cleanup API
 * Removes duplicate services, employees, and other data
 */

// Include dependencies
require_once __DIR__ . '/../shared/config.php';
require_once __DIR__ . '/../shared/database.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/functions.php';

// Initialize systems
Config::init();
TenantManager::init();

// Set JSON response headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

try {
    $db = TenantManager::getDatabase();
    $currentTenant = TenantManager::getCurrentTenant();
    
    $results = [
        'tenant' => $currentTenant,
        'cleanup_actions' => []
    ];
    
    // Get counts before cleanup
    $beforeCounts = [
        'categories' => $db->fetchRow("SELECT COUNT(*) as count FROM categories")['count'],
        'services' => $db->fetchRow("SELECT COUNT(*) as count FROM services")['count'],
        'employees' => $db->fetchRow("SELECT COUNT(*) as count FROM employees")['count'],
        'employee_services' => $db->fetchRow("SELECT COUNT(*) as count FROM employee_services")['count']
    ];
    
    $results['before_cleanup'] = $beforeCounts;
    
    // Start transaction
    $db->beginTransaction();
    
    // 1. Remove duplicate categories (keep the one with most services)
    // First, get categories with their service counts
    $categoryData = $db->fetchAll("
        SELECT c.id, c.name, COUNT(s.id) as service_count
        FROM categories c
        LEFT JOIN services s ON c.id = s.category_id AND s.is_active = 1
        GROUP BY c.id, c.name
        ORDER BY c.name, service_count DESC, c.id ASC
    ");

    // Find duplicates and mark for deletion
    $toDelete = [];
    $seen = [];
    foreach ($categoryData as $cat) {
        if (isset($seen[$cat['name']])) {
            $toDelete[] = $cat['id'];
        } else {
            $seen[$cat['name']] = $cat['id'];
        }
    }

    // Delete duplicates
    $deletedCount = 0;
    foreach ($toDelete as $id) {
        $db->query("DELETE FROM categories WHERE id = :id", [':id' => $id]);
        $deletedCount++;
    }
    $results['cleanup_actions'][] = "Removed duplicate categories: " . $deletedCount;
    
    // 2. Remove duplicate services (keep oldest for each category)
    $duplicateServices = $db->query("
        DELETE FROM services 
        WHERE id NOT IN (
            SELECT MIN(id) 
            FROM services 
            GROUP BY category_id, name
        )
    ");
    $results['cleanup_actions'][] = "Removed duplicate services: " . $duplicateServices->rowCount();
    
    // 3. Remove duplicate employees (keep oldest)
    $duplicateEmployees = $db->query("
        DELETE FROM employees 
        WHERE id NOT IN (
            SELECT MIN(id) 
            FROM employees 
            GROUP BY name
        )
    ");
    $results['cleanup_actions'][] = "Removed duplicate employees: " . $duplicateEmployees->rowCount();
    
    // 4. Clean up orphaned employee_services relationships
    $orphanedRelations = $db->query("
        DELETE FROM employee_services 
        WHERE employee_id NOT IN (SELECT id FROM employees) 
        OR service_id NOT IN (SELECT id FROM services)
    ");
    $results['cleanup_actions'][] = "Removed orphaned employee-service relations: " . $orphanedRelations->rowCount();
    
    // 5. Remove duplicate employee_services (keep one per employee-service pair)
    $duplicateRelations = $db->query("
        DELETE FROM employee_services 
        WHERE rowid NOT IN (
            SELECT MIN(rowid) 
            FROM employee_services 
            GROUP BY employee_id, service_id
        )
    ");
    $results['cleanup_actions'][] = "Removed duplicate employee-service relations: " . $duplicateRelations->rowCount();
    
    // Commit transaction
    $db->commit();
    
    // Get counts after cleanup
    $afterCounts = [
        'categories' => $db->fetchRow("SELECT COUNT(*) as count FROM categories")['count'],
        'services' => $db->fetchRow("SELECT COUNT(*) as count FROM services")['count'],
        'employees' => $db->fetchRow("SELECT COUNT(*) as count FROM employees")['count'],
        'employee_services' => $db->fetchRow("SELECT COUNT(*) as count FROM employee_services")['count']
    ];
    
    $results['after_cleanup'] = $afterCounts;
    $results['success'] = true;
    
    logActivity("Database cleanup completed for tenant $currentTenant", 'info');
    
    successResponse($results);
    
} catch (Exception $e) {
    if (isset($db)) {
        $db->rollback();
    }
    logActivity("Cleanup failed: " . $e->getMessage(), 'error');
    errorResponse('Cleanup failed: ' . $e->getMessage());
}
