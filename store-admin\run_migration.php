<?php
/**
 * Migration Runner for Bulk Action Tables
 * Run this script to add the necessary database tables for bulk actions
 */

require_once __DIR__ . '/core/Application.php';

// Initialize application
Application::init();

try {
    // Get the tenant database (not system database)
    $db = Application::getDb();

    echo "Current tenant: " . (TenantManager::getCurrentTenant() ?? 'unknown') . "\n";
    echo "Database path: " . $db->getDatabasePath() . "\n\n";

    echo "Running bulk action tables migration...\n";

    $successCount = 0;
    $errorCount = 0;

    // Create tables manually to avoid parsing issues
    $tables = [
        'email_log' => "CREATE TABLE IF NOT EXISTS email_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id VARCHAR(50),
            subject VARCHAR(255),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        'sms_log' => "CREATE TABLE IF NOT EXISTS sms_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id VARCHAR(50),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        'employee_notifications' => "CREATE TABLE IF NOT EXISTS employee_notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            employee_id VARCHAR(50),
            type VARCHAR(50),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        'reminder_log' => "CREATE TABLE IF NOT EXISTS reminder_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            reservation_id VARCHAR(50),
            customer_id VARCHAR(50),
            type VARCHAR(20),
            message TEXT,
            sent_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )"
    ];

    // Create tables
    foreach ($tables as $tableName => $sql) {
        echo "Creating table $tableName...\n";
        try {
            $result = $db->query($sql);
            if ($result !== false) {
                $successCount++;
                echo "✓ Table $tableName created successfully\n";
            } else {
                $errorCount++;
                echo "✗ Failed to create table $tableName\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "✗ Error creating table $tableName: " . $e->getMessage() . "\n";
        }
    }

    // Create indexes
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_email_log_customer_id ON email_log(customer_id)",
        "CREATE INDEX IF NOT EXISTS idx_email_log_sent_at ON email_log(sent_at)",
        "CREATE INDEX IF NOT EXISTS idx_sms_log_customer_id ON sms_log(customer_id)",
        "CREATE INDEX IF NOT EXISTS idx_sms_log_sent_at ON sms_log(sent_at)",
        "CREATE INDEX IF NOT EXISTS idx_employee_notifications_employee_id ON employee_notifications(employee_id)",
        "CREATE INDEX IF NOT EXISTS idx_employee_notifications_sent_at ON employee_notifications(sent_at)",
        "CREATE INDEX IF NOT EXISTS idx_reminder_log_reservation_id ON reminder_log(reservation_id)",
        "CREATE INDEX IF NOT EXISTS idx_reminder_log_customer_id ON reminder_log(customer_id)",
        "CREATE INDEX IF NOT EXISTS idx_reminder_log_sent_at ON reminder_log(sent_at)"
    ];

    echo "\nCreating indexes...\n";
    foreach ($indexes as $sql) {
        try {
            $result = $db->query($sql);
            if ($result !== false) {
                $successCount++;
                echo "✓ Index created successfully\n";
            } else {
                $errorCount++;
                echo "✗ Failed to create index\n";
            }
        } catch (Exception $e) {
            $errorCount++;
            echo "✗ Error creating index: " . $e->getMessage() . "\n";
        }
    }

    // Handle schedule column addition for employees table
    echo "\nAdding schedule column to employees table...\n";
    try {
        // Check if schedule column exists
        $columns = $db->fetchAll("PRAGMA table_info(employees)");
        $hasScheduleColumn = false;
        foreach ($columns as $column) {
            if ($column['name'] === 'schedule') {
                $hasScheduleColumn = true;
                break;
            }
        }

        if (!$hasScheduleColumn) {
            $result = $db->query("ALTER TABLE employees ADD COLUMN schedule TEXT");
            if ($result !== false) {
                echo "✓ Added schedule column to employees table\n";
                $successCount++;
            } else {
                echo "✗ Failed to add schedule column\n";
                $errorCount++;
            }
        } else {
            echo "✓ Schedule column already exists\n";
        }
    } catch (Exception $e) {
        echo "✗ Error adding schedule column: " . $e->getMessage() . "\n";
        $errorCount++;
    }
    
    echo "\nMigration completed!\n";
    echo "Successful statements: $successCount\n";
    echo "Failed statements: $errorCount\n";
    
    if ($errorCount === 0) {
        echo "\n🎉 All bulk action tables have been created successfully!\n";
        echo "You can now use all the bulk action features in the admin panel.\n";
    } else {
        echo "\n⚠️  Some statements failed. Please check the errors above.\n";
    }
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
