<?php

/**
 * Main Entry Point
 * Routes to appropriate interface based on request
 */

session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load dependencies
require_once __DIR__ . '/shared/config.php';
require_once __DIR__ . '/shared/database.php';
require_once __DIR__ . '/shared/tenant_manager.php';
require_once __DIR__ . '/shared/session.php';
require_once __DIR__ . '/shared/localization.php';
require_once __DIR__ . '/shared/functions.php';

try {
    // Initialize core systems
    Config::init();
    TenantManager::init();
    SessionManager::start();
    LocalizationManager::init();
    
    // Route based on path
    $path = $_SERVER['REQUEST_URI'];
    $path = parse_url($path, PHP_URL_PATH);
    
    // Remove leading slash
    $path = ltrim($path, '/');
    
    // Route to appropriate subsystem
    if (strpos($path, 'store-admin') === 0) {
        header('Location: /store-admin/');
        exit;
    } elseif (strpos($path, 'system-admin') === 0) {
        header('Location: /system-admin/');
        exit;
    } elseif (strpos($path, 'onboarding') === 0) {
        header('Location: /onboarding/');
        exit;
    } elseif (strpos($path, 'api') === 0) {
        // API requests handled by specific files
        include __DIR__ . '/api/index.php';
        exit;
    } else {
        // Default to client booking system
        header('Location: /client/');
        exit;
    }
    
} catch (Exception $e) {
    // Error handling
    if (isDebugMode()) {
        echo "<h1>Error Details:</h1>";
        echo "<p><strong>Message:</strong> " . escapeHtml($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<h3>Stack Trace:</h3>";
        echo "<pre>" . escapeHtml($e->getTraceAsString()) . "</pre>";
    } else {
        showErrorPage();
    }
}

function isDebugMode(): bool
{
    return $_SERVER['SERVER_NAME'] === 'localhost' || 
           strpos($_SERVER['SERVER_NAME'], '.local') !== false ||
           !empty($_GET['debug']);
}

function showErrorPage(): void
{
    http_response_code(500);
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>System Error</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error-container { max-width: 500px; margin: 0 auto; }
            .error-icon { font-size: 4em; color: #e74c3c; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="error-container">
            <div class="error-icon">⚠️</div>
            <h1>System Error</h1>
            <p>We're experiencing technical difficulties. Please try again later.</p>
            <a href="/">← Return to Home</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}
