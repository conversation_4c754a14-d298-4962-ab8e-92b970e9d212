<?php
/**
 * Create Sample Data to Test Translation System
 */

require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

try {
    TenantManager::init();
    $db = TenantManager::getDatabase();
    
    echo "Creating sample categories and services...\n";
    
    // Create sample categories
    $categories = [
        ['id' => 'CAT001', 'name' => 'Κομμωτήριο', 'description' => 'Υπηρεσίες κομμωτηρίου'],
        ['id' => 'CAT002', 'name' => 'Αισθητική', 'description' => 'Υπηρεσίες αισθητικής'],
        ['id' => 'CAT003', 'name' => 'Μασάζ', 'description' => 'Υπηρεσίες μασάζ']
    ];
    
    foreach ($categories as $category) {
        // Check if category exists
        $existing = $db->fetchRow("SELECT id FROM categories WHERE id = :id", [':id' => $category['id']]);
        
        if (!$existing) {
            // Create category
            $db->query("
                INSERT INTO categories (id, name, description, is_active, created_at, updated_at)
                VALUES (:id, :name, :description, 1, :created_at, :updated_at)
            ", [
                ':id' => $category['id'],
                ':name' => $category['name'],
                ':description' => $category['description'],
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
            
            // Auto-create translation entries using Translation::save
            Translation::save("category_name_{$category['id']}", $category['name'], '', 'categories');
            Translation::save("category_description_{$category['id']}", $category['description'], '', 'categories');
            
            echo "Created category: {$category['name']}\n";
        }
    }
    
    // Create sample services
    $services = [
        ['id' => 'SRV001', 'category_id' => 'CAT001', 'name' => 'Κούρεμα', 'description' => 'Κλασικό κούρεμα', 'price' => 15.00, 'duration' => 30],
        ['id' => 'SRV002', 'category_id' => 'CAT001', 'name' => 'Χτένισμα', 'description' => 'Χτένισμα και styling', 'price' => 25.00, 'duration' => 45],
        ['id' => 'SRV003', 'category_id' => 'CAT002', 'name' => 'Καθαρισμός προσώπου', 'description' => 'Βαθύς καθαρισμός προσώπου', 'price' => 35.00, 'duration' => 60],
        ['id' => 'SRV004', 'category_id' => 'CAT003', 'name' => 'Μασάζ χαλάρωσης', 'description' => 'Μασάζ για χαλάρωση', 'price' => 50.00, 'duration' => 90]
    ];
    
    foreach ($services as $service) {
        // Check if service exists
        $existing = $db->fetchRow("SELECT id FROM services WHERE id = :id", [':id' => $service['id']]);
        
        if (!$existing) {
            // Create service
            $db->query("
                INSERT INTO services (id, category_id, name, description, price, duration, is_active, created_at, updated_at)
                VALUES (:id, :category_id, :name, :description, :price, :duration, 1, :created_at, :updated_at)
            ", [
                ':id' => $service['id'],
                ':category_id' => $service['category_id'],
                ':name' => $service['name'],
                ':description' => $service['description'],
                ':price' => $service['price'],
                ':duration' => $service['duration'],
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
            
            // Auto-create translation entries using Translation::save
            Translation::save("service_name_{$service['id']}", $service['name'], '', 'services');
            Translation::save("service_description_{$service['id']}", $service['description'], '', 'services');
            
            echo "Created service: {$service['name']}\n";
        }
    }
    
    echo "Sample data creation completed!\n";
    echo "Translation entries have been auto-created for all services and categories.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
