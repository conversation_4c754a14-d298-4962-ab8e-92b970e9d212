<?php
/**
 * Translation Schema Fix Script
 * Standardizes translation system to use 'translations' table
 * and fixes all schema inconsistencies
 */

require_once __DIR__ . '/../../shared/tenant_manager.php';

try {
    TenantManager::init();
    $db = TenantManager::getDatabase();
    
    echo "Fixing translation schema inconsistencies...\n";
    
    // Check what tables exist
    $tablesResult = $db->fetchAll("SELECT name FROM sqlite_master WHERE type='table' AND name IN ('texts', 'translations')");
    $existingTables = array_column($tablesResult, 'name');
    
    echo "Found tables: " . implode(', ', $existingTables) . "\n";
    
    // Ensure translations table exists with correct schema
    $db->query("
        CREATE TABLE IF NOT EXISTS translations (
            id TEXT PRIMARY KEY,
            key TEXT NOT NULL,
            value_el TEXT NOT NULL DEFAULT '',
            value_en TEXT NOT NULL DEFAULT '',
            category TEXT NOT NULL DEFAULT 'general',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(key, category)
        )
    ");
    
    $db->query("CREATE INDEX IF NOT EXISTS idx_translations_category ON translations(category)");
    $db->query("CREATE INDEX IF NOT EXISTS idx_translations_key ON translations(key)");
    
    echo "Ensured translations table exists with correct schema.\n";
    
    // If texts table exists, migrate its data to translations table
    if (in_array('texts', $existingTables)) {
        echo "Migrating data from texts table to translations table...\n";
        
        // Get all texts data grouped by key
        $textsData = $db->fetchAll("
            SELECT text_key, text_value, language, category 
            FROM texts 
            ORDER BY text_key, language
        ");
        
        $groupedTexts = [];
        foreach ($textsData as $text) {
            $key = $text['text_key'];
            if (!isset($groupedTexts[$key])) {
                $groupedTexts[$key] = [
                    'category' => $text['category'],
                    'value_el' => '',
                    'value_en' => ''
                ];
            }
            
            if ($text['language'] === 'el') {
                $groupedTexts[$key]['value_el'] = $text['text_value'];
            } elseif ($text['language'] === 'en') {
                $groupedTexts[$key]['value_en'] = $text['text_value'];
            }
        }
        
        // Insert into translations table
        $migrated = 0;
        foreach ($groupedTexts as $key => $data) {
            // Check if already exists in translations
            $exists = $db->fetchRow("
                SELECT id FROM translations WHERE key = :key AND category = :category
            ", [':key' => $key, ':category' => $data['category']]);
            
            if (!$exists) {
                $db->query("
                    INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                    VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
                ", [
                    ':id' => 'TR' . strtoupper(substr(uniqid(), -8)),
                    ':key' => $key,
                    ':value_el' => $data['value_el'],
                    ':value_en' => $data['value_en'],
                    ':category' => $data['category'],
                    ':created_at' => date('Y-m-d H:i:s'),
                    ':updated_at' => date('Y-m-d H:i:s')
                ]);
                $migrated++;
            }
        }
        
        echo "Migrated $migrated translations from texts table.\n";
        
        // Rename texts table as backup
        $db->query("ALTER TABLE texts RENAME TO texts_backup_" . date('Ymd_His'));
        echo "Renamed texts table to backup.\n";
    }
    
    // Add core translation keys that are missing
    $coreTranslations = [
        // Month names
        ['january', 'January', 'Ιανουάριος', 'ui'],
        ['february', 'February', 'Φεβρουάριος', 'ui'],
        ['march', 'March', 'Μάρτιος', 'ui'],
        ['april', 'April', 'Απρίλιος', 'ui'],
        ['may', 'May', 'Μάιος', 'ui'],
        ['june', 'June', 'Ιούνιος', 'ui'],
        ['july', 'July', 'Ιούλιος', 'ui'],
        ['august', 'August', 'Αύγουστος', 'ui'],
        ['september', 'September', 'Σεπτέμβριος', 'ui'],
        ['october', 'October', 'Οκτώβριος', 'ui'],
        ['november', 'November', 'Νοέμβριος', 'ui'],
        ['december', 'December', 'Δεκέμβριος', 'ui'],
        
        // Day names
        ['monday', 'Monday', 'Δευτέρα', 'ui'],
        ['tuesday', 'Tuesday', 'Τρίτη', 'ui'],
        ['wednesday', 'Wednesday', 'Τετάρτη', 'ui'],
        ['thursday', 'Thursday', 'Πέμπτη', 'ui'],
        ['friday', 'Friday', 'Παρασκευή', 'ui'],
        ['saturday', 'Saturday', 'Σάββατο', 'ui'],
        ['sunday', 'Sunday', 'Κυριακή', 'ui'],
        
        // Short day names
        ['mon', 'Mon', 'Δευ', 'ui'],
        ['tue', 'Tue', 'Τρι', 'ui'],
        ['wed', 'Wed', 'Τετ', 'ui'],
        ['thu', 'Thu', 'Πεμ', 'ui'],
        ['fri', 'Fri', 'Παρ', 'ui'],
        ['sat', 'Sat', 'Σαβ', 'ui'],
        ['sun', 'Sun', 'Κυρ', 'ui'],
        
        // Common booking texts
        ['booked', 'Booked', 'Κλεισμένο', 'booking_system'],
        ['unavailable', 'Unavailable', 'Μη διαθέσιμο', 'booking_system'],
        ['staff', 'Staff', 'Προσωπικό', 'booking_system'],
        ['auto_assign', 'Auto Assign', 'Αυτόματη Ανάθεση', 'booking_system'],
        ['auto_assign_tooltip', 'Let us assign the best available staff member', 'Αφήστε μας να αναθέσουμε το καλύτερο διαθέσιμο μέλος προσωπικού', 'booking_system'],
        
        // Error messages
        ['invalid_step', 'Invalid step number', 'Μη έγκυρος αριθμός βήματος', 'general'],
        ['no_categories', 'No categories available', 'Δεν υπάρχουν διαθέσιμες κατηγορίες', 'general'],
        ['failed_fetch_categories', 'Failed to fetch categories', 'Αποτυχία φόρτωσης κατηγοριών', 'general'],
        ['failed_fetch_services', 'Failed to fetch services', 'Αποτυχία φόρτωσης υπηρεσιών', 'general'],
        ['service_not_found', 'Service data not found', 'Δεν βρέθηκαν δεδομένα υπηρεσίας', 'general'],
        ['category_not_found', 'Category data not found', 'Δεν βρέθηκαν δεδομένα κατηγορίας', 'general'],
        
        // Validation messages
        ['name_required', 'Name is required', 'Το όνομα είναι υποχρεωτικό', 'booking_flow'],
        ['name_min_length', 'Name must be at least 2 characters', 'Το όνομα πρέπει να έχει τουλάχιστον 2 χαρακτήρες', 'booking_flow'],
        ['email_required', 'Email is required', 'Το email είναι υποχρεωτικό', 'booking_flow'],
        ['invalid_email', 'Please enter a valid email', 'Παρακαλώ εισάγετε έγκυρο email', 'booking_flow'],
        ['phone_required', 'Phone is required', 'Το τηλέφωνο είναι υποχρεωτικό', 'booking_flow'],
        ['invalid_phone', 'Please enter a valid Greek phone number', 'Παρακαλώ εισάγετε έγκυρο ελληνικό τηλέφωνο', 'booking_flow'],
        
        // Time slot statuses
        ['duration_conflict', 'Duration conflict', 'Σύγκρουση διάρκειας', 'booking_system'],
        ['insufficient_time', 'Insufficient time', 'Ανεπαρκής χρόνος', 'booking_system'],
        ['employee_unavailable', 'Staff unavailable', 'Προσωπικό μη διαθέσιμο', 'booking_system'],
        ['past_time', 'Past time', 'Παρελθούσα ώρα', 'booking_system'],
        
        // Success messages
        ['verification_resent', 'Verification code resent successfully', 'Ο κωδικός επαλήθευσης στάλθηκε επιτυχώς', 'booking_flow'],
        ['booking_created', 'Booking created successfully', 'Η κράτηση δημιουργήθηκε επιτυχώς', 'booking_flow'],
        
        // General UI
        ['loading_text', 'Loading...', 'Φόρτωση...', 'user_interface'],
        ['please_wait', 'Please wait', 'Παρακαλώ περιμένετε', 'user_interface'],
        ['error_occurred', 'An error occurred', 'Παρουσιάστηκε σφάλμα', 'user_interface'],
        ['try_again', 'Try again', 'Δοκιμάστε ξανά', 'user_interface'],
        ['next', 'Next', 'Επόμενο', 'user_interface'],
        ['previous', 'Previous', 'Προηγούμενο', 'user_interface'],
        ['continue', 'Continue', 'Συνέχεια', 'user_interface'],
        ['back', 'Back', 'Πίσω', 'user_interface'],
        ['confirm', 'Confirm', 'Επιβεβαίωση', 'user_interface'],
        ['cancel', 'Cancel', 'Ακύρωση', 'user_interface'],
        ['close', 'Close', 'Κλείσιμο', 'user_interface'],
        ['save', 'Save', 'Αποθήκευση', 'user_interface'],
        ['edit', 'Edit', 'Επεξεργασία', 'user_interface'],
        ['delete', 'Delete', 'Διαγραφή', 'user_interface'],
        ['yes', 'Yes', 'Ναι', 'user_interface'],
        ['no', 'No', 'Όχι', 'user_interface'],
    ];
    
    echo "Adding missing core translations...\n";
    $added = 0;
    
    foreach ($coreTranslations as [$key, $enValue, $elValue, $category]) {
        // Check if key already exists
        $exists = $db->fetchRow("
            SELECT id FROM translations WHERE key = :key AND category = :category
        ", [':key' => $key, ':category' => $category]);
        
        if (!$exists) {
            $db->query("
                INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
            ", [
                ':id' => 'TR' . strtoupper(substr(uniqid(), -8)),
                ':key' => $key,
                ':value_el' => $elValue,
                ':value_en' => $enValue,
                ':category' => $category,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
            $added++;
            echo "Added: $key ($category)\n";
        }
    }
    
    echo "\nTranslation schema fix completed successfully!\n";
    echo "- Standardized on 'translations' table structure\n";
    echo "- Added $added missing core translations\n";
    echo "- Translation system is now consistent\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
