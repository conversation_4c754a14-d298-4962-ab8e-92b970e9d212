<?php

/**
 * View Service Page - Compact Design
 * Display service details with icon-based information and improved layout
 */

// Get service ID from URL
$serviceId = $_GET['id'] ?? '';

if (!$serviceId) {
    Application::redirect('/store-admin/?page=services', 'Service not found', 'error');
    exit;
}

// Fetch service data
try {
    $service = $db->fetchRow("
        SELECT s.*, c.name as category_name, c.color as category_color, c.icon as category_icon,
               COUNT(r.id) as total_bookings,
               COUNT(CASE WHEN r.status = 'completed' THEN 1 END) as completed_bookings
        FROM services s
        LEFT JOIN categories c ON s.category_id = c.id
        LEFT JOIN reservations r ON s.id = r.service_id
        WHERE s.id = :id
        GROUP BY s.id
    ", [':id' => $serviceId]);

    if (!$service) {
        Application::redirect('/store-admin/?page=services', 'Service not found', 'error');
        exit;
    }

    // Get recent reservations
    $recentReservations = $db->fetchAll("
        SELECT r.*, c.name as customer_name, e.name as employee_name
        FROM reservations r
        LEFT JOIN customers c ON r.customer_id = c.id
        LEFT JOIN employees e ON r.employee_id = e.id
        WHERE r.service_id = :id
        ORDER BY r.date DESC, r.start_time DESC
        LIMIT 10
    ", [':id' => $serviceId]);

    // Get assigned employees
    $assignedEmployees = $db->fetchAll("
        SELECT e.id, e.name, e.position, e.color
        FROM employees e
        INNER JOIN employee_services es ON e.id = es.employee_id
        WHERE es.service_id = :id AND e.is_active = 1
        ORDER BY e.name
    ", [':id' => $serviceId]);
} catch (Exception $e) {
    Application::redirect('/store-admin/?page=services', 'Error loading service data', 'error');
    exit;
}
?>

<div class="view-page">
    <!-- Header -->
    <div class="view-header">
        <div class="view-header-content">
            <div class="view-breadcrumb">
                <a href="/store-admin/?page=services" class="breadcrumb-link">
                    <i class="fas fa-cog"></i> Services
                </a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-current"><?= htmlspecialchars($service['name']) ?></span>
            </div>

            <div class="view-title">
                <?php if ($service['category_name']): ?>
                    <div class="category-icon-large" style="background-color: <?= htmlspecialchars($service['category_color']) ?>;">
                        <i class="<?= htmlspecialchars($service['category_icon'] ?? 'fas fa-cog') ?>"></i>
                    </div>
                <?php endif; ?>
                <div class="title-content">
                    <h1><?= htmlspecialchars($service['name']) ?></h1>
                    <div class="view-subtitle">
                        <?php if ($service['category_name']): ?>
                            <span class="category-badge" style="background-color: <?= htmlspecialchars($service['category_color']) ?>">
                                <?= htmlspecialchars($service['category_name']) ?>
                            </span>
                        <?php endif; ?>
                        <span class="status-badge status-<?= $service['is_active'] ? 'active' : 'inactive' ?>">
                            <i class="fas fa-<?= $service['is_active'] ? 'check-circle' : 'times-circle' ?>"></i>
                            <?= $service['is_active'] ? 'Active' : 'Inactive' ?>
                        </span>
                        <?php if (!empty($service['name_en'])): ?>
                            <span class="english-name"><?= htmlspecialchars($service['name_en']) ?></span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="view-actions">
            <div class="dropdown">
                <button class="btn btn-outline dropdown-toggle" type="button">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu">
                    <a href="/store-admin/?page=edit-service&id=<?= htmlspecialchars($serviceId) ?>" class="dropdown-item">
                        <i class="fas fa-edit"></i> Edit Service
                    </a>
                    <button onclick="duplicateService('<?= htmlspecialchars($serviceId) ?>')" class="dropdown-item">
                        <i class="fas fa-copy"></i> Duplicate
                    </button>
                    <a href="/store-admin/?page=reservations&service_id=<?= htmlspecialchars($serviceId) ?>" class="dropdown-item">
                        <i class="fas fa-calendar-alt"></i> View Bookings
                    </a>
                    <a href="/store-admin/?page=employees&service_id=<?= htmlspecialchars($serviceId) ?>" class="dropdown-item">
                        <i class="fas fa-users"></i> Manage Staff
                    </a>
                    <div class="dropdown-divider"></div>
                    <a href="/store-admin/?page=add-reservation&service_id=<?= htmlspecialchars($serviceId) ?>" class="dropdown-item">
                        <i class="fas fa-calendar-plus"></i> Book Service
                    </a>
                    <button onclick="toggleServiceStatus('<?= htmlspecialchars($serviceId) ?>')" class="dropdown-item">
                        <i class="fas fa-toggle-on"></i> Toggle Status
                    </button>
                    <button onclick="exportServiceData('<?= htmlspecialchars($serviceId) ?>')" class="dropdown-item">
                        <i class="fas fa-download"></i> Export Data
                    </button>
                    <div class="dropdown-divider"></div>
                    <button onclick="deleteService('<?= htmlspecialchars($serviceId) ?>')" class="dropdown-item text-danger">
                        <i class="fas fa-trash"></i> Delete Service
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="view-content">
        <div class="view-grid">
            <!-- Service Details -->
            <div class="consolidated-info-box compact">
                <div class="compact-header">
                    <i class="fas fa-info-circle"></i>
                    <span>Service Details</span>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <i class="fas fa-tag info-icon"></i>
                        <span class="info-label">Name:</span>
                        <span class="info-value"><?= htmlspecialchars($service['name']) ?></span>
                    </div>
                    <?php if (!empty($service['name_en'])): ?>
                        <div class="info-item">
                            <i class="fas fa-language info-icon"></i>
                            <span class="info-label">English:</span>
                            <span class="info-value"><?= htmlspecialchars($service['name_en']) ?></span>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="info-row">
                    <div class="info-item">
                        <i class="fas fa-euro-sign info-icon"></i>
                        <span class="info-label">Price:</span>
                        <span class="info-value price">€<?= number_format($service['price'], 2) ?></span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock info-icon"></i>
                        <span class="info-label">Duration:</span>
                        <span class="info-value"><?= $service['duration'] ?> minutes</span>
                    </div>
                </div>

                <div class="info-row">
                    <?php if ($service['category_name']): ?>
                        <div class="info-item">
                            <i class="fas fa-folder info-icon"></i>
                            <span class="info-label">Category:</span>
                            <span class="category-badge" style="background-color: <?= htmlspecialchars($service['category_color']) ?>">
                                <?= htmlspecialchars($service['category_name']) ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    <div class="info-item">
                        <i class="fas fa-user-check info-icon"></i>
                        <span class="info-label">Employee Selection:</span>
                        <span class="info-value">
                            <?= $service['employee_selection'] === 'manual' ? 'Manual' : 'Automatic' ?>
                        </span>
                    </div>
                </div>

                <?php if (!empty($service['description'])): ?>
                    <div class="info-row full-width">
                        <div class="info-item">
                            <i class="fas fa-align-left info-icon"></i>
                            <span class="info-label">Description:</span>
                            <div class="info-value description"><?= nl2br(htmlspecialchars($service['description'])) ?></div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Time Settings -->
            <div class="consolidated-info-box compact">
                <div class="compact-header">
                    <i class="fas fa-stopwatch"></i>
                    <span>Time Settings</span>
                </div>
                <div class="info-row">
                    <div class="info-item">
                        <i class="fas fa-hourglass-start info-icon"></i>
                        <span class="info-label">Preparation:</span>
                        <span class="info-value"><?= $service['preparation_time'] ?? 0 ?> minutes</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-hourglass-end info-icon"></i>
                        <span class="info-label">Cleanup:</span>
                        <span class="info-value"><?= $service['cleanup_time'] ?? 0 ?> minutes</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-clock info-icon"></i>
                        <span class="info-label">Total Time:</span>
                        <span class="info-value">
                            <?= ($service['preparation_time'] ?? 0) + $service['duration'] + ($service['cleanup_time'] ?? 0) ?> minutes
                        </span>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-chart-bar"></i> Performance Statistics</h2>
                </div>
                <div class="section-content">
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-value"><?= $service['total_bookings'] ?></div>
                            <div class="stat-label">Total Bookings</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value"><?= $service['completed_bookings'] ?></div>
                            <div class="stat-label">Completed</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">€<?= number_format(($service['completed_bookings'] * $service['price']) ?? 0, 2) ?></div>
                            <div class="stat-label">Total Revenue</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">
                                <?= $service['total_bookings'] > 0 ? round(($service['completed_bookings'] / $service['total_bookings']) * 100, 1) : 0 ?>%
                            </div>
                            <div class="stat-label">Completion Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assigned Employees -->
        <div class="view-section">
            <div class="section-header">
                <h2><i class="fas fa-users"></i> Assigned Employees</h2>
                <a href="/store-admin/?page=edit-service&id=<?= htmlspecialchars($serviceId) ?>" class="btn btn-outline btn-sm">
                    <i class="fas fa-edit"></i> Manage Assignments
                </a>
            </div>
            <div class="section-content">
                <?php if (!empty($assignedEmployees)): ?>
                    <div class="employee-list">
                        <?php foreach ($assignedEmployees as $employee): ?>
                            <div class="employee-item">
                                <div class="employee-avatar" style="background-color: <?= htmlspecialchars($employee['color'] ?: '#3498db') ?>">
                                    <?= strtoupper(substr($employee['name'], 0, 1)) ?>
                                </div>
                                <div class="employee-info">
                                    <div class="employee-name"><?= htmlspecialchars($employee['name']) ?></div>
                                    <?php if (!empty($employee['position'])): ?>
                                        <div class="employee-position"><?= htmlspecialchars($employee['position']) ?></div>
                                    <?php endif; ?>
                                </div>
                                <a href="/store-admin/?page=view-employee&id=<?= htmlspecialchars($employee['id']) ?>"
                                    class="btn btn-sm btn-outline" title="View Employee">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="info-item-with-icon">
                        <div class="info-icon">
                            <i class="fas fa-exclamation-circle"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">Employees</div>
                            <div class="info-value">No employees assigned to this service</div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>



        <!-- Recent Reservations -->
        <?php if (!empty($recentReservations)): ?>
            <div class="view-section">
                <div class="section-header">
                    <h2><i class="fas fa-calendar-check"></i> Recent Reservations</h2>
                    <a href="/store-admin/?page=reservations&service_id=<?= htmlspecialchars($serviceId) ?>" class="btn btn-outline btn-sm">
                        <i class="fas fa-external-link-alt"></i> View All
                    </a>
                </div>
                <div class="section-content">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Customer</th>
                                    <th>Employee</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentReservations as $reservation): ?>
                                    <tr>
                                        <td>
                                            <div class="date-info">
                                                <strong><?= date('M j, Y', strtotime($reservation['date'])) ?></strong><br>
                                                <small><?= date('H:i', strtotime($reservation['start_time'])) ?></small>
                                            </div>
                                        </td>
                                        <td><?= htmlspecialchars($reservation['customer_name']) ?></td>
                                        <td><?= htmlspecialchars($reservation['employee_name']) ?></td>
                                        <td>
                                            <span class="status-badge status-<?= $reservation['status'] ?>">
                                                <?= ucfirst($reservation['status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="/store-admin/?page=view-reservation&id=<?= $reservation['id'] ?>"
                                                class="btn btn-sm btn-outline" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    function duplicateService(id) {
        if (confirm('Are you sure you want to duplicate this service?')) {
            const formData = new FormData();
            formData.append('action', 'duplicate_service');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Service duplicated successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=edit-service&id=' + data.new_id;
                        }, 1000);
                    } else {
                        showNotification('Error duplicating service: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }

    function deleteService(id) {
        if (confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
            const formData = new FormData();
            formData.append('action', 'delete_service');
            formData.append('id', id);

            fetch('/store-admin/controllers/ajax.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Service deleted successfully', 'success');
                        setTimeout(() => {
                            window.location.href = '/store-admin/?page=services';
                        }, 1000);
                    } else {
                        showNotification('Error deleting service: ' + (data.error || 'Unknown error'), 'error');
                    }
                })
                .catch(error => {
                    showNotification('Network error occurred', 'error');
                });
        }
    }
</script>