<?php
/**
 * Translation Sync Script
 * Quick script to discover and sync new t() calls with the admin interface
 */

require_once __DIR__ . '/discover_and_sync_translations.php';

echo "🔄 Translation Sync Tool\n";
echo "========================\n\n";

echo "This script will:\n";
echo "1. Scan client code for t() function calls\n";
echo "2. Add any missing translations to the database\n";
echo "3. Make them available in the admin interface\n\n";

echo "Starting sync...\n\n";

try {
    $discovery = new TranslationDiscovery();
    
    // Discover translations
    $discovered = $discovery->discoverTranslations();
    
    // Sync with database
    $results = $discovery->syncWithDatabase();
    
    echo "\n✅ Sync completed!\n";
    echo "==================\n";
    echo "- New translations added: {$results['added']}\n";
    echo "- Already existed: {$results['skipped']}\n";
    echo "- Errors: " . count($results['errors']) . "\n\n";
    
    if ($results['added'] > 0) {
        echo "🎉 {$results['added']} new translations are now available in the admin interface!\n";
        echo "Go to Store Admin > Translations to edit them.\n\n";
    } else {
        echo "ℹ️  All translations are already synced. No new ones found.\n\n";
    }
    
    echo "💡 Tips:\n";
    echo "- Run this script whenever you add new t() calls to client code\n";
    echo "- New translations will have default values from the t() calls\n";
    echo "- Edit the translations in the admin interface to customize them\n";
    echo "- Changes in admin will immediately appear on the client\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
