* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-brand h2 {
    color: #007bff;
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-menu a:hover {
    color: #007bff;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Buttons */
.btn-primary {
    background: #007bff;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    display: inline-block;
    cursor: pointer;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: #007bff;
    padding: 12px 24px;
    border: 2px solid #007bff;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s;
    display: inline-block;
}

.btn-secondary:hover {
    background: #007bff;
    color: white;
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
}

.dashboard-preview {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    width: 400px;
    height: 300px;
    border: 1px solid rgba(255,255,255,0.2);
}

.dashboard-header {
    background: rgba(255,255,255,0.2);
    height: 40px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    height: 200px;
}

.dashboard-card {
    background: rgba(255,255,255,0.15);
    border-radius: 8px;
    border: 1px solid rgba(255,255,255,0.1);
}

/* Features Section */
.features {
    padding: 80px 0;
    background: #f8f9fa;
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Benefits Section */
.benefits {
    padding: 80px 0;
    background: white;
}

.benefits-content h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.benefits-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.benefit-icon {
    color: #28a745;
    font-size: 1.5rem;
    font-weight: bold;
}

.benefit-item h4 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.benefit-item p {
    color: #666;
}

/* Testimonials */
.testimonials {
    padding: 80px 0;
    background: #f8f9fa;
}

.testimonials h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.testimonial-content p {
    font-style: italic;
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.testimonial-author h4 {
    color: #333;
    margin-bottom: 0.25rem;
}

.testimonial-author span {
    color: #666;
    font-size: 0.9rem;
}

/* Pricing Section */
.pricing {
    padding: 80px 0;
    background: white;
}

.pricing h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: all 0.3s;
}

.pricing-card.featured {
    border-color: #007bff;
    transform: scale(1.05);
}

.pricing-card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.popular-badge {
    background: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.pricing-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.price {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.2rem;
    vertical-align: top;
    color: #666;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
}

.period {
    color: #666;
    font-size: 1rem;
}

.features-list {
    list-style: none;
    margin-bottom: 2rem;
}

.features-list li {
    padding: 0.5rem 0;
    color: #666;
    border-bottom: 1px solid #f1f1f1;
}

.features-list li:last-child {
    border-bottom: none;
}

/* Demo Section */
.demo-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.demo-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.demo-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: #f8f9fa;
    text-align: center;
}

.contact h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.contact p {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    color: #666;
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
}

.contact-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.contact-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.contact-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #007bff;
}

.footer-section h4 {
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-section a:hover {
    color: #007bff;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #444;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .dashboard-preview {
        width: 100%;
        max-width: 350px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .benefits-list {
        grid-template-columns: 1fr;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .contact-options {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .btn-primary, .btn-secondary {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    .features h2,
    .benefits-content h2,
    .testimonials h2,
    .pricing h2,
    .demo-section h2,
    .contact h2 {
        font-size: 2rem;
    }

    .feature-card,
    .testimonial-card,
    .contact-card {
        padding: 1.5rem;
    }
}
