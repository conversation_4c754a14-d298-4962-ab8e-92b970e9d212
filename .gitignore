# Database files
*.db
*.sqlite
*.sqlite3
data/
storage/

# Log files
*.log
logs/
error.log
access.log

# Configuration files with sensitive data
config/local.php
.env
.env.local
.env.production

# Temporary files
tmp/
temp/
cache/
*.tmp
*.temp

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# PHP specific
vendor/
composer.lock
.phpunit.result.cache

# Node.js (if any)
node_modules/
npm-debug.log
yarn-error.log

# Backup files
*.bak
*.backup
*.old

# Upload directories
uploads/
files/
media/

# System files
.htaccess.backup
robots.txt.backup

# Development files
test_*.php
debug_*.php
*.test.php
*.debug.php

# Documentation (development only)
*.md
!README.md

# Excel/Office files
*.xlsx
*.xls
*.doc
*.docx
*.ppt
*.pptx

# Archive files
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
