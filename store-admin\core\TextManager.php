<?php
/**
 * TextManager Class
 * Handles automatic text creation for services, categories, and other translatable content
 */

class TextManager
{
    /**
     * Create text entries for a new service
     */
    public static function createServiceTexts(Database $db, string $serviceId, string $serviceName, string $serviceDescription): void
    {
        try {
            $defaultLanguage = 'el'; // Greek as primary language
            $timestamp = date('Y-m-d H:i:s');
            
            // Create service name text entry
            $nameKey = "service_name_{$serviceId}";
            $db->query("
                INSERT INTO texts (id, text_key, text_value, description, language, category, created_at, updated_at) 
                VALUES (:id, :text_key, :text_value, :description, :language, :category, :created_at, :updated_at)
            ", [
                ':id' => Application::generateId('TXT'),
                ':text_key' => $nameKey,
                ':text_value' => $serviceName,
                ':description' => "Service name: {$serviceName}",
                ':language' => $defaultLanguage,
                ':category' => 'services',
                ':created_at' => $timestamp,
                ':updated_at' => $timestamp
            ]);
            
            // Create service description text entry (if not empty)
            if (!empty(trim($serviceDescription))) {
                $descKey = "service_description_{$serviceId}";
                $db->query("
                    INSERT INTO texts (id, text_key, text_value, description, language, category, created_at, updated_at) 
                    VALUES (:id, :text_key, :text_value, :description, :language, :category, :created_at, :updated_at)
                ", [
                    ':id' => Application::generateId('TXT'),
                    ':text_key' => $descKey,
                    ':text_value' => $serviceDescription,
                    ':description' => "Service description: {$serviceName}",
                    ':language' => $defaultLanguage,
                    ':category' => 'services',
                    ':created_at' => $timestamp,
                    ':updated_at' => $timestamp
                ]);
            }
            
            error_log("Created text entries for service: {$serviceName}");
            
        } catch (Exception $e) {
            error_log("Failed to create service texts: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create text entries for a new category
     */
    public static function createCategoryTexts(Database $db, string $categoryId, string $categoryName, string $categoryDescription = ''): void
    {
        try {
            $defaultLanguage = 'el'; // Greek as primary language
            $timestamp = date('Y-m-d H:i:s');
            
            // Create category name text entry
            $nameKey = "category_name_{$categoryId}";
            $db->query("
                INSERT INTO texts (id, text_key, text_value, description, language, category, created_at, updated_at) 
                VALUES (:id, :text_key, :text_value, :description, :language, :category, :created_at, :updated_at)
            ", [
                ':id' => Application::generateId('TXT'),
                ':text_key' => $nameKey,
                ':text_value' => $categoryName,
                ':description' => "Category name: {$categoryName}",
                ':language' => $defaultLanguage,
                ':category' => 'categories',
                ':created_at' => $timestamp,
                ':updated_at' => $timestamp
            ]);
            
            // Create category description text entry (if not empty)
            if (!empty(trim($categoryDescription))) {
                $descKey = "category_description_{$categoryId}";
                $db->query("
                    INSERT INTO texts (id, text_key, text_value, description, language, category, created_at, updated_at) 
                    VALUES (:id, :text_key, :text_value, :description, :language, :category, :created_at, :updated_at)
                ", [
                    ':id' => Application::generateId('TXT'),
                    ':text_key' => $descKey,
                    ':text_value' => $categoryDescription,
                    ':description' => "Category description: {$categoryName}",
                    ':language' => $defaultLanguage,
                    ':category' => 'categories',
                    ':created_at' => $timestamp,
                    ':updated_at' => $timestamp
                ]);
            }
            
            error_log("Created text entries for category: {$categoryName}");
            
        } catch (Exception $e) {
            error_log("Failed to create category texts: " . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Create default client-side text entries
     */
    public static function createDefaultClientTexts(Database $db): void
    {
        $defaultTexts = [
            // Booking flow texts
            'booking_title' => ['el' => 'Κλείσιμο Ραντεβού', 'en' => 'Book Appointment'],
            'select_category' => ['el' => 'Επιλέξτε Κατηγορία', 'en' => 'Select Category'],
            'select_service' => ['el' => 'Επιλέξτε Υπηρεσία', 'en' => 'Select Service'],
            'select_date' => ['el' => 'Επιλέξτε Ημερομηνία', 'en' => 'Select Date'],
            'select_time' => ['el' => 'Επιλέξτε Ώρα', 'en' => 'Select Time'],
            'contact_details' => ['el' => 'Στοιχεία Επικοινωνίας', 'en' => 'Contact Details'],
            'verification' => ['el' => 'Επιβεβαίωση', 'en' => 'Verification'],
            'confirmation' => ['el' => 'Επιβεβαίωση', 'en' => 'Confirmation'],
            
            // Form fields
            'name' => ['el' => 'Όνομα', 'en' => 'Name'],
            'email' => ['el' => 'Email', 'en' => 'Email'],
            'phone' => ['el' => 'Τηλέφωνο', 'en' => 'Phone'],
            'notes' => ['el' => 'Σημειώσεις', 'en' => 'Notes'],
            
            // Navigation
            'next' => ['el' => 'Επόμενο', 'en' => 'Next'],
            'back' => ['el' => 'Πίσω', 'en' => 'Back'],
            'continue' => ['el' => 'Συνέχεια', 'en' => 'Continue'],
            'cancel' => ['el' => 'Ακύρωση', 'en' => 'Cancel'],
            
            // Messages
            'loading' => ['el' => 'Φόρτωση...', 'en' => 'Loading...'],
            'please_wait' => ['el' => 'Παρακαλώ περιμένετε...', 'en' => 'Please wait...'],
            'booking_confirmed' => ['el' => 'Το ραντεβού επιβεβαιώθηκε!', 'en' => 'Booking Confirmed!'],
            'booking_success_message' => ['el' => 'Το ραντεβού σας κλείστηκε επιτυχώς. Θα λάβετε email επιβεβαίωσης σύντομα.', 'en' => 'Your appointment has been successfully booked. We\'ll send you a confirmation email shortly.'],
            'book_another' => ['el' => 'Κλείσιμο Άλλου Ραντεβού', 'en' => 'Book Another Appointment'],
            
            // Time slots
            'available' => ['el' => 'Διαθέσιμο', 'en' => 'Available'],
            'unavailable' => ['el' => 'Μη διαθέσιμο', 'en' => 'Unavailable'],
            'closed' => ['el' => 'Κλειστό', 'en' => 'Closed'],
            
            // Validation messages
            'required_field' => ['el' => 'Αυτό το πεδίο είναι υποχρεωτικό', 'en' => 'This field is required'],
            'invalid_email' => ['el' => 'Παρακαλώ εισάγετε έγκυρο email', 'en' => 'Please enter a valid email'],
            'invalid_phone' => ['el' => 'Παρακαλώ εισάγετε έγκυρο τηλέφωνο', 'en' => 'Please enter a valid phone number'],
        ];
        
        $timestamp = date('Y-m-d H:i:s');
        
        foreach ($defaultTexts as $textKey => $translations) {
            foreach ($translations as $language => $textValue) {
                try {
                    // Check if text already exists
                    $existing = $db->fetchRow("
                        SELECT id FROM texts 
                        WHERE text_key = :text_key AND language = :language AND category = 'booking'
                    ", [
                        ':text_key' => $textKey,
                        ':language' => $language
                    ]);
                    
                    if (!$existing) {
                        $db->query("
                            INSERT INTO texts (id, text_key, text_value, description, language, category, created_at, updated_at) 
                            VALUES (:id, :text_key, :text_value, :description, :language, :category, :created_at, :updated_at)
                        ", [
                            ':id' => Application::generateId('TXT'),
                            ':text_key' => $textKey,
                            ':text_value' => $textValue,
                            ':description' => "Client-side text: {$textKey}",
                            ':language' => $language,
                            ':category' => 'booking',
                            ':created_at' => $timestamp,
                            ':updated_at' => $timestamp
                        ]);
                    }
                } catch (Exception $e) {
                    error_log("Failed to create default text {$textKey} for {$language}: " . $e->getMessage());
                }
            }
        }
        
        error_log("Created default client-side texts");
    }
}
