<?php

/**
 * Customer Form
 * Add/Edit customer form
 */

$isEdit = !empty($item);
$customer = $item;
?>

<form method="POST" class="modal-form">
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
    <?php if ($isEdit): ?>
        <input type="hidden" name="id" value="<?php echo $customer['id']; ?>">
    <?php endif; ?>

    <div class="form-group">
        <label for="name">Customer Name *</label>
        <input type="text" id="name" name="name" required
            value="<?php echo $isEdit ? htmlspecialchars($customer['name']) : ''; ?>">
    </div>

    <div class="form-row">
        <div class="form-group">
            <label for="email">Email</label>
            <input type="email" id="email" name="email"
                value="<?php echo $isEdit ? htmlspecialchars($customer['email']) : ''; ?>">
        </div>
        <div class="form-group">
            <label for="phone">Phone</label>
            <input type="tel" id="phone" name="phone"
                value="<?php echo $isEdit ? htmlspecialchars($customer['phone']) : ''; ?>">
        </div>
    </div>

    <div class="form-group">
        <label for="language">Preferred Language</label>
        <select id="language" name="language">
            <option value="el" <?php echo (!$isEdit || $customer['language'] === 'el') ? 'selected' : ''; ?>>
                Greek
            </option>
            <option value="en" <?php echo ($isEdit && $customer['language'] === 'en') ? 'selected' : ''; ?>>
                English
            </option>
        </select>
    </div>

    <div class="form-group">
        <label for="notes">Notes</label>
        <textarea id="notes" name="notes" rows="3"
            placeholder="Any special notes about this customer..."><?php echo $isEdit ? htmlspecialchars($customer['notes']) : ''; ?></textarea>
    </div>

    <?php if ($isEdit): ?>
        <!-- Customer Statistics -->
        <div class="customer-stats">
            <h4>Customer Statistics</h4>
            <?php
            $stats = $db->fetchRow("
                SELECT COUNT(*) as total_reservations,
                       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_reservations,
                       COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_reservations,
                       SUM(CASE WHEN status = 'completed' THEN price ELSE 0 END) as total_spent,
                       MAX(date) as last_visit,
                       MIN(created_at) as first_booking
                FROM reservations 
                WHERE customer_id = :customer_id
            ", [':customer_id' => $customer['id']]);
            ?>

            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-value"><?php echo $stats['total_reservations']; ?></span>
                    <span class="stat-label">Total Reservations</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo $stats['completed_reservations']; ?></span>
                    <span class="stat-label">Completed</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo '€' . number_format($stats['total_spent'] ?: 0, 2); ?></span>
                    <span class="stat-label">Total Spent</span>
                </div>
            </div>

            <?php if ($stats['last_visit']): ?>
                <div class="customer-dates">
                    <div class="date-item">
                        <strong>Last Visit:</strong>
                        <?php echo $stats['last_visit'] ? date('M j, Y', strtotime($stats['last_visit'])) : 'Never'; ?>
                    </div>
                    <div class="date-item">
                        <strong>First Booking:</strong>
                        <?php echo $stats['first_booking'] ? date('M j, Y', strtotime($stats['first_booking'])) : 'Never'; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <!-- Quick Actions -->
        <div class="customer-actions">
            <h4>Quick Actions</h4>
            <div class="action-buttons">
                <a href="/store-admin/?page=reservations&customer_id=<?php echo $customer['id']; ?>"
                    class="btn btn-secondary btn-sm" target="_blank">
                    <i class="fas fa-calendar-alt"></i> View Reservations
                </a>
                <a href="/store-admin/?page=reservations&action=add&customer_id=<?php echo $customer['id']; ?>"
                    class="btn btn-primary btn-sm" target="_blank">
                    <i class="fas fa-plus"></i> New Reservation
                </a>
            </div>
        </div>
    <?php endif; ?>

    <div class="form-actions">
        <button type="submit" class="btn btn-primary">
            <?php echo $isEdit ? 'Update' : 'Create'; ?> Customer
        </button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">
            Cancel
        </button>
    </div>
</form>

<!-- Inline styles removed - now using consistent CSS framework -->