<?php

class EmailTemplate
{
    private static function getBaseTemplate(): string
    {
        return '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TITLE}}</title>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        /* Header */
        .email-header {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .email-header h1 {
            font-size: 1.875rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .email-header .subtitle {
            font-size: 1.125rem;
            color: #718096;
            margin-bottom: 0;
        }
        
        /* Body */
        .email-body {
            padding: 40px 30px;
            background: white;
        }
        
        .email-body h2 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .email-body p {
            color: #4a5568;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        /* Verification Code Box */
        .verification-code-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .verification-code-box {
            display: inline-block;
            background: #f7fafc;
            border: 2px dashed #a0aec0;
            border-radius: 12px;
            padding: 25px 40px;
            margin: 20px 0;
        }
        
        .verification-code {
            font-family: "Courier New", monospace;
            font-size: 2rem;
            font-weight: 700;
            color: #2d3748;
            letter-spacing: 8px;
            margin: 0;
        }
        
        .verification-label {
            font-size: 0.875rem;
            color: #718096;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        /* Booking Summary */
        .booking-summary {
            background: #f7fafc;
            border-radius: 16px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .summary-item:last-child {
            border-bottom: none;
        }
        
        .summary-label {
            font-weight: 600;
            color: #2d3748;
        }
        
        .summary-value {
            color: #4a5568;
            font-weight: 500;
        }
        
        .summary-price {
            font-weight: 700;
            color: #667eea;
            font-size: 1.125rem;
        }
        
        /* Booking ID */
        .booking-id-container {
            text-align: center;
            margin: 30px 0;
        }
        
        .booking-id {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 12px;
            font-family: "Courier New", monospace;
            font-size: 1.125rem;
            font-weight: 600;
            letter-spacing: 2px;
        }
        
        /* Buttons */
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            text-align: center;
            margin: 10px 5px;
            transition: transform 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        /* Footer */
        .email-footer {
            background: #f7fafc;
            padding: 30px;
            text-align: center;
            color: #718096;
            font-size: 0.875rem;
        }
        
        .email-footer p {
            margin-bottom: 10px;
        }
        
        /* Responsive */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-header,
            .email-body,
            .email-footer {
                padding: 20px;
            }
            
            .verification-code {
                font-size: 1.5rem;
                letter-spacing: 4px;
            }
            
            .verification-code-box {
                padding: 20px 25px;
            }
            
            .booking-summary {
                padding: 20px;
            }
            
            .summary-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="email-header">
            <h1>{{BUSINESS_NAME}}</h1>
            <p class="subtitle">{{HEADER_SUBTITLE}}</p>
        </div>
        
        <div class="email-body">
            {{CONTENT}}
        </div>
        
        <div class="email-footer">
            <p>{{BUSINESS_NAME}}</p>
            <p>This email was sent automatically. Please do not reply to this email.</p>
            <p>If you have any questions, please contact us directly.</p>
        </div>
    </div>
</body>
</html>';
    }
    
    public static function generateVerificationEmail(string $code, string $businessName = 'Booking System'): string
    {
        $content = '
            <h2>Email Verification Required</h2>
            <p>Thank you for your booking request. To complete your reservation, please verify your email address using the code below:</p>
            
            <div class="verification-code-container">
                <div class="verification-label">Your Verification Code</div>
                <div class="verification-code-box">
                    <div class="verification-code">' . $code . '</div>
                </div>
            </div>
            
            <p style="text-align: center; color: #e53e3e; font-weight: 600;">This code will expire in 5 minutes.</p>
            <p style="text-align: center;">Enter this code on the booking page to confirm your reservation.</p>
            <p style="text-align: center; font-size: 0.875rem; color: #718096;">If you did not request this code, please ignore this email.</p>
        ';
        
        return str_replace([
            '{{TITLE}}',
            '{{BUSINESS_NAME}}',
            '{{HEADER_SUBTITLE}}',
            '{{CONTENT}}'
        ], [
            'Email Verification - ' . $businessName,
            $businessName,
            'Verify your email to complete your booking',
            $content
        ], self::getBaseTemplate());
    }
    
    public static function generateBookingConfirmation(array $bookingData): string
    {
        $businessName = $bookingData['business_name'] ?? 'Booking System';
        $customerName = $bookingData['customer_name'] ?? 'Valued Customer';
        
        $content = '
            <h2>🎉 Booking Confirmed!</h2>
            <p>Dear ' . htmlspecialchars($customerName) . ',</p>
            <p>Great news! Your booking has been confirmed. Here are the details:</p>
            
            <div class="booking-summary">
                <div class="summary-item">
                    <span class="summary-label">Service</span>
                    <span class="summary-value">' . htmlspecialchars($bookingData['service_name'] ?? '') . '</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Date</span>
                    <span class="summary-value">' . htmlspecialchars($bookingData['date'] ?? '') . '</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Time</span>
                    <span class="summary-value">' . htmlspecialchars($bookingData['time'] ?? '') . '</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Duration</span>
                    <span class="summary-value">' . htmlspecialchars($bookingData['duration'] ?? '') . ' minutes</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Staff Member</span>
                    <span class="summary-value">' . htmlspecialchars($bookingData['employee_name'] ?? '') . '</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">Price</span>
                    <span class="summary-price">€' . htmlspecialchars($bookingData['price'] ?? '0') . '</span>
                </div>
            </div>
            
            <p style="text-align: center;">We look forward to seeing you!</p>
            <p style="text-align: center; font-size: 0.875rem; color: #718096;">Please arrive 5 minutes before your appointment time.</p>
        ';
        
        return str_replace([
            '{{TITLE}}',
            '{{BUSINESS_NAME}}',
            '{{HEADER_SUBTITLE}}',
            '{{CONTENT}}'
        ], [
            'Booking Confirmation - ' . $businessName,
            $businessName,
            'Your appointment is confirmed',
            $content
        ], self::getBaseTemplate());
    }
}
