<?php

/**
 * Global Configuration
 * Centralized configuration management for the booking system
 */

class Config
{
    // System Configuration
    const TIMEZONE = 'Europe/Athens';
    const SESSION_LIFETIME = 86400; // 24 hours
    const SUPPORTED_LANGUAGES = ['en', 'el'];
    const DEFAULT_LANGUAGE = 'en';

    // SMTP Configuration (fallback values)
    // Primary SMTP settings are stored in database per tenant
    const SMTP_HOST = '';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '';
    const SMTP_PASSWORD = '';
    const SMTP_FROM_EMAIL = '';
    const SMTP_FROM_NAME = '';

    // Default Business Hours
    const BUSINESS_HOURS = [
        'monday' => ['09:00', '17:00'],
        'tuesday' => ['09:00', '17:00'],
        'wednesday' => ['09:00', '17:00'],
        'thursday' => ['09:00', '17:00'],
        'friday' => ['09:00', '17:00'],
        'saturday' => ['09:00', '15:00'],
        'sunday' => null
    ];

    // Verification System
    const VERIFICATION_CODE_LENGTH = 6;
    const VERIFICATION_CODE_EXPIRY = 300; // 5 minutes

    // Booking System
    const BUFFER_TIME_MINUTES = 15;
    const SLOT_DURATION_MINUTES = 15;

    // Database Configuration
    const DB_DIR = __DIR__ . '/../data';
    const MASTER_DB = __DIR__ . '/../data/system/system.db';

    // Security Configuration
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_TIME = 900; // 15 minutes

    // File Upload Configuration
    const MAX_UPLOAD_SIZE = 5242880; // 5MB
    const ALLOWED_UPLOAD_TYPES = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

    private static bool $initialized = false;

    /**
     * Initialize the configuration system
     */
    public static function init(): void
    {
        if (self::$initialized) {
            return;
        }

        date_default_timezone_set(self::TIMEZONE);
        self::$initialized = true;
    }

    /**
     * Get default business hours
     */
    public static function getBusinessHours(): array
    {
        return self::BUSINESS_HOURS;
    }

    /**
     * Check if a day is a working day
     */
    public static function isWorkingDay(string $day): bool
    {
        return isset(self::BUSINESS_HOURS[$day]) && self::BUSINESS_HOURS[$day] !== null;
    }

    /**
     * Get working hours for a specific day
     */
    public static function getWorkingHours(string $day): ?array
    {
        return self::BUSINESS_HOURS[$day] ?? null;
    }

    /**
     * Get tenant database path
     */
    public static function getTenantDbPath(string $tenantId): string
    {
        return self::DB_DIR . '/tenants/' . $tenantId . '.db';
    }

    /**
     * Get system database path
     */
    public static function getSystemDbPath(): string
    {
        return self::MASTER_DB;
    }

    /**
     * Check if initialization is complete
     */
    public static function isInitialized(): bool
    {
        return self::$initialized;
    }
}
