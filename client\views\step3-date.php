<?php
/**
 * Step 3: Date Selection View Template
 */
?>

<div class="step-title"><?= t('select_date', 'Select Date', 'client') ?></div>
<div class="step-subtitle"><?= t('select_date_subtitle', 'Choose your preferred date', 'client') ?></div>

<div class="calendar-container">
    <div class="calendar-header">
        <div class="calendar-month" id="calendarMonth"></div>
        <div class="calendar-nav">
            <button class="calendar-nav-btn" onclick="BookingSystem.instance.changeMonth(-1)">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button class="calendar-nav-btn" onclick="BookingSystem.instance.changeMonth(1)">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
    <div class="calendar-grid" id="calendarGrid">
        <!-- Calendar will be loaded dynamically -->
    </div>
</div>

<script>
// Load calendar when this step is shown
document.addEventListener('DOMContentLoaded', function() {
    if (typeof BookingSystem !== 'undefined' && BookingSystem.instance) {
        BookingSystem.instance.loadCalendar();
    }
});
</script>
