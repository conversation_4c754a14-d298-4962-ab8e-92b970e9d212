<?php

/**
 * Pagination Class
 * Handles pagination for admin interface
 */

class Pagination
{
    private $totalItems;
    private $itemsPerPage;
    private $currentPage;
    private $totalPages;
    private $baseUrl;

    public function __construct($totalItems, $itemsPerPage = 20, $currentPage = 1, $baseUrl = '')
    {
        $this->totalItems = max(0, (int)$totalItems);
        $this->itemsPerPage = max(1, (int)$itemsPerPage);
        $this->currentPage = max(1, (int)$currentPage);
        $this->totalPages = ceil($this->totalItems / $this->itemsPerPage);
        $this->baseUrl = $baseUrl;
        
        // Ensure current page doesn't exceed total pages
        if ($this->currentPage > $this->totalPages && $this->totalPages > 0) {
            $this->currentPage = $this->totalPages;
        }
    }

    /**
     * Get offset for SQL LIMIT clause
     */
    public function getOffset()
    {
        return ($this->currentPage - 1) * $this->itemsPerPage;
    }

    /**
     * Get limit for SQL LIMIT clause
     */
    public function getLimit()
    {
        return $this->itemsPerPage;
    }

    /**
     * Get current page number
     */
    public function getCurrentPage()
    {
        return $this->currentPage;
    }

    /**
     * Get total pages
     */
    public function getTotalPages()
    {
        return $this->totalPages;
    }

    /**
     * Get total items
     */
    public function getTotalItems()
    {
        return $this->totalItems;
    }

    /**
     * Get items per page
     */
    public function getItemsPerPage()
    {
        return $this->itemsPerPage;
    }

    /**
     * Check if there's a previous page
     */
    public function hasPrevious()
    {
        return $this->currentPage > 1;
    }

    /**
     * Check if there's a next page
     */
    public function hasNext()
    {
        return $this->currentPage < $this->totalPages;
    }

    /**
     * Get previous page number
     */
    public function getPreviousPage()
    {
        return $this->hasPrevious() ? $this->currentPage - 1 : 1;
    }

    /**
     * Get next page number
     */
    public function getNextPage()
    {
        return $this->hasNext() ? $this->currentPage + 1 : $this->totalPages;
    }

    /**
     * Get page range for pagination links
     */
    public function getPageRange($range = 5)
    {
        $start = max(1, $this->currentPage - floor($range / 2));
        $end = min($this->totalPages, $start + $range - 1);
        
        // Adjust start if we're near the end
        if ($end - $start + 1 < $range) {
            $start = max(1, $end - $range + 1);
        }
        
        return range($start, $end);
    }

    /**
     * Generate pagination HTML
     */
    public function render($class = 'pagination')
    {
        if ($this->totalPages <= 1) {
            return '';
        }

        $html = "<div class=\"{$class}\">";
        
        // Previous button
        if ($this->hasPrevious()) {
            $prevUrl = $this->buildUrl($this->getPreviousPage());
            $html .= "<a href=\"{$prevUrl}\" class=\"page-link prev\"><i class=\"fas fa-chevron-left\"></i> Previous</a>";
        } else {
            $html .= "<span class=\"page-link prev disabled\"><i class=\"fas fa-chevron-left\"></i> Previous</span>";
        }

        // Page numbers
        $pageRange = $this->getPageRange();
        
        // First page if not in range
        if ($pageRange[0] > 1) {
            $firstUrl = $this->buildUrl(1);
            $html .= "<a href=\"{$firstUrl}\" class=\"page-link\">1</a>";
            if ($pageRange[0] > 2) {
                $html .= "<span class=\"page-link ellipsis\">...</span>";
            }
        }

        // Page range
        foreach ($pageRange as $page) {
            if ($page == $this->currentPage) {
                $html .= "<span class=\"page-link current\">{$page}</span>";
            } else {
                $pageUrl = $this->buildUrl($page);
                $html .= "<a href=\"{$pageUrl}\" class=\"page-link\">{$page}</a>";
            }
        }

        // Last page if not in range
        if (end($pageRange) < $this->totalPages) {
            if (end($pageRange) < $this->totalPages - 1) {
                $html .= "<span class=\"page-link ellipsis\">...</span>";
            }
            $lastUrl = $this->buildUrl($this->totalPages);
            $html .= "<a href=\"{$lastUrl}\" class=\"page-link\">{$this->totalPages}</a>";
        }

        // Next button
        if ($this->hasNext()) {
            $nextUrl = $this->buildUrl($this->getNextPage());
            $html .= "<a href=\"{$nextUrl}\" class=\"page-link next\">Next <i class=\"fas fa-chevron-right\"></i></a>";
        } else {
            $html .= "<span class=\"page-link next disabled\">Next <i class=\"fas fa-chevron-right\"></i></span>";
        }

        $html .= "</div>";

        return $html;
    }

    /**
     * Build URL for page
     */
    private function buildUrl($page)
    {
        // Get current URL and parameters
        $currentUrl = $_SERVER['REQUEST_URI'];
        $urlParts = parse_url($currentUrl);
        $basePath = $urlParts['path'] ?? '';

        // Parse existing query parameters
        $queryParams = [];
        if (isset($urlParts['query'])) {
            parse_str($urlParts['query'], $queryParams);
        }

        // Add/update page parameter
        $queryParams['page_num'] = $page;

        // Build final URL
        $url = $basePath;
        if (!empty($queryParams)) {
            $url .= '?' . http_build_query($queryParams);
        }

        return $url;
    }

    /**
     * Get pagination info text
     */
    public function getInfoText()
    {
        if ($this->totalItems == 0) {
            return "No items found";
        }

        $start = $this->getOffset() + 1;
        $end = min($this->getOffset() + $this->itemsPerPage, $this->totalItems);
        
        return "Showing {$start} to {$end} of {$this->totalItems} items";
    }

    /**
     * Get pagination data as array
     */
    public function toArray()
    {
        return [
            'current_page' => $this->currentPage,
            'total_pages' => $this->totalPages,
            'total_items' => $this->totalItems,
            'items_per_page' => $this->itemsPerPage,
            'has_previous' => $this->hasPrevious(),
            'has_next' => $this->hasNext(),
            'previous_page' => $this->getPreviousPage(),
            'next_page' => $this->getNextPage(),
            'page_range' => $this->getPageRange(),
            'offset' => $this->getOffset(),
            'info_text' => $this->getInfoText()
        ];
    }
}
