<?php
/**
 * Services Controller
 * Handles service CRUD operations
 */

/**
 * Handle service form submission
 */
function handleServicesForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';
    $id = $data['id'] ?? '';
    
    if ($action === 'save') {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }
        
        // Get default values from settings for new services
        $defaultPrepTime = Application::getSetting('default_preparation_time', 0);
        $defaultCleanupTime = Application::getSetting('default_cleanup_time', 0);
        $defaultEmployeeSelection = Application::getSetting('default_employee_selection', 'auto');

        $serviceData = [
            ':name' => Application::sanitize($data['name']),
            ':description' => Application::sanitize($data['description']),
            ':category_id' => $data['category_id'] ?: null,
            ':duration' => (int)$data['duration'],
            ':price' => (float)$data['price'],
            ':employee_selection' => $data['employee_selection'] ?? $defaultEmployeeSelection,
            ':preparation_time' => isset($data['preparation_time']) ? (int)$data['preparation_time'] : $defaultPrepTime,
            ':cleanup_time' => isset($data['cleanup_time']) ? (int)$data['cleanup_time'] : $defaultCleanupTime,
            ':is_active' => isset($data['is_active']) ? 1 : 0,
            ':updated_at' => date('Y-m-d H:i:s')
        ];

        try {
            $db->beginTransaction();
            
            if ($id) {
                // Update existing service
                $serviceData[':id'] = $id;
                $sql = "UPDATE services SET
                        name = :name,
                        description = :description,
                        category_id = :category_id,
                        duration = :duration,
                        price = :price,
                        employee_selection = :employee_selection,
                        preparation_time = :preparation_time,
                        cleanup_time = :cleanup_time,
                        is_active = :is_active,
                        updated_at = :updated_at
                        WHERE id = :id";
                $serviceId = $id;
            } else {
                // Create new service
                $serviceId = Application::generateId('SRV');
                $serviceData[':id'] = $serviceId;
                $serviceData[':created_at'] = date('Y-m-d H:i:s');
                $sql = "INSERT INTO services (id, name, description, category_id, duration, price,
                        employee_selection, preparation_time, cleanup_time, is_active, created_at, updated_at)
                        VALUES (:id, :name, :description, :category_id, :duration, :price,
                        :employee_selection, :preparation_time, :cleanup_time, :is_active, :created_at, :updated_at)";
            }

            $result = $db->query($sql, $serviceData);

            if ($result !== false) {
                // Handle employee assignments
                // First, remove existing service assignments
                $db->query("DELETE FROM employee_services WHERE service_id = :service_id", [':service_id' => $serviceId]);

                // Add new employee assignments
                if (!empty($data['employee_ids'])) {
                    foreach ($data['employee_ids'] as $employeeId) {
                        $db->query(
                            "INSERT INTO employee_services (employee_id, service_id) VALUES (:employee_id, :service_id)",
                            [':employee_id' => $employeeId, ':service_id' => $serviceId]
                        );
                    }
                }

                // Auto-create translation entries for new service
                if (!$id) { // Only for new services (when $id is empty)
                    try {
                        require_once __DIR__ . '/../../shared/translation.php';
                        // Always create name translation
                        Translation::save("service_name_{$serviceId}", $serviceData[':name'], $serviceData[':name'], 'services');
                        // Always create description translation, even if empty
                        $description = $serviceData[':description'] ?? '';
                        Translation::save("service_description_{$serviceId}", $description, $description, 'services');
                    } catch (Exception $e) {
                        // Text creation failed, but service was created successfully
                        error_log("Failed to create service translations: " . $e->getMessage());
                    }
                }

                $db->commit();
                
                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=services',
                    'message' => $id ? 'Service updated successfully' : 'Service created successfully'
                ];
            } else {
                $db->rollback();
                return ['success' => false, 'error' => 'Failed to save service'];
            }
            
        } catch (Exception $e) {
            $db->rollback();
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    return ['success' => false, 'error' => 'Invalid action'];
}
