<?php

/**
 * Store Admin Dashboard
 * Main entry point for tenant administration interface
 */

// Set proper headers for UTF-8 encoding
header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Ensure UTF-8 encoding
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

session_start();
require_once __DIR__ . '/core/Application.php';
require_once __DIR__ . '/core/Pagination.php';
require_once __DIR__ . '/core/AdminHelpers.php';

Application::init();

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: /store-admin/login.php');
    exit;
}

// Initialize admin translation system safely
try {
    require_once __DIR__ . '/core/AdminTranslation.php';
    $db = Application::getDb();
    AdminTranslation::init($db);
} catch (Exception $e) {
    error_log("Failed to initialize admin translations: " . $e->getMessage());
    // Continue without translations - will use fallback values
}

// Initialize main translation system for translations page
try {
    require_once __DIR__ . '/../shared/translation.php';
    Translation::init();
} catch (Exception $e) {
    error_log("Failed to initialize main translations: " . $e->getMessage());
}

$page = $_GET['page'] ?? 'dashboard';
$action = $_GET['action'] ?? '';
$id = $_GET['id'] ?? '';

// Handle AJAX requests
if (isset($_POST['ajax'])) {
    require_once __DIR__ . '/controllers/ajax.php';
    exit;
}

// Handle form submissions
if ($_POST && !isset($_POST['ajax'])) {
    $result = handleFormSubmission($_POST, $page);
    if (isset($result['redirect'])) {
        Application::redirect($result['redirect'], $result['message'] ?? '', $result['type'] ?? 'success');
    }
}

/**
 * Handle form submissions
 */
function handleFormSubmission(array $data, string $page): array
{
    // Map page names to controller files
    $pageToController = [
        'add-customer' => 'customers',
        'edit-customer' => 'customers',
        'view-customer' => 'customers',
        'add-service' => 'services',
        'edit-service' => 'services',
        'view-service' => 'services',
        'add-employee' => 'employees',
        'edit-employee' => 'employees',
        'view-employee' => 'employees',
        'add-category' => 'categories',
        'edit-category' => 'categories',
        'view-category' => 'categories',
        'reorder-categories' => 'categories',
        'add-reservation' => 'reservations',
        'edit-reservation' => 'reservations',
        'view-reservation' => 'reservations',
    ];

    $controllerName = $pageToController[$page] ?? $page;
    $controllerFile = __DIR__ . "/controllers/{$controllerName}.php";

    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $functionName = "handle" . ucfirst($controllerName) . "Form";
        if (function_exists($functionName)) {
            return $functionName($data, Application::getDb());
        }
    }

    return ['success' => false, 'error' => 'Controller not found'];
}

$db = Application::getDb();
$flashMessage = Application::getFlashMessage();

// Get dashboard statistics
$stats = [];
if ($page === 'dashboard') {
    $stats = [
        'total_categories' => $db->fetchRow("SELECT COUNT(*) as count FROM categories")['count'],
        'total_services' => $db->fetchRow("SELECT COUNT(*) as count FROM services")['count'],
        'total_employees' => $db->fetchRow("SELECT COUNT(*) as count FROM employees")['count'],
        'total_customers' => $db->fetchRow("SELECT COUNT(*) as count FROM customers")['count'],
        'total_reservations' => $db->fetchRow("SELECT COUNT(*) as count FROM reservations")['count'],
        'recent_reservations' => $db->fetchAll("
            SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
            FROM reservations r
            LEFT JOIN customers c ON r.customer_id = c.id
            LEFT JOIN services s ON r.service_id = s.id
            LEFT JOIN employees e ON r.employee_id = e.id
            ORDER BY r.created_at DESC LIMIT 5
        ")
    ];
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <title><?php echo ucfirst($page); ?> - Store Admin</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/store-admin/assets/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/store-admin/assets/favicon.svg">

    <!-- Enhanced Admin Styles -->
    <link rel="stylesheet" href="/store-admin/assets/admin.css?v=<?php echo time(); ?>">
    <!-- Use Font Awesome CDN without integrity check to avoid hash mismatch -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" crossorigin="anonymous">
</head>

<body>
    <div class="layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><?php echo htmlspecialchars(Application::getSetting('business_name', 'Store Admin')); ?></h1>
                <button class="sidebar-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="/store-admin/" class="nav-link <?php echo $page === 'dashboard' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        <span class="nav-text"><?= at('dashboard') ?></span>
                    </a>
                    <a href="/store-admin/?page=customers" class="nav-link <?php echo $page === 'customers' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span class="nav-text"><?= at('customers') ?></span>
                        <?php
                        // Get customer count for badge
                        try {
                            $customerCount = $db->fetchColumn("SELECT COUNT(*) FROM customers");
                            if ($customerCount > 0): ?>
                                <span class="nav-badge"><?php echo $customerCount; ?></span>
                        <?php endif;
                        } catch (Exception $e) {
                            // Ignore errors for badge count
                        }
                        ?>
                    </a>
                    <a href="/store-admin/?page=services" class="nav-link <?php echo $page === 'services' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
                        </svg>
                        <span class="nav-text"><?= at('services') ?></span>
                    </a>
                    <a href="/store-admin/?page=employees" class="nav-link <?php echo $page === 'employees' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="8.5" cy="7" r="4"></circle>
                            <line x1="20" y1="8" x2="20" y2="14"></line>
                            <line x1="23" y1="11" x2="17" y2="11"></line>
                        </svg>
                        <span class="nav-text"><?= at('employees') ?></span>
                    </a>
                    <a href="/store-admin/?page=categories" class="nav-link <?php echo $page === 'categories' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                            <line x1="7" y1="7" x2="7.01" y2="7"></line>
                        </svg>
                        <span class="nav-text"><?= at('categories') ?></span>
                    </a>
                    <a href="/store-admin/?page=reservations" class="nav-link <?php echo $page === 'reservations' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        <span class="nav-text"><?= at('reservations') ?></span>
                        <?php
                        // Get pending reservations count for badge
                        try {
                            $pendingCount = $db->fetchColumn("SELECT COUNT(*) FROM reservations WHERE status = 'pending' OR (status = 'confirmed' AND date = CURDATE())");
                            if ($pendingCount > 0): ?>
                                <span class="nav-badge"><?php echo $pendingCount; ?></span>
                        <?php endif;
                        } catch (Exception $e) {
                            // Ignore errors for badge count
                        }
                        ?>
                    </a>
                    <a href="/store-admin/?page=calendar" class="nav-link <?php echo $page === 'calendar' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                            <rect x="8" y="14" width="2" height="2"></rect>
                        </svg>
                        <span class="nav-text"><?= at('calendar') ?></span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title"><?= at('management') ?></div>
                    <a href="/store-admin/?page=settings" class="nav-link <?php echo $page === 'settings' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m10-5a2 2 0 1 1 0 4 2 2 0 0 1 0-4z"></path>
                        </svg>
                        <span class="nav-text"><?= at('settings') ?></span>
                    </a>
                    <a href="/store-admin/?page=translations" class="nav-link <?php echo $page === 'translations' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M5 8l6 6m-7 0l6-6 2-3M2 5h12M7 2h1m14 20l-5-10-5 10m2-4h6"></path>
                        </svg>
                        <span class="nav-text"><?= at('translations') ?></span>
                    </a>
                    <a href="/store-admin/?page=email_logs" class="nav-link <?php echo $page === 'email_logs' ? 'active' : ''; ?>">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <span class="nav-text"><?= at('email_logs') ?></span>
                        <?php
                        // Get recent email count for badge
                        try {
                            $recentEmailCount = $db->fetchColumn("SELECT COUNT(*) FROM email_log WHERE DATE(sent_at) = CURDATE()");
                            if ($recentEmailCount > 0): ?>
                                <span class="nav-badge"><?php echo $recentEmailCount; ?></span>
                        <?php endif;
                        } catch (Exception $e) {
                            // Ignore errors for badge count
                        }
                        ?>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="/store-admin/logout.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        <span class="nav-text"><?= at('logout') ?></span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <button class="mobile-menu-btn" id="mobile-menu-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="3" y1="6" x2="21" y2="6"></line>
                            <line x1="3" y1="12" x2="21" y2="12"></line>
                            <line x1="3" y1="18" x2="21" y2="18"></line>
                        </svg>
                    </button>
                    <div class="header-title-section">
                        <h1 class="header-title"><?php echo ucfirst($page); ?></h1>
                        <div class="breadcrumb">
                            <span>Store</span>
                            <span class="breadcrumb-separator">/</span>
                            <span><?php echo ucfirst($page); ?></span>
                        </div>
                    </div>
                </div>
                <div class="header-right">
                    <div class="header-search">
                        <input type="text" class="search-input" placeholder="Search...">
                        <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"></circle>
                            <path d="m21 21-4.35-4.35"></path>
                        </svg>
                    </div>
                </div>
            </header>

            <main class="main">

                <div class="container">
                    <!-- Flash Messages -->
                    <?php if (!empty($flashMessage['message'])): ?>
                        <div class="flash-message flash-<?php echo $flashMessage['type']; ?>">
                            <?php echo htmlspecialchars($flashMessage['message']); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Page Content -->
                    <?php
                    $viewFile = __DIR__ . "/views/{$page}.php";
                    if (file_exists($viewFile)) {
                        require_once $viewFile;
                    } else {
                        echo "<p>Page not found: {$page}</p>";
                    }
                    ?>
                </div>
            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <div class="loading-text">Loading...</div>
        </div>
    </div>

    <script src="/store-admin/assets/admin.js?v=6.0&t=<?php echo time(); ?>"></script>
</body>

</html>