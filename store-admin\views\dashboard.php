<?php
/**
 * Dashboard View
 * Main dashboard with statistics and overview
 */

// Get recent activity
$recentReservations = $db->fetchAll("
    SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
    FROM reservations r
    LEFT JOIN customers c ON r.customer_id = c.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN employees e ON r.employee_id = e.id
    ORDER BY r.created_at DESC LIMIT 5
");

$upcomingReservations = $db->fetchAll("
    SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
    FROM reservations r
    LEFT JOIN customers c ON r.customer_id = c.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN employees e ON r.employee_id = e.id
    WHERE r.date >= :today AND r.status != 'cancelled'
    ORDER BY r.date ASC, r.start_time ASC LIMIT 5
", [':today' => date('Y-m-d')]);

$todaysReservations = $db->fetchAll("
    SELECT r.*, c.name as customer_name, s.name as service_name, e.name as employee_name
    FROM reservations r
    LEFT JOIN customers c ON r.customer_id = c.id
    LEFT JOIN services s ON r.service_id = s.id
    LEFT JOIN employees e ON r.employee_id = e.id
    WHERE r.date = :today
    ORDER BY r.start_time ASC
", [':today' => date('Y-m-d')]);

// Get monthly statistics
$currentMonth = date('Y-m');
$monthlyStats = [
    'reservations' => $db->fetchRow("SELECT COUNT(*) as count FROM reservations WHERE date LIKE :month", [':month' => $currentMonth . '%'])['count'],
    'revenue' => $db->fetchRow("SELECT SUM(price) as total FROM reservations WHERE date LIKE :month AND status != 'cancelled'", [':month' => $currentMonth . '%'])['total'] ?? 0,
    'new_customers' => $db->fetchRow("SELECT COUNT(*) as count FROM customers WHERE created_at LIKE :month", [':month' => $currentMonth . '%'])['count']
];
?>

<!-- Dashboard Container -->
<div class="dashboard-container">
    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
    <!-- Overview Statistics -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Store Overview</h2>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value"><?php echo $stats['total_categories']; ?></div>
                    <div class="stat-label">Categories</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $stats['total_services']; ?></div>
                    <div class="stat-label">Services</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $stats['total_employees']; ?></div>
                    <div class="stat-label">Employees</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $stats['total_customers']; ?></div>
                    <div class="stat-label">Customers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $stats['total_reservations']; ?></div>
                    <div class="stat-label">Total Reservations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo count($todaysReservations); ?></div>
                    <div class="stat-label">Today</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Statistics -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">This Month</h2>
        </div>
        <div class="card-body">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value"><?php echo $monthlyStats['reservations']; ?></div>
                    <div class="stat-label">Reservations</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">€<?php echo number_format($monthlyStats['revenue'], 2); ?></div>
                    <div class="stat-label">Revenue</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo $monthlyStats['new_customers']; ?></div>
                    <div class="stat-label">New Customers</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Reservations -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Today's Reservations</h2>
            <a href="/store-admin/?page=reservations&date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline btn-sm">View All</a>
        </div>
        <div class="card-body">
            <?php if (empty($todaysReservations)): ?>
                <div class="empty-state">
                    <p>No reservations for today.</p>
                    <a href="/store-admin/?page=reservations&action=add" class="btn btn-primary">Create First Reservation</a>
                </div>
            <?php else: ?>
                <div class="reservation-list">
                    <?php foreach ($todaysReservations as $reservation): ?>
                        <div class="reservation-item">
                            <div class="reservation-time">
                                <?php echo date('H:i', strtotime($reservation['start_time'])); ?>
                            </div>
                            <div class="reservation-details">
                                <div class="reservation-customer">
                                    <strong><?php echo htmlspecialchars($reservation['customer_name']); ?></strong>
                                </div>
                                <div class="reservation-service">
                                    <?php echo htmlspecialchars($reservation['service_name']); ?>
                                    <?php if ($reservation['employee_name']): ?>
                                        - <?php echo htmlspecialchars($reservation['employee_name']); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="reservation-status">
                                <span class="status-badge <?php echo $reservation['status']; ?>">
                                    <?php echo ucfirst($reservation['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Upcoming Reservations -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Upcoming Reservations</h2>
            <a href="/store-admin/?page=reservations" class="btn btn-outline btn-sm">View All</a>
        </div>
        <div class="card-body">
            <?php if (empty($upcomingReservations)): ?>
                <div class="empty-state">
                    <p>No upcoming reservations.</p>
                    <a href="/store-admin/?page=reservations&action=add" class="btn btn-primary">Create Reservation</a>
                </div>
            <?php else: ?>
                <div class="reservation-list">
                    <?php foreach ($upcomingReservations as $reservation): ?>
                        <div class="reservation-item">
                            <div class="reservation-date">
                                <?php echo date('M j', strtotime($reservation['date'])); ?>
                                <br>
                                <small><?php echo date('H:i', strtotime($reservation['start_time'])); ?></small>
                            </div>
                            <div class="reservation-details">
                                <div class="reservation-customer">
                                    <strong><?php echo htmlspecialchars($reservation['customer_name']); ?></strong>
                                </div>
                                <div class="reservation-service">
                                    <?php echo htmlspecialchars($reservation['service_name']); ?>
                                    <?php if ($reservation['employee_name']): ?>
                                        - <?php echo htmlspecialchars($reservation['employee_name']); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="reservation-status">
                                <span class="status-badge <?php echo $reservation['status']; ?>">
                                    <?php echo ucfirst($reservation['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">Quick Actions</h2>
        </div>
        <div class="card-body">
            <div class="action-buttons">
                <button class="btn btn-success" onclick="addReservation()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="8" x2="12" y2="16"></line>
                        <line x1="8" y1="12" x2="16" y2="12"></line>
                    </svg>
                    New Reservation
                </button>
                <button class="btn btn-info" onclick="addCustomer()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="8.5" cy="7" r="4"></circle>
                        <line x1="20" y1="8" x2="20" y2="14"></line>
                        <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                    Add Customer
                </button>
                <button class="btn btn-warning" onclick="addService()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"></path>
                    </svg>
                    Add Service
                </button>
                <button class="btn btn-secondary" onclick="addEmployee()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="8.5" cy="7" r="4"></circle>
                        <line x1="20" y1="8" x2="20" y2="14"></line>
                        <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                    Add Employee
                </button>
            </div>
        </div>
    </div>
    </div>
</div>
