<?php

/**
 * Dummy Data Generator
 * Generates test data for tenant databases
 */

class DummyData
{
    private array $categories = [
        [
            'name' => 'Κομμωτήριο',
            'name_en' => 'Hair Salon',
            'description' => 'Υπηρεσίες κομμωτηρίου για άνδρες και γυναίκες',
            'description_en' => 'Hair salon services for men and women',
            'icon' => 'fas fa-cut',
            'color' => '#FF6B6B'
        ],
        [
            'name' => 'Αισθητική',
            'name_en' => 'Beauty',
            'description' => 'Υπηρεσίες αισθητικής και περιποίησης προσώπου',
            'description_en' => 'Beauty and facial care services',
            'icon' => 'fas fa-spa',
            'color' => '#4ECDC4'
        ],
        [
            'name' => 'Μασάζ',
            'name_en' => 'Massage',
            'description' => 'Θεραπευτικά και χαλαρωτικά μασάζ',
            'description_en' => 'Therapeutic and relaxation massage',
            'icon' => 'fas fa-hand-holding-heart',
            'color' => '#45B7D1'
        ],
        [
            'name' => 'Νύχια',
            'name_en' => 'Nails',
            'description' => 'Υπηρεσίες περιποίησης νυχιών',
            'description_en' => 'Nail care services',
            'icon' => 'fas fa-hand-sparkles',
            'color' => '#96CEB4'
        ],
        [
            'name' => 'Wellness',
            'name_en' => 'Wellness',
            'description' => 'Υπηρεσίες ευεξίας και χαλάρωσης',
            'description_en' => 'Wellness and relaxation services',
            'icon' => 'fas fa-leaf',
            'color' => '#F7DC6F'
        ],
        [
            'name' => 'Fitness',
            'name_en' => 'Fitness',
            'description' => 'Προσωπική εκγύμναση και συμβουλευτική',
            'description_en' => 'Personal training and fitness consulting',
            'icon' => 'fas fa-dumbbell',
            'color' => '#BB8FCE'
        ]
    ];

    private array $services = [
        // Hair services
        [
            'category' => 'Κομμωτήριο',
            'name' => 'Κούρεμα Ανδρικό',
            'name_en' => 'Men\'s Haircut',
            'description' => 'Κλασικό ανδρικό κούρεμα με ψαλίδι και μηχανή',
            'description_en' => 'Classic men\'s haircut with scissors and clippers',
            'duration' => 30,
            'price' => 15.00
        ],
        [
            'category' => 'Κομμωτήριο',
            'name' => 'Κούρεμα Γυναικείο',
            'name_en' => 'Women\'s Haircut',
            'description' => 'Κούρεμα και styling για γυναίκες',
            'description_en' => 'Haircut and styling for women',
            'duration' => 45,
            'price' => 25.00
        ],
        [
            'category' => 'Κομμωτήριο',
            'name' => 'Βαφή Μαλλιών',
            'name_en' => 'Hair Coloring',
            'description' => 'Βαφή μαλλιών με επαγγελματικά προϊόντα',
            'description_en' => 'Hair coloring with professional products',
            'duration' => 120,
            'price' => 45.00
        ],

        // Beauty services
        [
            'category' => 'Αισθητική',
            'name' => 'Καθαρισμός Προσώπου',
            'name_en' => 'Facial Cleansing',
            'description' => 'Βαθύς καθαρισμός προσώπου με ατμό και εξαγωγή κομεδόνων',
            'description_en' => 'Deep facial cleansing with steam and blackhead extraction',
            'duration' => 60,
            'price' => 35.00
        ],
        [
            'category' => 'Αισθητική',
            'name' => 'Μάσκα Προσώπου',
            'name_en' => 'Face Mask',
            'description' => 'Ενυδατική μάσκα προσώπου για όλους τους τύπους δέρματος',
            'description_en' => 'Hydrating face mask for all skin types',
            'duration' => 45,
            'price' => 25.00
        ],
        [
            'category' => 'Αισθητική',
            'name' => 'Αποτρίχωση Ποδιών',
            'name_en' => 'Leg Waxing',
            'description' => 'Αποτρίχωση ποδιών με κερί για μακροχρόνια αποτελέσματα',
            'description_en' => 'Leg waxing with wax for long-lasting results',
            'duration' => 45,
            'price' => 20.00
        ],

        // Massage services
        [
            'category' => 'Μασάζ',
            'name' => 'Μασάζ Χαλάρωσης',
            'name_en' => 'Relaxation Massage',
            'description' => 'Χαλαρωτικό μασάζ ολόκληρου του σώματος',
            'description_en' => 'Full body relaxation massage',
            'duration' => 60,
            'price' => 40.00
        ],
        [
            'category' => 'Μασάζ',
            'name' => 'Μασάζ Αθλητικό',
            'name_en' => 'Sports Massage',
            'description' => 'Θεραπευτικό μασάζ για αθλητές και ενεργούς ανθρώπους',
            'description_en' => 'Therapeutic massage for athletes and active people',
            'duration' => 45,
            'price' => 35.00
        ],

        // Nail services
        ['category' => 'Νύχια', 'name' => 'Μανικιούρ', 'name_en' => 'Manicure', 'description' => 'Περιποίηση νυχιών χεριών με βερνίκι', 'duration' => 45, 'price' => 15.00],
        ['category' => 'Νύχια', 'name' => 'Πεντικιούρ', 'name_en' => 'Pedicure', 'description' => 'Περιποίηση νυχιών ποδιών με βερνίκι', 'duration' => 60, 'price' => 20.00],
        ['category' => 'Νύχια', 'name' => 'Gel Nails', 'name_en' => 'Gel Nails', 'description' => 'Εφαρμογή gel στα νύχια για μακροχρόνια διάρκεια', 'duration' => 90, 'price' => 35.00],

        // Wellness services
        ['category' => 'Wellness', 'name' => 'Αρωματοθεραπεία', 'name_en' => 'Aromatherapy', 'description' => 'Θεραπεία με αιθέρια έλαια για χαλάρωση', 'duration' => 60, 'price' => 45.00],
        ['category' => 'Wellness', 'name' => 'Μεσογειακή Διατροφή', 'name_en' => 'Mediterranean Diet Consultation', 'description' => 'Συμβουλευτική για υγιεινή διατροφή', 'duration' => 45, 'price' => 30.00],

        // Fitness services
        ['category' => 'Fitness', 'name' => 'Προσωπική Εκγύμναση', 'name_en' => 'Personal Training', 'description' => 'Εξατομικευμένο πρόγραμμα γυμναστικής', 'duration' => 60, 'price' => 50.00],
        ['category' => 'Fitness', 'name' => 'Yoga Session', 'name_en' => 'Yoga Session', 'description' => 'Ατομικό μάθημα yoga για ευελιξία και χαλάρωση', 'duration' => 75, 'price' => 40.00]
    ];

    private array $employees = [
        [
            'name' => 'Μαρία Παπαδοπούλου',
            'name_en' => 'Maria Papadopoulou',
            'email' => '<EMAIL>',
            'phone' => '6901234567',
            'position' => 'Senior Hair Stylist',
            'color' => '#FF6B6B',
            'specialization' => 'hair',
            'working_hours' => [
                'monday' => [['start' => '09:00', 'end' => '17:00']],
                'tuesday' => [['start' => '09:00', 'end' => '17:00']],
                'wednesday' => [['start' => '09:00', 'end' => '17:00']],
                'thursday' => [['start' => '09:00', 'end' => '17:00']],
                'friday' => [['start' => '09:00', 'end' => '17:00']],
                'saturday' => [['start' => '09:00', 'end' => '14:00'], ['start' => '17:00', 'end' => '20:00']]
            ]
        ],
        [
            'name' => 'Γιάννης Κωνσταντίνου',
            'name_en' => 'Yannis Konstantinou',
            'email' => '<EMAIL>',
            'phone' => '6902345678',
            'position' => 'Hair Colorist & Barber',
            'color' => '#4ECDC4',
            'specialization' => 'hair',
            'working_hours' => [
                'tuesday' => [['start' => '10:00', 'end' => '14:00'], ['start' => '16:00', 'end' => '20:00']],
                'wednesday' => [['start' => '10:00', 'end' => '18:00']],
                'thursday' => [['start' => '10:00', 'end' => '14:00'], ['start' => '16:00', 'end' => '20:00']],
                'friday' => [['start' => '10:00', 'end' => '18:00']],
                'saturday' => [['start' => '09:00', 'end' => '17:00']]
            ]
        ],
        [
            'name' => 'Ελένη Γεωργίου',
            'name_en' => 'Eleni Georgiou',
            'email' => '<EMAIL>',
            'phone' => '6903456789',
            'position' => 'Nail Technician',
            'color' => '#45B7D1',
            'specialization' => 'nails',
            'working_hours' => [
                'monday' => [['start' => '11:00', 'end' => '19:00']],
                'tuesday' => [['start' => '11:00', 'end' => '19:00']],
                'wednesday' => [['start' => '11:00', 'end' => '19:00']],
                'thursday' => [['start' => '11:00', 'end' => '19:00']],
                'friday' => [['start' => '11:00', 'end' => '19:00']]
            ]
        ],
        [
            'name' => 'Σοφία Αντωνίου',
            'name_en' => 'Sofia Antoniou',
            'email' => '<EMAIL>',
            'phone' => '6904567890',
            'position' => 'Beauty Specialist',
            'color' => '#96CEB4',
            'specialization' => 'beauty',
            'working_hours' => [
                'monday' => [['start' => '08:00', 'end' => '14:00']],
                'wednesday' => [['start' => '08:00', 'end' => '14:00']],
                'friday' => [['start' => '08:00', 'end' => '14:00']],
                'saturday' => [['start' => '10:00', 'end' => '16:00']]
            ]
        ]
    ];

    private array $customers = [
        ['name' => 'Κατερίνα Αλεξίου', 'email' => '<EMAIL>', 'phone' => '6911111111'],
        ['name' => 'Γιάννης Νικολάου', 'email' => '<EMAIL>', 'phone' => '6922222222'],
        ['name' => 'Δέσποινα Μιχαήλ', 'email' => '<EMAIL>', 'phone' => '6933333333'],
        ['name' => 'Νίκος Παπαγιάννης', 'email' => '<EMAIL>', 'phone' => '6944444444'],
        ['name' => 'Χριστίνα Βασιλείου', 'email' => '<EMAIL>', 'phone' => '6955555555'],
        ['name' => 'Δημήτρης Κωστόπουλος', 'email' => '<EMAIL>', 'phone' => '6966666666']
    ];

    public function generateCategories(Database $db): int
    {
        $count = 0;
        foreach ($this->categories as $category) {
            $categoryId = 'CAT' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

            $db->query(
                "INSERT OR IGNORE INTO categories (id, name, name_en, description, icon, color, is_active, created_at)
                 VALUES (:id, :name, :name_en, :description, :icon, :color, 1, :created)",
                [
                    ':id' => $categoryId,
                    ':name' => $category['name'],
                    ':name_en' => $category['name_en'],
                    ':description' => $category['description'],
                    ':icon' => $category['icon'],
                    ':color' => $category['color'],
                    ':created' => date('Y-m-d H:i:s')
                ]
            );

            // Create translation entries for category
            $this->createCategoryTranslations($db, $categoryId, $category['name'], $category['name_en'], $category['description'], $category['description_en'] ?? '');

            $count++;
        }
        return $count;
    }

    public function generateServices(Database $db): int
    {
        
        $count = 0;
        foreach ($this->services as $service) {
            // Get category ID
            $category = $db->fetchRow(
                "SELECT id FROM categories WHERE name = :name",
                [':name' => $service['category']]
            );
            
            if ($category) {
                $serviceId = 'SRV' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

                $db->query(
                    "INSERT OR IGNORE INTO services (id, category_id, name, name_en, description, duration, price, employee_selection, preparation_time, cleanup_time, is_active, created_at)
                     VALUES (:id, :category_id, :name, :name_en, :description, :duration, :price, :employee_selection, :preparation_time, :cleanup_time, 1, :created)",
                    [
                        ':id' => $serviceId,
                        ':category_id' => $category['id'],
                        ':name' => $service['name'],
                        ':name_en' => $service['name_en'],
                        ':description' => $service['description'],
                        ':duration' => $service['duration'],
                        ':price' => $service['price'],
                        ':employee_selection' => 'auto',
                        ':preparation_time' => 0,
                        ':cleanup_time' => 0,
                        ':created' => date('Y-m-d H:i:s')
                    ]
                );

                // Create translation entries for service
                $this->createServiceTranslations($db, $serviceId, $service['name'], $service['name_en'], $service['description'], $service['description_en'] ?? '');

                $count++;
            }
        }
        return $count;
    }

    public function generateEmployees(Database $db): int
    {
        $count = 0;

        foreach ($this->employees as $employee) {
            $employeeId = 'EMP' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

            // Use employee-specific working hours
            $workingHours = $employee['working_hours'];

            // Create employee
            $db->query(
                "INSERT OR IGNORE INTO employees (id, name, name_en, email, phone, position, color, working_hours, is_active, created_at)
                 VALUES (:id, :name, :name_en, :email, :phone, :position, :color, :working_hours, 1, :created)",
                [
                    ':id' => $employeeId,
                    ':name' => $employee['name'],
                    ':name_en' => $employee['name_en'],
                    ':email' => $employee['email'],
                    ':phone' => $employee['phone'],
                    ':position' => $employee['position'],
                    ':color' => $employee['color'],
                    ':working_hours' => json_encode($workingHours),
                    ':created' => date('Y-m-d H:i:s')
                ]
            );
            $count++;
        }
        return $count;
    }

    public function generateCustomers(Database $db): int
    {
        $count = 0;
        foreach ($this->customers as $customer) {
            $db->query(
                "INSERT OR IGNORE INTO customers (id, name, email, phone, language, created_at)
                 VALUES (:id, :name, :email, :phone, :language, :created)",
                [
                    ':id' => 'CUS' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8)),
                    ':name' => $customer['name'],
                    ':email' => $customer['email'],
                    ':phone' => $customer['phone'],
                    ':language' => 'el',
                    ':created' => date('Y-m-d H:i:s')
                ]
            );
            $count++;
        }
        return $count;
    }

    public function generateReservations(Database $db): int
    {
        $services = $db->fetchAll("SELECT * FROM services WHERE is_active = 1");
        $employees = $db->fetchAll("SELECT id FROM employees WHERE is_active = 1");
        $customers = $db->fetchAll("SELECT id FROM customers");

        if (empty($services) || empty($employees) || empty($customers)) {
            return 0;
        }

        $count = 0;
        $statuses = ['confirmed', 'completed', 'cancelled'];

        // Generate reservations for the past 7 days and next 7 days
        for ($i = -7; $i <= 7; $i++) {
            $date = date('Y-m-d', strtotime("$i days"));

            // Generate 0-3 reservations per day
            $dailyReservations = rand(0, 3);
            $attempts = 0;
            $maxAttempts = 20; // Prevent infinite loops

            for ($j = 0; $j < $dailyReservations && $attempts < $maxAttempts; $j++) {
                $attempts++;

                $service = $services[array_rand($services)];
                $employee = $employees[array_rand($employees)];
                $customer = $customers[array_rand($customers)];

                $hour = rand(9, 16); // 9 AM to 4 PM (to allow for service duration)
                $minute = rand(0, 3) * 15; // 0, 15, 30, 45 minutes
                $startTime = sprintf('%02d:%02d', $hour, $minute);

                // Simple conflict check - just check if employee has another reservation at same time
                if ($i >= 0) {
                    $conflict = $db->fetchRow(
                        "SELECT 1 FROM reservations
                         WHERE employee_id = :emp_id
                         AND date = :date
                         AND start_time = :time
                         AND status != 'cancelled'",
                        [
                            ':emp_id' => $employee['id'],
                            ':date' => $date,
                            ':time' => $startTime
                        ]
                    );

                    if ($conflict) {
                        $j--; // Try again with different time/employee
                        continue;
                    }
                }

                // Calculate end time based on service duration
                $endDateTime = new DateTime($date . ' ' . $startTime);
                $endDateTime->add(new DateInterval('PT' . $service['duration'] . 'M'));
                $endTime = $endDateTime->format('H:i');

                $status = $i < 0 ? $statuses[array_rand($statuses)] : 'confirmed';

                try {
                    $db->query(
                        "INSERT INTO reservations (id, customer_id, service_id, employee_id, date, start_time, end_time, status, price, created_at)
                         VALUES (:id, :customer_id, :service_id, :employee_id, :date, :start_time, :end_time, :status, :price, :created)",
                        [
                            ':id' => 'RSV' . strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8)),
                            ':customer_id' => $customer['id'],
                            ':service_id' => $service['id'],
                            ':employee_id' => $employee['id'],
                            ':date' => $date,
                            ':start_time' => $startTime,
                            ':end_time' => $endTime,
                            ':status' => $status,
                            ':price' => $service['price'],
                            ':created' => date('Y-m-d H:i:s')
                        ]
                    );
                    $count++;
                } catch (Exception $e) {
                    // Skip this reservation if it conflicts (database constraint)
                    error_log("Dummy reservation conflict: " . $e->getMessage());
                    $j--; // Try again
                }
            }
        }

        return $count;
    }

    public function generateAll(Database $db): array
    {
        $results = [];

        // First, create essential client translations
        $results['translations'] = $this->generateClientTranslations($db);

        $results['categories'] = $this->generateCategories($db);
        $results['services'] = $this->generateServices($db);
        $results['employees'] = $this->generateEmployees($db);
        $results['employee_services'] = $this->generateEmployeeServices($db);
        $results['customers'] = $this->generateCustomers($db);
        $results['reservations'] = $this->generateReservations($db);

        return $results;
    }

    public function generateEmployeeServices(Database $db): int
    {
        $count = 0;

        // Get all employees and services with categories
        $employees = $db->fetchAll("SELECT id, name FROM employees WHERE is_active = 1");
        $services = $db->fetchAll("
            SELECT s.id, s.name, c.name as category_name
            FROM services s
            LEFT JOIN categories c ON s.category_id = c.id
            WHERE s.is_active = 1
        ");

        if (empty($employees) || empty($services)) {
            return 0;
        }

        // Create realistic employee-service assignments based on specializations
        $employeeSpecializations = [];
        $employeeIndex = 0;
        foreach ($this->employees as $empData) {
            if ($employeeIndex < count($employees)) {
                $employeeSpecializations[$employees[$employeeIndex]['id']] = $empData['specialization'];
                $employeeIndex++;
            }
        }

        foreach ($employees as $employee) {
            $specialization = $employeeSpecializations[$employee['id']] ?? 'general';

            foreach ($services as $service) {
                $shouldAssign = false;

                // Assign services based on specialization
                switch ($specialization) {
                    case 'hair':
                        $shouldAssign = stripos($service['category_name'], 'Κομμωτήριο') !== false ||
                                       stripos($service['name'], 'Κούρεμα') !== false ||
                                       stripos($service['name'], 'Βαφή') !== false;
                        break;
                    case 'nails':
                        $shouldAssign = stripos($service['category_name'], 'Νύχια') !== false ||
                                       stripos($service['name'], 'Μανικιούρ') !== false ||
                                       stripos($service['name'], 'Πεντικιούρ') !== false;
                        break;
                    case 'beauty':
                        $shouldAssign = stripos($service['category_name'], 'Αισθητική') !== false ||
                                       stripos($service['name'], 'Καθαρισμός') !== false ||
                                       stripos($service['name'], 'Μάσκα') !== false ||
                                       stripos($service['name'], 'Αποτρίχωση') !== false;
                        break;
                    default:
                        // General employees can do basic services
                        $shouldAssign = stripos($service['category_name'], 'Μασάζ') !== false;
                        break;
                }

                if ($shouldAssign) {
                    $db->query(
                        "INSERT OR IGNORE INTO employee_services (employee_id, service_id) VALUES (:emp_id, :svc_id)",
                        [
                            ':emp_id' => $employee['id'],
                            ':svc_id' => $service['id']
                        ]
                    );
                    $count++;
                }
            }
        }

        return $count;
    }

    /**
     * Generate essential client translations for the booking interface
     */
    public function generateClientTranslations(Database $db): int
    {
        // Essential client translations that are used in the booking interface
        $clientTranslations = [
            // Main interface
            'online_booking' => ['el' => 'Κλείστε Ραντεβού Online', 'en' => 'Book Appointment Online'],
            'book_appointment_online' => ['el' => 'Κλείστε το ραντεβού σας online', 'en' => 'Book your appointment online'],

            // Step 1 - Categories
            'choose_category' => ['el' => 'Επιλέξτε Κατηγορία', 'en' => 'Choose a Category'],
            'select_category_message' => ['el' => 'Παρακαλώ επιλέξτε μια κατηγορία υπηρεσιών', 'en' => 'Please select a service category'],

            // Step 2 - Services
            'select_service' => ['el' => 'Επιλέξτε Υπηρεσία', 'en' => 'Select a Service'],
            'available_services' => ['el' => 'Διαθέσιμες Υπηρεσίες', 'en' => 'Available Services'],
            'duration' => ['el' => 'Διάρκεια', 'en' => 'Duration'],
            'price' => ['el' => 'Τιμή', 'en' => 'Price'],
            'minutes' => ['el' => 'λεπτά', 'en' => 'minutes'],

            // Step 3 - Employee
            'select_employee' => ['el' => 'Επιλέξτε Εργαζόμενο', 'en' => 'Select Employee'],
            'any_employee' => ['el' => 'Οποιοσδήποτε διαθέσιμος', 'en' => 'Any available'],

            // Step 4 - Date & Time
            'select_date_time' => ['el' => 'Επιλέξτε Ημερομηνία & Ώρα', 'en' => 'Select Date & Time'],
            'available_times' => ['el' => 'Διαθέσιμες Ώρες', 'en' => 'Available Times'],
            'no_available_times' => ['el' => 'Δεν υπάρχουν διαθέσιμες ώρες', 'en' => 'No available times'],

            // Step 5 - Contact Details
            'contact_details' => ['el' => 'Στοιχεία Επικοινωνίας', 'en' => 'Contact Details'],
            'full_name' => ['el' => 'Ονοματεπώνυμο', 'en' => 'Full Name'],
            'email_address' => ['el' => 'Email', 'en' => 'Email Address'],
            'phone_number' => ['el' => 'Τηλέφωνο', 'en' => 'Phone Number'],
            'notes' => ['el' => 'Σημειώσεις', 'en' => 'Notes'],
            'optional' => ['el' => 'προαιρετικό', 'en' => 'optional'],

            // Navigation
            'next' => ['el' => 'Επόμενο', 'en' => 'Next'],
            'previous' => ['el' => 'Προηγούμενο', 'en' => 'Previous'],
            'confirm' => ['el' => 'Επιβεβαίωση', 'en' => 'Confirm'],
            'back' => ['el' => 'Πίσω', 'en' => 'Back'],

            // Confirmation
            'booking_confirmed' => ['el' => 'Το ραντεβού επιβεβαιώθηκε!', 'en' => 'Booking Confirmed!'],
            'booking_summary' => ['el' => 'Περίληψη Ραντεβού', 'en' => 'Booking Summary'],
            'confirmation_sent' => ['el' => 'Στάλθηκε email επιβεβαίωσης', 'en' => 'Confirmation email sent'],

            // Common
            'loading' => ['el' => 'Φόρτωση...', 'en' => 'Loading...'],
            'please_wait' => ['el' => 'Παρακαλώ περιμένετε', 'en' => 'Please wait'],
            'error' => ['el' => 'Σφάλμα', 'en' => 'Error'],
            'try_again' => ['el' => 'Δοκιμάστε ξανά', 'en' => 'Try again'],

            // Step navigation labels
            'step_category' => ['el' => 'Κατηγορία', 'en' => 'Category'],
            'step_service' => ['el' => 'Υπηρεσία', 'en' => 'Service'],
            'step_date' => ['el' => 'Ημερομηνία', 'en' => 'Date'],
            'step_time' => ['el' => 'Ώρα', 'en' => 'Time'],
            'step_details' => ['el' => 'Στοιχεία', 'en' => 'Details'],
            'step_verify' => ['el' => 'Επιβεβαίωση', 'en' => 'Verify'],
            'step_confirm' => ['el' => 'Ολοκλήρωση', 'en' => 'Confirm'],

            // Enhanced service selection
            'select_service_you_want' => ['el' => 'Επιλέξτε την υπηρεσία που θέλετε να κλείσετε', 'en' => 'Select the service you want to book'],
            'select_service_type' => ['el' => 'Επιλέξτε τον τύπο υπηρεσίας που αναζητάτε', 'en' => 'Select the type of service you are looking for'],

            // Enhanced date/time selection
            'select_date' => ['el' => 'Επιλέξτε Ημερομηνία', 'en' => 'Select Date'],
            'select_preferred_date' => ['el' => 'Επιλέξτε την προτιμώμενη ημερομηνία', 'en' => 'Select your preferred date'],
            'select_time' => ['el' => 'Επιλέξτε Ώρα', 'en' => 'Select Time'],
            'choose_preferred_time_slot' => ['el' => 'Επιλέξτε την προτιμώμενη ώρα', 'en' => 'Choose your preferred time slot'],
            'insufficient_time' => ['el' => 'Ανεπαρκής χρόνος', 'en' => 'Insufficient time'],

            // Enhanced contact details
            'provide_contact_details' => ['el' => 'Παρακαλώ παρέχετε τα στοιχεία επικοινωνίας σας', 'en' => 'Please provide your contact details'],

            // Email verification
            'confirm_your_email' => ['el' => 'Επιβεβαιώστε το Email σας', 'en' => 'Confirm your Email'],
            'verification_code_sent' => ['el' => 'Στείλαμε κωδικό επιβεβαίωσης στο email', 'en' => 'We sent a verification code to your email'],
            'didnt_receive_code' => ['el' => 'Δεν λάβατε τον κωδικό;', 'en' => 'Didn\'t receive the code?'],
            'resend_code' => ['el' => 'Επαναποστολή κωδικού', 'en' => 'Resend code'],
            'verify' => ['el' => 'Επιβεβαίωση', 'en' => 'Verify'],

            // Success and additional actions
            'appointment_booked_successfully' => ['el' => 'Το ραντεβού σας κλείστηκε επιτυχώς. Θα λάβετε email επιβεβαίωσης σύντομα.', 'en' => 'Your appointment has been booked successfully. You will receive a confirmation email shortly.'],
            'book_another_appointment' => ['el' => 'Κλείστε άλλο ραντεβού', 'en' => 'Book Another Appointment'],

            // Time slot status labels
            'closed' => ['el' => 'Κλεισμένο', 'en' => 'Closed'],
            'duration_conflict' => ['el' => 'Σύγκρουση διάρκειας', 'en' => 'Duration conflict'],
            'staff_unavailable' => ['el' => 'Προσωπικό μη διαθέσιμο', 'en' => 'Staff unavailable'],

            // Booking confirmation details
            'booking_code' => ['el' => 'Κωδικός Κράτησης', 'en' => 'Booking Code'],
            'service' => ['el' => 'Υπηρεσία', 'en' => 'Service'],
            'category' => ['el' => 'Κατηγορία', 'en' => 'Category'],
            'date' => ['el' => 'Ημερομηνία', 'en' => 'Date'],
            'time' => ['el' => 'Ώρα', 'en' => 'Time'],
            'duration' => ['el' => 'Διάρκεια', 'en' => 'Duration'],
            'customer' => ['el' => 'Πελάτης', 'en' => 'Customer'],
            'email' => ['el' => 'Email', 'en' => 'Email'],
            'phone' => ['el' => 'Τηλέφωνο', 'en' => 'Phone'],
            'total_price' => ['el' => 'Συνολική Τιμή', 'en' => 'Total Price'],

            // Time units and days
            'minutes' => ['el' => 'λεπτά', 'en' => 'minutes'],
            'thursday' => ['el' => 'Πέμπτη', 'en' => 'Thursday'],
        ];

        $count = 0;
        foreach ($clientTranslations as $key => $values) {
            try {
                if (class_exists('Translation')) {
                    $success = Translation::save($key, $values['el'], $values['en'], 'client');
                    if ($success) {
                        $count++;
                    }
                } else {
                    // Fallback to direct database insertion
                    $id = 'TR' . strtoupper(substr(uniqid(), -8));
                    $db->query("
                        INSERT OR IGNORE INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                        VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
                    ", [
                        ':id' => $id,
                        ':key' => $key,
                        ':value_el' => $values['el'],
                        ':value_en' => $values['en'],
                        ':category' => 'client',
                        ':created_at' => date('Y-m-d H:i:s'),
                        ':updated_at' => date('Y-m-d H:i:s')
                    ]);
                    $count++;
                }
            } catch (Exception $e) {
                error_log("Failed to create client translation '{$key}': " . $e->getMessage());
            }
        }

        return $count;
    }

    /**
     * Create translation entries for a category
     */
    private function createCategoryTranslations(Database $db, string $categoryId, string $nameEl, string $nameEn, string $descriptionEl, string $descriptionEn): void
    {
        $timestamp = date('Y-m-d H:i:s');

        // Create category name translation
        $nameKey = "category_name_{$categoryId}";
        $this->createTranslationPair($db, $nameKey, $nameEn, $nameEl, 'categories', $timestamp);

        // Always create category description translation, even if empty
        $descKey = "category_description_{$categoryId}";
        $this->createTranslationPair($db, $descKey, $descriptionEn, $descriptionEl, 'categories', $timestamp);
    }

    /**
     * Create translation entries for a service
     */
    private function createServiceTranslations(Database $db, string $serviceId, string $nameEl, string $nameEn, string $descriptionEl, string $descriptionEn): void
    {
        $timestamp = date('Y-m-d H:i:s');

        // Create service name translation
        $nameKey = "service_name_{$serviceId}";
        $this->createTranslationPair($db, $nameKey, $nameEn, $nameEl, 'services', $timestamp);

        // Always create service description translation, even if empty
        $descKey = "service_description_{$serviceId}";
        $this->createTranslationPair($db, $descKey, $descriptionEn, $descriptionEl, 'services', $timestamp);
    }

    /**
     * Create a translation pair (English and Greek) using the proper Translation system
     */
    private function createTranslationPair(Database $db, string $key, string $enValue, string $elValue, string $category, string $timestamp): void
    {
        try {
            // Check if Translation class is available
            if (!class_exists('Translation')) {
                // Fallback to direct database insertion if Translation class not available
                $id = 'TR' . strtoupper(substr(uniqid(), -8));
                $db->query("
                    INSERT OR IGNORE INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                    VALUES (:id, :key, :value_el, :value_en, :category, :created_at, :updated_at)
                ", [
                    ':id' => $id,
                    ':key' => $key,
                    ':value_el' => $elValue,
                    ':value_en' => $enValue,
                    ':category' => $category,
                    ':created_at' => $timestamp,
                    ':updated_at' => $timestamp
                ]);
                return;
            }

            // Use Translation::save which handles duplicates and constraints properly
            $success = Translation::save($key, $elValue, $enValue, $category);

            if (!$success) {
                error_log("Failed to save translation for key '{$key}' in category '{$category}'");
            }

        } catch (Exception $e) {
            // Log error but don't fail dummy data generation
            error_log("Failed to create translation for key '{$key}': " . $e->getMessage());
        }
    }
}
