<?php
/**
 * Add Remaining Client Translations
 * Adds the final missing client translations identified from the interface
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

function addRemainingClientTranslations(): void {
    echo "➕ Adding Remaining Client Translations\n";
    echo str_repeat("=", 60) . "\n";

    try {
        Config::init();
        TenantManager::init();
        Translation::init();

        // Additional missing translations from the latest interface review
        $remainingTranslations = [
            // Time slot status labels
            'closed' => ['el' => 'Κλεισμένο', 'en' => 'Closed'],
            'duration_conflict' => ['el' => 'Σύγκρουση διάρκειας', 'en' => 'Duration conflict'],
            'staff_unavailable' => ['el' => 'Προσωπικό μη διαθέσιμο', 'en' => 'Staff unavailable'],
            'insufficient_time' => ['el' => 'Ανεπαρκής χρόνος', 'en' => 'Insufficient time'],
            
            // Booking confirmation details
            'booking_code' => ['el' => 'Κωδικός Κράτησης', 'en' => 'Booking Code'],
            'service' => ['el' => 'Υπηρεσία', 'en' => 'Service'],
            'category' => ['el' => 'Κατηγορία', 'en' => 'Category'],
            'date' => ['el' => 'Ημερομηνία', 'en' => 'Date'],
            'time' => ['el' => 'Ώρα', 'en' => 'Time'],
            'duration' => ['el' => 'Διάρκεια', 'en' => 'Duration'],
            'customer' => ['el' => 'Πελάτης', 'en' => 'Customer'],
            'email' => ['el' => 'Email', 'en' => 'Email'],
            'phone' => ['el' => 'Τηλέφωνο', 'en' => 'Phone'],
            'total_price' => ['el' => 'Συνολική Τιμή', 'en' => 'Total Price'],
            
            // Time units
            'minutes' => ['el' => 'λεπτά', 'en' => 'minutes'],
            'hours' => ['el' => 'ώρες', 'en' => 'hours'],
            'hour' => ['el' => 'ώρα', 'en' => 'hour'],
            'minute' => ['el' => 'λεπτό', 'en' => 'minute'],
            
            // Days of week (for date display)
            'monday' => ['el' => 'Δευτέρα', 'en' => 'Monday'],
            'tuesday' => ['el' => 'Τρίτη', 'en' => 'Tuesday'],
            'wednesday' => ['el' => 'Τετάρτη', 'en' => 'Wednesday'],
            'thursday' => ['el' => 'Πέμπτη', 'en' => 'Thursday'],
            'friday' => ['el' => 'Παρασκευή', 'en' => 'Friday'],
            'saturday' => ['el' => 'Σάββατο', 'en' => 'Saturday'],
            'sunday' => ['el' => 'Κυριακή', 'en' => 'Sunday'],
            
            // Months (for date display)
            'january' => ['el' => 'Ιανουάριος', 'en' => 'January'],
            'february' => ['el' => 'Φεβρουάριος', 'en' => 'February'],
            'march' => ['el' => 'Μάρτιος', 'en' => 'March'],
            'april' => ['el' => 'Απρίλιος', 'en' => 'April'],
            'may' => ['el' => 'Μάιος', 'en' => 'May'],
            'june' => ['el' => 'Ιούνιος', 'en' => 'June'],
            'july' => ['el' => 'Ιούλιος', 'en' => 'July'],
            'august' => ['el' => 'Αύγουστος', 'en' => 'August'],
            'september' => ['el' => 'Σεπτέμβριος', 'en' => 'September'],
            'october' => ['el' => 'Οκτώβριος', 'en' => 'October'],
            'november' => ['el' => 'Νοέμβριος', 'en' => 'November'],
            'december' => ['el' => 'Δεκέμβριος', 'en' => 'December'],
            
            // Additional interface elements
            'select_category' => ['el' => 'Επιλέξτε Κατηγορία', 'en' => 'Select Category'],
            'select_service_type_description' => ['el' => 'Επιλέξτε τον τύπο υπηρεσίας που αναζητάτε', 'en' => 'Select the type of service you are looking for'],
            'select_service_description' => ['el' => 'Επιλέξτε την υπηρεσία που θέλετε να κλείσετε', 'en' => 'Select the service you want to book'],
            'select_preferred_date_description' => ['el' => 'Επιλέξτε την προτιμώμενη ημερομηνία', 'en' => 'Select your preferred date'],
            'provide_contact_details_description' => ['el' => 'Παρακαλώ παρέχετε τα στοιχεία επικοινωνίας σας', 'en' => 'Please provide your contact details'],
            'confirm_email_description' => ['el' => 'Επιβεβαιώστε το Email σας', 'en' => 'Confirm your Email'],
            'verification_code_sent_description' => ['el' => 'Στείλαμε κωδικό επιβεβαίωσης στο email', 'en' => 'We sent a verification code to your email'],
            
            // Currency and pricing
            'euro' => ['el' => '€', 'en' => '€'],
            'free' => ['el' => 'Δωρεάν', 'en' => 'Free'],
            'price' => ['el' => 'Τιμή', 'en' => 'Price'],
            'cost' => ['el' => 'Κόστος', 'en' => 'Cost'],
            
            // Status messages
            'available' => ['el' => 'Διαθέσιμο', 'en' => 'Available'],
            'unavailable' => ['el' => 'Μη διαθέσιμο', 'en' => 'Unavailable'],
            'booked' => ['el' => 'Κλεισμένο', 'en' => 'Booked'],
            'pending' => ['el' => 'Εκκρεμεί', 'en' => 'Pending'],
            'confirmed' => ['el' => 'Επιβεβαιωμένο', 'en' => 'Confirmed'],
            
            // Form validation messages
            'required_field' => ['el' => 'Υποχρεωτικό πεδίο', 'en' => 'Required field'],
            'invalid_email' => ['el' => 'Μη έγκυρο email', 'en' => 'Invalid email'],
            'invalid_phone' => ['el' => 'Μη έγκυρος αριθμός τηλεφώνου', 'en' => 'Invalid phone number'],
            'field_too_short' => ['el' => 'Το πεδίο είναι πολύ σύντομο', 'en' => 'Field is too short'],
            'field_too_long' => ['el' => 'Το πεδίο είναι πολύ μεγάλο', 'en' => 'Field is too long'],
        ];

        $added = 0;
        $updated = 0;
        $skipped = 0;

        echo "Processing " . count($remainingTranslations) . " remaining translations...\n\n";

        foreach ($remainingTranslations as $key => $values) {
            try {
                // Check if translation already exists
                $db = TenantManager::getDatabase();
                $existing = $db->fetchRow("
                    SELECT * FROM translations 
                    WHERE key = :key AND category = 'client'
                ", [':key' => $key]);

                if ($existing) {
                    // Update if values are different
                    if ($existing['value_el'] !== $values['el'] || $existing['value_en'] !== $values['en']) {
                        $success = Translation::save($key, $values['el'], $values['en'], 'client');
                        if ($success) {
                            echo "🔄 Updated: {$key}\n";
                            echo "   EL: {$values['el']}\n";
                            echo "   EN: {$values['en']}\n";
                            $updated++;
                        }
                    } else {
                        echo "⏭️  Skipped: {$key} (already exists with same values)\n";
                        $skipped++;
                    }
                } else {
                    // Add new translation
                    $success = Translation::save($key, $values['el'], $values['en'], 'client');
                    if ($success) {
                        echo "➕ Added: {$key}\n";
                        echo "   EL: {$values['el']}\n";
                        echo "   EN: {$values['en']}\n";
                        $added++;
                    } else {
                        echo "❌ Failed to add: {$key}\n";
                    }
                }

            } catch (Exception $e) {
                echo "❌ Error processing {$key}: " . $e->getMessage() . "\n";
            }
        }

        echo "\n📊 Results Summary\n";
        echo str_repeat("=", 60) . "\n";
        echo "Added: {$added}\n";
        echo "Updated: {$updated}\n";
        echo "Skipped: {$skipped}\n";
        echo "Total processed: " . ($added + $updated + $skipped) . "\n";

        // Test some key translations
        echo "\n🧪 Testing key translations...\n";
        $testKeys = ['booking_code', 'duration_conflict', 'thursday', 'total_price'];
        
        foreach (['el', 'en'] as $lang) {
            Translation::setLanguage($lang);
            echo "Language: {$lang}\n";
            
            foreach ($testKeys as $testKey) {
                $value = t($testKey, 'NOT_FOUND', 'client');
                if ($value !== 'NOT_FOUND') {
                    echo "  ✅ {$testKey}: {$value}\n";
                } else {
                    echo "  ❌ {$testKey}: NOT FOUND\n";
                }
            }
        }

        // Get final count
        $totalClientTranslations = $db->fetchColumn("
            SELECT COUNT(*) FROM translations WHERE category = 'client'
        ");

        echo "\n✅ Remaining client translations have been added!\n";
        echo "Total client translations now: {$totalClientTranslations}\n";
        echo "\nThese translations cover:\n";
        echo "- Time slot status labels (closed, conflicts, etc.)\n";
        echo "- Booking confirmation details\n";
        echo "- Date and time formatting\n";
        echo "- Form validation messages\n";
        echo "- Status indicators\n";
        echo "- Currency and pricing\n";

    } catch (Exception $e) {
        echo "❌ Failed to add remaining translations: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Run the script
addRemainingClientTranslations();
?>
