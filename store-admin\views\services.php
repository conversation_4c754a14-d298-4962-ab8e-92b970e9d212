<?php

/**
 * Services View
 * Manage services
 */

// Get search and pagination parameters
$searchParams = AdminHelpers::getSearchParams();
$search = $searchParams['search'];
$pageNum = $searchParams['page_num'];
$perPage = $searchParams['per_page'];

// Build search query - search by service name, description, category name, and price
$searchWhere = AdminHelpers::buildSearchWhere($search, ['s.name', 's.description', 'c.name', 's.price']);

// Get total count for pagination first
$countSql = "SELECT COUNT(*) as total
             FROM services s
             LEFT JOIN categories c ON s.category_id = c.id
             {$searchWhere['where']}";
$totalCount = $db->fetchRow($countSql, $searchWhere['params'])['total'];

// Create pagination
$pagination = new Pagination($totalCount, $perPage, $pageNum);

// Get services with categories using proper SQL pagination
$sql = "SELECT s.*, c.name as category_name, c.color as category_color
        FROM services s
        LEFT JOIN categories c ON s.category_id = c.id
        {$searchWhere['where']}
        ORDER BY s.name ASC
        LIMIT {$perPage} OFFSET {$pagination->getOffset()}";
$paginatedServices = $db->fetchAll($sql, $searchWhere['params']);
?>

<!-- Main Toolbar -->
<div class="main-toolbar">
    <div class="toolbar-left">
        <a href="/store-admin/?page=add-service" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Service
        </a>
        <button class="btn btn-secondary" onclick="manageCategories()">
            <i class="fas fa-tags"></i> Categories
        </button>
        <div class="view-toggle">
            <button class="active" data-view="cards">
                <i class="fas fa-th-large"></i> Cards
            </button>
            <button data-view="table">
                <i class="fas fa-list"></i> Table
            </button>
        </div>
    </div>
    <div class="toolbar-right">
        <button class="btn btn-secondary" onclick="toggleFilters()" id="filter-toggle-btn">
            <i class="fas fa-filter"></i> Filters
        </button>
        <div class="search-bar">
            <input type="text" placeholder="Search services, categories, prices..." value="<?php echo htmlspecialchars($search); ?>" id="service-search">
            <button type="button" class="search-btn" onclick="performSearch(document.getElementById('service-search').value)">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
</div>

<!-- Filters Section -->
<div class="filters-section" id="filters-section" style="display: none;">
    <div class="filters-header">
        <h3><i class="fas fa-filter"></i> Filters</h3>
        <button class="btn btn-outline btn-sm" onclick="clearFilters()">
            <i class="fas fa-times"></i> Clear All
        </button>
    </div>
    <div class="filters-content">
        <div class="filter-group">
            <label class="filter-label">Category:</label>
            <select class="form-control" id="category-filter">
                <option value="">All Categories</option>
                <?php
                $categories = $db->fetchAll("SELECT DISTINCT c.id, c.name FROM categories c JOIN services s ON c.id = s.category_id ORDER BY c.name");
                foreach ($categories as $category): ?>
                    <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Price Range:</label>
            <select class="form-control" id="price-filter">
                <option value="">All Prices</option>
                <option value="0-25">€0 - €25</option>
                <option value="25-50">€25 - €50</option>
                <option value="50-100">€50 - €100</option>
                <option value="100+">€100+</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Duration:</label>
            <select class="form-control" id="duration-filter">
                <option value="">All Durations</option>
                <option value="0-30">0-30 min</option>
                <option value="30-60">30-60 min</option>
                <option value="60-120">1-2 hours</option>
                <option value="120+">2+ hours</option>
            </select>
        </div>
        <div class="filter-group">
            <label class="filter-label">Status:</label>
            <select class="form-control" id="status-filter">
                <option value="">All Services</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
    </div>
</div>



<!-- Enhanced Services Grid -->
<div class="entity-grid" id="services-grid">
    <?php if (empty($paginatedServices)): ?>
        <div class="empty-state">
            <i class="fas fa-cut fa-2x text-muted"></i>
            <h3>No services found</h3>
            <p>Start by adding your first service or adjust your search criteria.</p>
            <button class="btn btn-primary btn-lg" onclick="addService()">
                <i class="fas fa-plus"></i> Add First Service
            </button>
        </div>
    <?php else: ?>
        <?php
        // Include the entity card component
        require_once __DIR__ . '/components/entity-card.php';

        foreach ($paginatedServices as $service):
            // Prepare service data for the card component
            $totalDuration = ($service['preparation_time'] ?? 0) + $service['duration'] + ($service['cleanup_time'] ?? 0);

            // Prepare service data for card
            $serviceData = [
                'id' => $service['id'],
                'name' => $service['name'],
                'description' => $service['description'] ?? '',
                'category_name' => $service['category_name'] ?? 'Uncategorized',
                'category_color' => $service['category_color'] ?? '',
                'price' => $service['price'],
                'duration' => $service['duration'],
                'total_duration' => $totalDuration,
                'preparation_time' => $service['preparation_time'] ?? 0,
                'cleanup_time' => $service['cleanup_time'] ?? 0,
                'active' => $service['is_active'] ?? true,
                'created_at' => $service['created_at'] ?? null
            ];

            // Render the service card
            echo renderEntityCard($serviceData, 'service', [
                'show_checkbox' => false,
                'show_actions' => false
            ]);
        endforeach; ?>

        <!-- Add Service Card -->
        <div class="entity-card add-card" onclick="window.location.href='/store-admin/?page=add-service'">
            <div class="add-card-content">
                <i class="fas fa-plus fa-2x"></i>
                <h3>Add New Service</h3>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if ($pagination->getTotalPages() > 1): ?>
    <div class="pagination-container">
        <?php echo $pagination->render(); ?>
    </div>
<?php endif; ?>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .page-actions {
        display: flex;
        gap: 10px;
    }

    .card-title-section {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .category-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.85rem;
        font-weight: 500;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }

    .pagination-container {
        margin-top: 30px;
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 15px;
        }

        .page-actions {
            flex-wrap: wrap;
            justify-content: center;
        }
    }
</style>