<?php
/**
 * Email Logs View
 * Display email sending history with filtering and pagination
 */

$pageTitle = 'Email Logs';
$currentPage = 'email_logs';

// Get filter parameters
$customerId = $_GET['customer_id'] ?? '';
$dateFrom = $_GET['date_from'] ?? '';
$dateTo = $_GET['date_to'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));

// Get customers for filter dropdown
$customers = $db->fetchAll("SELECT id, name, email FROM customers ORDER BY name");
?>

<div class="admin-header">
    <div class="header-content">
        <div class="header-left">
            <h1><i class="fas fa-envelope"></i> Email Logs</h1>
            <p>View email sending history and delivery status</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-secondary" onclick="refreshEmailLogs()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button class="btn btn-primary" onclick="exportEmailLogs()">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="filters-section">
    <form method="GET" class="filters-form">
        <input type="hidden" name="page" value="email_logs">
        
        <div class="filter-group">
            <label for="customer_id">Customer:</label>
            <select name="customer_id" id="customer_id" class="form-control">
                <option value="">All Customers</option>
                <?php foreach ($customers as $customer): ?>
                    <option value="<?= htmlspecialchars($customer['id']) ?>" 
                            <?= $customerId === $customer['id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($customer['name']) ?> (<?= htmlspecialchars($customer['email']) ?>)
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="date_from">From Date:</label>
            <input type="date" name="date_from" id="date_from" class="form-control" 
                   value="<?= htmlspecialchars($dateFrom) ?>">
        </div>
        
        <div class="filter-group">
            <label for="date_to">To Date:</label>
            <input type="date" name="date_to" id="date_to" class="form-control" 
                   value="<?= htmlspecialchars($dateTo) ?>">
        </div>
        
        <div class="filter-group">
            <label for="search">Search:</label>
            <input type="text" name="search" id="search" class="form-control" 
                   placeholder="Subject, message, customer..." 
                   value="<?= htmlspecialchars($search) ?>">
        </div>
        
        <div class="filter-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Filter
            </button>
            <a href="?page=email_logs" class="btn btn-secondary">
                <i class="fas fa-times"></i> Clear
            </a>
        </div>
    </form>
</div>

<!-- Email Logs Table -->
<div class="table-container">
    <div id="email-logs-container">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i> Loading email logs...
        </div>
    </div>
</div>

<!-- Email Preview Modal -->
<div id="email-preview-modal" class="modal" style="display: none;">
    <div class="modal-content large">
        <div class="modal-header">
            <h3>Email Preview</h3>
            <button class="modal-close" onclick="closeEmailPreview()">&times;</button>
        </div>
        <div class="modal-body">
            <div id="email-preview-content"></div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeEmailPreview()">Close</button>
        </div>
    </div>
</div>

<style>
.filters-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filters-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 500;
    margin-bottom: 5px;
    color: #333;
}

.filter-actions {
    display: flex;
    gap: 10px;
}

.email-log-row {
    cursor: pointer;
    transition: background-color 0.2s;
}

.email-log-row:hover {
    background-color: #f8f9fa;
}

.email-preview {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 15px;
    background: #f8f9fa;
}

.email-meta {
    background: white;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.email-meta h4 {
    margin: 0 0 10px 0;
    color: #007bff;
}

.email-meta p {
    margin: 5px 0;
    color: #666;
}

.loading-spinner {
    text-align: center;
    padding: 40px;
    color: #666;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-sent {
    background: #d4edda;
    color: #155724;
}

.status-failed {
    background: #f8d7da;
    color: #721c24;
}
</style>

<script>
let currentEmailLogsPage = 1;

document.addEventListener('DOMContentLoaded', function() {
    loadEmailLogs();
});

function loadEmailLogs(page = 1) {
    currentEmailLogsPage = page;
    
    const formData = new FormData();
    formData.append('action', 'get_email_logs');
    formData.append('page', page);
    formData.append('limit', '20');
    
    // Add filters
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('customer_id')) formData.append('customer_id', urlParams.get('customer_id'));
    if (urlParams.get('date_from')) formData.append('date_from', urlParams.get('date_from'));
    if (urlParams.get('date_to')) formData.append('date_to', urlParams.get('date_to'));
    if (urlParams.get('search')) formData.append('search', urlParams.get('search'));
    
    makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
        if (data.success) {
            renderEmailLogs(data.logs, data.pagination);
        } else {
            document.getElementById('email-logs-container').innerHTML = 
                '<div class="error-message">Error loading email logs: ' + (data.error || 'Unknown error') + '</div>';
        }
    });
}

function renderEmailLogs(logs, pagination) {
    let html = '<table class="data-table">';
    html += '<thead><tr>';
    html += '<th>Date/Time</th>';
    html += '<th>Customer</th>';
    html += '<th>Subject</th>';
    html += '<th>Preview</th>';
    html += '<th>Actions</th>';
    html += '</tr></thead><tbody>';
    
    if (logs.length === 0) {
        html += '<tr><td colspan="5" class="text-center">No email logs found</td></tr>';
    } else {
        logs.forEach(log => {
            const sentDate = new Date(log.sent_at).toLocaleString();
            const preview = log.message.length > 100 ? log.message.substring(0, 100) + '...' : log.message;
            
            html += `<tr class="email-log-row" onclick="previewEmail(${log.id})">`;
            html += `<td>${sentDate}</td>`;
            html += `<td>${log.customer_name || 'Unknown'}<br><small>${log.customer_email || ''}</small></td>`;
            html += `<td>${log.subject}</td>`;
            html += `<td>${preview.replace(/<[^>]*>/g, '')}</td>`;
            html += `<td><button class="btn btn-sm btn-primary" onclick="event.stopPropagation(); previewEmail(${log.id})">View</button></td>`;
            html += '</tr>';
        });
    }
    
    html += '</tbody></table>';
    
    // Add pagination
    if (pagination.pages > 1) {
        html += '<div class="pagination">';
        for (let i = 1; i <= pagination.pages; i++) {
            const activeClass = i === pagination.page ? 'active' : '';
            html += `<button class="pagination-btn ${activeClass}" onclick="loadEmailLogs(${i})">${i}</button>`;
        }
        html += '</div>';
    }
    
    document.getElementById('email-logs-container').innerHTML = html;
}

function previewEmail(logId) {
    // Find the log data
    const formData = new FormData();
    formData.append('action', 'get_email_logs');
    formData.append('limit', '1000'); // Get all to find the specific one
    
    makeAjaxCall('/store-admin/controllers/ajax.php', formData, function(data) {
        if (data.success) {
            const log = data.logs.find(l => l.id == logId);
            if (log) {
                showEmailPreview(log);
            }
        }
    });
}

function showEmailPreview(log) {
    const sentDate = new Date(log.sent_at).toLocaleString();
    
    const content = `
        <div class="email-meta">
            <h4>${log.subject}</h4>
            <p><strong>To:</strong> ${log.customer_name} &lt;${log.customer_email}&gt;</p>
            <p><strong>Sent:</strong> ${sentDate}</p>
        </div>
        <div class="email-preview">
            ${log.message}
        </div>
    `;
    
    document.getElementById('email-preview-content').innerHTML = content;
    document.getElementById('email-preview-modal').style.display = 'block';
}

function closeEmailPreview() {
    document.getElementById('email-preview-modal').style.display = 'none';
}

function refreshEmailLogs() {
    loadEmailLogs(currentEmailLogsPage);
}

function exportEmailLogs() {
    const urlParams = new URLSearchParams(window.location.search);
    const params = new URLSearchParams();
    params.append('action', 'bulk_export');
    params.append('table', 'email_logs');
    params.append('format', 'csv');
    
    // Add current filters
    if (urlParams.get('customer_id')) params.append('customer_id', urlParams.get('customer_id'));
    if (urlParams.get('date_from')) params.append('date_from', urlParams.get('date_from'));
    if (urlParams.get('date_to')) params.append('date_to', urlParams.get('date_to'));
    if (urlParams.get('search')) params.append('search', urlParams.get('search'));
    
    window.open('/store-admin/controllers/ajax.php?' + params.toString(), '_blank');
}

// Make functions globally available
window.loadEmailLogs = loadEmailLogs;
window.previewEmail = previewEmail;
window.showEmailPreview = showEmailPreview;
window.closeEmailPreview = closeEmailPreview;
window.refreshEmailLogs = refreshEmailLogs;
window.exportEmailLogs = exportEmailLogs;
</script>
