<?php
session_start();
require_once __DIR__ . '/../shared/environment.php';

// Load environment variables
Environment::load();

// Check if registration data exists
if (!isset($_SESSION['registration_data'])) {
    header('Location: register_shop.php');
    exit;
}

$registration_data = $_SESSION['registration_data'];

// Payment processing configuration - load from environment variables
$payment_config = [
    'paypal' => [
        'client_id' => Environment::get('PAYPAL_CLIENT_ID'),
        'client_secret' => Environment::get('PAYPAL_CLIENT_SECRET'),
        'sandbox' => Environment::get('DEVELOPMENT_MODE', 'true') === 'true',
        'success_url' => Environment::get('BASE_URL', 'http://localhost/gk_radevou') . '/onboarding/payment_success.php',
        'cancel_url' => Environment::get('BASE_URL', 'http://localhost/gk_radevou') . '/onboarding/register_shop.php'
    ],
    'stripe' => [
        'publishable_key' => Environment::get('STRIPE_PUBLISHABLE_KEY'),
        'secret_key' => Environment::get('STRIPE_SECRET_KEY'),
        'success_url' => Environment::get('BASE_URL', 'http://localhost/gk_radevou') . '/onboarding/payment_success.php',
        'cancel_url' => Environment::get('BASE_URL', 'http://localhost/gk_radevou') . '/onboarding/register_shop.php'
    ]
];

// Validate required payment configuration
if (empty($payment_config['stripe']['publishable_key']) || empty($payment_config['stripe']['secret_key'])) {
    error_log('Warning: Stripe keys not configured in environment variables');
}

if (empty($payment_config['paypal']['client_id']) || empty($payment_config['paypal']['client_secret'])) {
    error_log('Warning: PayPal credentials not configured in environment variables');
}

// Plan pricing
$plans = [
    'starter' => 29.00,
    'professional' => 79.00,
    'enterprise' => 199.00
];

$selected_plan = $registration_data['plan'];
$plan_price = $plans[$selected_plan] ?? 79.00;

// Handle payment processing
if ($_POST && isset($_POST['process_payment'])) {
    $payment_method = $registration_data['payment_method'];
    
    if ($payment_method === 'paypal') {
        processPayPalPayment($registration_data, $plan_price);
    } elseif ($payment_method === 'stripe') {
        processStripePayment($registration_data, $plan_price);
    }
}

function processPayPalPayment($data, $amount) {
    // PayPal API integration would go here
    // This is a simplified demo version
    
    // In production, you would:
    // 1. Create PayPal order
    // 2. Redirect to PayPal for payment
    // 3. Handle PayPal webhook/callback
    
    // For demo purposes, simulate successful payment
    simulatePaymentSuccess($data, 'paypal', $amount);
}

function processStripePayment($data, $amount) {
    // Stripe API integration would go here
    // This is a simplified demo version
    
    // In production, you would:
    // 1. Create Stripe session
    // 2. Redirect to Stripe checkout
    // 3. Handle Stripe webhook
    
    // For demo purposes, simulate successful payment
    simulatePaymentSuccess($data, 'stripe', $amount);
}

function simulatePaymentSuccess($data, $method, $amount) {
    // Create tenant and process registration
    $tenant_id = createTenant($data);
    $credentials = generateLoginCredentials($data);
    
    // Store payment record
    $payment_record = [
        'tenant_id' => $tenant_id,
        'amount' => $amount,
        'method' => $method,
        'status' => 'completed',
        'transaction_id' => 'demo_' . time(),
        'date' => date('Y-m-d H:i:s')
    ];
    
    // Store in session for success page
    $_SESSION['payment_success'] = [
        'tenant_id' => $tenant_id,
        'credentials' => $credentials,
        'payment' => $payment_record
    ];
    
    // Send welcome email
    sendWelcomeEmail($data, $credentials);
    
    // Redirect to success page
    header('Location: payment_success.php');
    exit;
}

function createTenant($data) {
    // Generate unique tenant ID
    $tenant_id = 'tenant_' . uniqid();
    
    // In production, this would:
    // 1. Create database entries
    // 2. Set up tenant-specific tables
    // 3. Configure tenant settings
    // 4. Create admin user
    
    // For demo, we'll simulate this
    $tenant_data = [
        'id' => $tenant_id,
        'business_name' => $data['business_name'],
        'business_type' => $data['business_type'],
        'owner_name' => $data['owner_name'],
        'email' => $data['email'],
        'phone' => $data['phone'],
        'address' => $data['address'],
        'city' => $data['city'],
        'state' => $data['state'],
        'zip' => $data['zip'],
        'country' => $data['country'],
        'plan' => $data['plan'],
        'status' => 'active',
        'created_at' => date('Y-m-d H:i:s'),
        'trial_ends_at' => date('Y-m-d H:i:s', strtotime('+14 days'))
    ];
    
    // Store tenant data (in production, save to database)
    file_put_contents('tenants.json', json_encode($tenant_data) . "\n", FILE_APPEND);
    
    return $tenant_id;
}

function generateLoginCredentials($data) {
    // Generate secure login credentials
    $username = strtolower(str_replace(' ', '_', $data['business_name']));
    $password = generateSecurePassword();
    
    return [
        'username' => $username,
        'password' => $password,
        'email' => $data['email'],
        'login_url' => 'https://app.gkradevou.com/login',
        'setup_url' => 'https://app.gkradevou.com/setup'
    ];
}

function generateSecurePassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    return substr(str_shuffle($chars), 0, $length);
}

function sendWelcomeEmail($data, $credentials) {
    // This would integrate with your email service
    // For demo purposes, we'll use the email_login_credentials.php file
    include 'email_login_credentials.php';
    sendLoginCredentialsEmail($data, $credentials);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Processing - GK Radevou</title>
    <link rel="stylesheet" href="assets/landing.css">
    <style>
        .payment-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-top: 100px;
            margin-bottom: 2rem;
        }
        
        .payment-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .payment-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .payment-summary {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .summary-row:last-child {
            margin-bottom: 0;
            font-weight: bold;
            font-size: 1.1rem;
            border-top: 1px solid #ddd;
            padding-top: 0.5rem;
        }
        
        .payment-method-display {
            background: #e3f2fd;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .payment-method-display h3 {
            color: #007bff;
            margin-bottom: 0.5rem;
        }
        
        .payment-actions {
            text-align: center;
        }
        
        .payment-actions .btn-primary {
            font-size: 1.1rem;
            padding: 1rem 2rem;
            margin-bottom: 1rem;
        }
        
        .security-info {
            background: #e8f5e8;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 2rem;
            font-size: 0.9rem;
            color: #155724;
        }
        
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.9);
            z-index: 1000;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .trial-highlight {
            background: #fff3cd;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            color: #856404;
        }
        
        .trial-highlight strong {
            color: #856404;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <h2>GK Radevou</h2>
            </div>
            <div class="nav-actions">
                <a href="register_shop.php" class="btn-secondary">Back to Registration</a>
            </div>
        </div>
    </nav>

    <div class="payment-container">
        <div class="payment-header">
            <h1>Complete Your Registration</h1>
            <p>Review your order and complete payment setup</p>
        </div>

        <div class="payment-summary">
            <h3>Order Summary</h3>
            <div class="summary-row">
                <span>Business Name:</span>
                <span><?php echo htmlspecialchars($registration_data['business_name']); ?></span>
            </div>
            <div class="summary-row">
                <span>Plan:</span>
                <span><?php echo ucfirst($selected_plan); ?> Plan</span>
            </div>
            <div class="summary-row">
                <span>Monthly Price:</span>
                <span>$<?php echo number_format($plan_price, 2); ?></span>
            </div>
            <div class="summary-row">
                <span>Setup Fee:</span>
                <span>$0.00</span>
            </div>
            <div class="summary-row">
                <span><strong>Total:</strong></span>
                <span><strong>$<?php echo number_format($plan_price, 2); ?>/month</strong></span>
            </div>
        </div>

        <div class="trial-highlight">
            <strong>🎉 14-Day Free Trial Included!</strong><br>
            You won't be charged until your trial period ends. Cancel anytime during the trial at no cost.
        </div>

        <div class="payment-method-display">
            <h3>Payment Method</h3>
            <p>
                <?php if ($registration_data['payment_method'] === 'paypal'): ?>
                    💳 <strong>PayPal</strong><br>
                    You'll be redirected to PayPal to complete your payment setup
                <?php else: ?>
                    💰 <strong>Credit Card</strong><br>
                    You'll be redirected to our secure payment processor
                <?php endif; ?>
            </p>
        </div>

        <form method="POST" id="paymentForm">
            <div class="payment-actions">
                <button type="submit" name="process_payment" class="btn-primary" onclick="showLoading()">
                    <?php if ($registration_data['payment_method'] === 'paypal'): ?>
                        Continue with PayPal
                    <?php else: ?>
                        Continue with Credit Card
                    <?php endif; ?>
                </button>
                <br>
                <a href="register_shop.php" class="btn-secondary">Modify Registration</a>
            </div>
        </form>

        <div class="security-info">
            <strong>🔒 Your Information is Secure</strong><br>
            • All payment data is encrypted with SSL<br>
            • We never store your payment details<br>
            • Your personal information is protected<br>
            • 30-day money-back guarantee
        </div>
    </div>

    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <h3 style="margin-top: 1rem;">Processing Your Payment...</h3>
        <p>Please wait while we set up your account</p>
    </div>

    <script>
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        // Simulate payment processing delay
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            showLoading();
            
            // For demo purposes, add a delay to simulate payment processing
            setTimeout(() => {
                // Form will submit naturally after delay
            }, 2000);
        });

        // Payment method specific handling
        document.addEventListener('DOMContentLoaded', function() {
            const paymentMethod = '<?php echo $registration_data['payment_method']; ?>';
            
            if (paymentMethod === 'paypal') {
                // PayPal specific initialization
                console.log('PayPal payment method selected');
            } else if (paymentMethod === 'stripe') {
                // Stripe specific initialization
                console.log('Stripe payment method selected');
            }
        });

        // Handle page unload to prevent accidental navigation
        window.addEventListener('beforeunload', function(e) {
            if (document.getElementById('loadingOverlay').style.display === 'flex') {
                e.preventDefault();
                e.returnValue = 'Payment is being processed. Are you sure you want to leave?';
            }
        });
    </script>
</body>
</html>
