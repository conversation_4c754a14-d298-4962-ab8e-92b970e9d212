<?php

/**
 * Booking API Endpoint
 * Handles booking creation with availability checking and verification
 */

// Clean output buffer to prevent JSON contamination
if (ob_get_level()) {
    ob_clean();
}

// Include required dependencies
if (!class_exists('AvailabilityChecker')) {
    require_once __DIR__ . '/../shared/availability_checker.php';
}
if (!class_exists('VerificationManager')) {
    require_once __DIR__ . '/../shared/verification.php';
}
if (!class_exists('ErrorMessages')) {
    require_once __DIR__ . '/../shared/error_messages.php';
}
if (!class_exists('SMTPMailer')) {
    require_once __DIR__ . '/../shared/smtp.php';
require_once __DIR__ . '/../shared/verification.php';
}

// Verify critical classes are available
if (!class_exists('AvailabilityChecker')) {
    error_log("Booking API - CRITICAL: AvailabilityChecker class not available");
    errorResponse('Booking system unavailable - availability checker not found');
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    errorResponse('Method not allowed', 405);
}

// Parse JSON input
$rawInput = file_get_contents('php://input');
$input = json_decode($rawInput, true);

// Log for debugging
error_log("Booking API - Raw input: " . $rawInput);
error_log("Booking API - Parsed input: " . json_encode($input));

if (!$input) {
    error_log("Booking API - Invalid JSON input: " . $rawInput);
    errorResponse('Invalid JSON input');
}

try {
    error_log("Booking API - Starting booking process");

    $serviceId = $input['service_id'] ?? null;
    $employeeId = $input['employee_id'] ?? null; // Customer's selected employee
    $date = $input['date'] ?? null;
    $time = $input['time'] ?? null;
    $customer = $input['customer'] ?? null;
    $notes = $input['notes'] ?? '';

    error_log("Booking API - Parsed parameters: serviceId=$serviceId, employeeId=$employeeId, date=$date, time=$time");

    // Extract customer details from customer object or direct fields (backward compatibility)
    $customerName = $customer['name'] ?? $input['customer_name'] ?? null;
    $customerEmail = $customer['email'] ?? $input['customer_email'] ?? null;
    $customerPhone = $customer['phone'] ?? $input['customer_phone'] ?? null;
    $customerNotes = $customer['notes'] ?? $notes;

    // Validate required fields
    if (!$serviceId || !$date || !$time || !$customerName || !$customerEmail) {
        errorResponse('Missing required fields');
    }

    // Validate email
    if (!validateEmail($customerEmail)) {
        errorResponse('Invalid email format');
    }

    // Validate phone if provided
    if ($customerPhone && !validatePhone($customerPhone)) {
        errorResponse('Invalid phone format');
    }

    // Implement file locking to prevent concurrent booking attempts
    $lockFile = __DIR__ . '/../data/booking.lock';
    $lockHandle = fopen($lockFile, 'w');

    if (!$lockHandle || !flock($lockHandle, LOCK_EX | LOCK_NB)) {
        if ($lockHandle) fclose($lockHandle);
        errorResponse('Another booking is in progress. Please wait a moment and try again.');
    }

    try {
        $db = TenantManager::getDatabase();

        // Apply database optimizations for better concurrency
        try {
            $db->exec('PRAGMA busy_timeout = 60000');
            $db->exec('PRAGMA journal_mode = WAL');
            $db->exec('PRAGMA synchronous = NORMAL');
            $db->exec('PRAGMA temp_store = MEMORY');
        } catch (Exception $e) {
            // Ignore pragma errors, they might already be set
        }

        // Initialize availability checker
        if (!class_exists('AvailabilityChecker')) {
            error_log("Booking API - AvailabilityChecker class not found");
            errorResponse('Booking system unavailable - missing availability checker');
        }

        $checker = new AvailabilityChecker($db);
    
        // Get service details first (needed for duration checks)
        $service = $db->fetchRow(
            "SELECT * FROM services WHERE id = :id",
            [':id' => $serviceId]
        );
        
        if (!$service) {
            errorResponse('Service not found');
        }

        // Initial availability check (quick validation)
        if (!$checker->isTimeSlotAvailable($serviceId, $date, $time)) {
            errorResponse('Time slot is no longer available');
        }

        // Handle employee selection
        error_log("Booking API - Employee selection: employeeId = " . ($employeeId ?? 'null'));

        if ($employeeId) {
            // Customer selected a specific employee - validate they're available
            error_log("Booking API - Validating selected employee: $employeeId");

            if (!$checker->isEmployeeAvailableAtTime($employeeId, $date, $time, $service['duration'])) {
                error_log("Booking API - Selected employee $employeeId not available");
                errorResponse('Selected staff member is no longer available for this time slot');
            }

            error_log("Booking API - Selected employee $employeeId is available");
        } else {
            // No specific employee selected - find any available employee (auto selection)
            error_log("Booking API - Auto-selecting employee");

            $employeeId = $checker->findAvailableEmployee($serviceId, $date, $time);
            if (!$employeeId) {
                error_log("Booking API - No available employees found");
                errorResponse('No available staff for this time slot');
            }

            error_log("Booking API - Auto-selected employee: $employeeId");
        }
        
        // Start database transaction
        $db->beginTransaction();

        try {
            // Final conflict check
            $conflicts = $checker->checkAllConflicts($serviceId, $employeeId, $date, $time);

            if (!empty($conflicts)) {
                // Check if it's an employee conflict that might be resolved with alternative employee
                $hasNoAvailableEmployees = false;
                $hasEmployeeConflict = false;

                foreach ($conflicts as $conflict) {
                    if ($conflict['type'] === 'no_available_employees') {
                        $hasNoAvailableEmployees = true;
                    } elseif ($conflict['type'] === 'employee_conflict') {
                        $hasEmployeeConflict = true;
                    }
                }

                // If only employee conflict, try to find alternative employee
                if ($hasEmployeeConflict && !$hasNoAvailableEmployees) {
                    $alternativeEmployees = $checker->findAlternativeEmployees($serviceId, $date, $time, $employeeId);

                    if (!empty($alternativeEmployees)) {
                        $employeeId = $alternativeEmployees[0]['id'];
                        error_log("Switched to alternative employee: {$employeeId} for booking");

                        // Re-check conflicts with new employee
                        $conflicts = $checker->checkAllConflicts($serviceId, $employeeId, $date, $time);
                    }
                }

                // If still have conflicts, fail the booking with multilingual message
                if (!empty($conflicts)) {
                    $userLanguage = ErrorMessages::detectLanguage();
                    $errorMessage = ErrorMessages::formatErrorResponse($conflicts, $userLanguage);
                    throw new Exception($errorMessage);
                }
            }

            // Check if customer exists
            $customer = $db->fetchRow(
                "SELECT * FROM customers WHERE email = :email",
                [':email' => $customerEmail]
            );

            if ($customer) {
                // Update existing customer
                $db->query(
                    "UPDATE customers SET name = :name, phone = :phone, updated_at = :updated 
                     WHERE id = :id",
                    [
                        ':name' => $customerName,
                        ':phone' => $customerPhone,
                        ':updated' => date('Y-m-d H:i:s'),
                        ':id' => $customer['id']
                    ]
                );
                $customerId = $customer['id'];
            } else {
                // Create new customer
                $customerId = 'CUST' . uniqid();
                $db->query(
                    "INSERT INTO customers (id, name, email, phone, created_at)
                     VALUES (:id, :name, :email, :phone, :created)",
                    [
                        ':id' => $customerId,
                        ':name' => $customerName,
                        ':email' => $customerEmail,
                        ':phone' => $customerPhone,
                        ':created' => date('Y-m-d H:i:s')
                    ]
                );
            }
            
            // Create reservation
            $reservationId = 'RSV' . uniqid();
            $endTime = date('H:i', strtotime($time) + ($service['duration'] * 60));

            // Get verification method setting
            $verificationMethod = $db->fetchColumn(
                "SELECT value FROM settings WHERE key = 'verification_method'",
                []
            ) ?: 'email';

            // Determine reservation status based on verification method
            $reservationStatus = ($verificationMethod === 'none') ? 'confirmed' : 'pending';

            $db->query(
                "INSERT INTO reservations (id, customer_id, service_id, employee_id, date,
                 start_time, end_time, status, notes, price, created_at)
                 VALUES (:id, :customer_id, :service_id, :employee_id, :date, :start_time,
                 :end_time, :status, :notes, :price, :created)",
                [
                    ':id' => $reservationId,
                    ':customer_id' => $customerId,
                    ':service_id' => $serviceId,
                    ':employee_id' => $employeeId,
                    ':date' => $date,
                    ':start_time' => $time,
                    ':end_time' => $endTime,
                    ':status' => $reservationStatus,
                    ':notes' => $customerNotes,
                    ':price' => $service['price'],
                    ':created' => date('Y-m-d H:i:s')
                ]
            );

            // Handle verification based on method
            $businessName = $db->fetchColumn(
                "SELECT value FROM settings WHERE key = 'business_name'",
                []
            ) ?: 'Booking System';

            $verificationResult = ['success' => true, 'message' => ''];
            $verificationRequired = false;
            $verificationExpires = null;

            if ($verificationMethod !== 'none') {
                // Create booking-specific verification code
                $verification = new VerificationManager($db);
                $verificationCode = $verification->generateCode('booking', $reservationId);

                // Send verification code via the selected method
                if ($verificationMethod === 'email') {
                    $mailer = new SMTPMailer();
                    $verificationResult = [
                        'success' => $mailer->sendVerificationCode($customerEmail, $verificationCode, $businessName),
                        'message' => 'Verification code sent to email'
                    ];
                } elseif ($verificationMethod === 'sms' && !empty($customerPhone)) {
                    // SMS verification would go here
                    $verificationResult = [
                        'success' => false,
                        'message' => 'SMS verification not implemented yet'
                    ];
                } else {
                    $verificationResult = [
                        'success' => false,
                        'message' => 'Invalid verification method or missing phone number'
                    ];
                }

                $verificationRequired = true;
                $verificationExpires = date('Y-m-d H:i:s', time() + Config::VERIFICATION_CODE_EXPIRY);
            }

            $db->commit();

            // Prepare response based on verification method
            $responseData = [
                'reservation_id' => $reservationId,
                'verification_required' => $verificationRequired,
                'verification_method' => $verificationMethod
            ];

            if ($verificationRequired) {
                $responseData['verification_expires'] = $verificationExpires;
                $responseData['message'] = $verificationResult['success']
                    ? $verificationResult['message']
                    : 'Booking created but verification failed: ' . $verificationResult['message'];
            } else {
                $responseData['message'] = 'Booking confirmed successfully!';
            }

            successResponse($responseData);
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } finally {
        // Always release the file lock
        if (isset($lockHandle) && $lockHandle) {
            flock($lockHandle, LOCK_UN);
            fclose($lockHandle);
        }
        if (isset($lockFile) && file_exists($lockFile)) {
            @unlink($lockFile);
        }
    }

} catch (Exception $e) {
    // Handle any errors
    error_log("Booking API Critical Error: " . $e->getMessage());
    error_log("Booking API Error File: " . $e->getFile() . " Line: " . $e->getLine());
    error_log("Booking API Error Stack Trace: " . $e->getTraceAsString());

    // Also log the input data for debugging
    error_log("Booking API Error - Input data: " . json_encode($input ?? []));

    errorResponse('Booking system temporarily unavailable. Please try again. Error: ' . $e->getMessage());
}
?>
