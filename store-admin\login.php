<?php
/**
 * Store Admin Login
 * Authentication interface for tenant administrators with security features
 */

session_start();
require_once __DIR__ . '/../shared/environment.php';
require_once __DIR__ . '/core/Application.php';

// Load environment variables
Environment::load();
Application::init();

// Check if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: /store-admin/');
    exit;
}

$error = '';
$loginAttempts = $_SESSION['login_attempts'] ?? 0;
$lastAttempt = $_SESSION['last_attempt'] ?? 0;

// Rate limiting: 3 attempts per 15 minutes
if ($loginAttempts >= 3 && (time() - $lastAttempt) < 900) {
    $error = 'Too many failed login attempts. Please try again later.';
}

if ($_POST && empty($error)) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $csrfToken = $_POST['csrf_token'] ?? '';

    // Verify CSRF token
    if (!Application::verifyCsrfToken($csrfToken)) {
        $error = 'Invalid request. Please try again.';
    } else {
        // Get admin credentials from settings or environment
        $adminUsername = Application::getSetting('admin_username', 'admin');
        $adminPassword = Application::getSetting('admin_password');
        
        // If no admin password set in database, check environment variables
        if (empty($adminPassword)) {
            $envPassword = Environment::get('STORE_ADMIN_PASSWORD');
            if (!empty($envPassword)) {
                $adminPassword = $envPassword;
                // Save to database for future use
                Application::setSetting('admin_password', password_hash($adminPassword, PASSWORD_DEFAULT));
            } else {
                // Generate secure password if none exists
                $adminPassword = Environment::generateSecurePassword(16);
                // Save hashed password to database
                Application::setSetting('admin_password', password_hash($adminPassword, PASSWORD_DEFAULT));
                // Log the generated password for admin
                error_log("Generated new admin password for tenant: $adminPassword");
            }
        }
        
        // For security, hash the password if it's not already hashed
        if (strlen($adminPassword) !== 60) {
            $adminPassword = password_hash($adminPassword, PASSWORD_DEFAULT);
            Application::setSetting('admin_password', $adminPassword);
        }

        if ($username === $adminUsername && password_verify($password, $adminPassword)) {
            // Reset login attempts
            unset($_SESSION['login_attempts']);
            unset($_SESSION['last_attempt']);
            
            // Set session
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_username'] = $username;
            $_SESSION['login_time'] = time();
            
            // Redirect to dashboard
            header('Location: /store-admin/');
            exit;
        } else {
            $error = 'Invalid username or password.';
            $_SESSION['login_attempts'] = $loginAttempts + 1;
            $_SESSION['last_attempt'] = time();
        }
    }
}

$csrfToken = Application::generateCsrfToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Store Admin Login</title>
    <link rel="stylesheet" href="/store-admin/assets/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1><i class="fas fa-store"></i> Store Admin</h1>
                <p>Please sign in to your account</p>
            </div>
            
            <?php if ($error): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" class="login-form">
                <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">
                
                <div class="form-group">
                    <label for="username">Username</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" name="username" required 
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary btn-block">
                    <i class="fas fa-sign-in-alt"></i> Sign In
                </button>
            </form>
            
            <div class="login-footer">
                <p>Use admin credentials from Settings</p>
                <small>If you don't have credentials, check server logs for generated password</small>
            </div>
        </div>
    </div>
    
    <script>
        function togglePassword() {
            const password = document.getElementById('password');
            const toggle = document.querySelector('.password-toggle i');
            
            if (password.type === 'password') {
                password.type = 'text';
                toggle.classList.remove('fa-eye');
                toggle.classList.add('fa-eye-slash');
            } else {
                password.type = 'password';
                toggle.classList.remove('fa-eye-slash');
                toggle.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
