<?php

/**
 * Base API Class
 * Provides common functionality for all API endpoints
 */

class ApiBase
{
    protected Database $db;
    protected bool $initialized = false;
    
    public function __construct()
    {
        $this->initializeApi();
    }
    
    /**
     * Initialize API with all required dependencies
     */
    protected function initializeApi(): void
    {
        if ($this->initialized) {
            return;
        }
        
        try {
            // Clean output buffer
            while (ob_get_level()) {
                ob_end_clean();
            }
            
            // Set headers
            header('Content-Type: application/json; charset=utf-8');
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');
            
            // <PERSON>le preflight requests
            if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
                http_response_code(200);
                exit;
            }
            
            // Include required dependencies
            if (!class_exists('Config')) {
                require_once __DIR__ . '/config.php';
            }
            if (!class_exists('Database')) {
                require_once __DIR__ . '/database.php';
            }
            if (!class_exists('TenantManager')) {
                require_once __DIR__ . '/tenant_manager.php';
            }
            if (!function_exists('errorResponse')) {
                require_once __DIR__ . '/functions.php';
            }
            if (!class_exists('Application')) {
                require_once __DIR__ . '/../store-admin/core/Application.php';
            }

            // Initialize systems
            Config::init();
            TenantManager::init();
            Application::init();
            
            // Get database
            $this->db = TenantManager::getDatabase();
            
            $this->initialized = true;
            
        } catch (Exception $e) {
            $this->handleFatalError('API initialization failed', $e);
        }
    }
    
    /**
     * Handle fatal errors with proper JSON response
     */
    protected function handleFatalError(string $message, Exception $e = null): void
    {
        // Clean any output
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // Log the error
        $errorDetails = $message;
        if ($e) {
            $errorDetails .= ': ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine();
            error_log("API Fatal Error: " . $errorDetails);
            error_log("Stack trace: " . $e->getTraceAsString());
        }
        
        // Send error response
        http_response_code(500);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'error' => 'API temporarily unavailable',
            'message' => 'API temporarily unavailable'
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Validate required parameters
     */
    protected function validateRequired(array $params, array $required): void
    {
        $missing = [];
        foreach ($required as $field) {
            if (!isset($params[$field]) || $params[$field] === '' || $params[$field] === null) {
                $missing[] = $field;
            }
        }
        
        if (!empty($missing)) {
            errorResponse('Missing required parameters: ' . implode(', ', $missing), 400);
        }
    }
    
    /**
     * Get request parameters (GET or POST)
     */
    protected function getParams(): array
    {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $input = file_get_contents('php://input');
            $decoded = json_decode($input, true);
            return $decoded ?: $_POST;
        }
        return $_GET;
    }
    
    /**
     * Validate request method
     */
    protected function validateMethod(array $allowedMethods): void
    {
        if (!in_array($_SERVER['REQUEST_METHOD'], $allowedMethods)) {
            errorResponse('Method not allowed', 405);
        }
    }
    
    /**
     * Safe database query with error handling
     */
    protected function safeQuery(string $sql, array $params = []): array
    {
        try {
            return $this->db->fetchAll($sql, $params);
        } catch (Exception $e) {
            error_log("Database query failed: " . $e->getMessage() . " SQL: " . $sql);
            errorResponse('Database query failed', 500);
        }
    }
    
    /**
     * Safe single row fetch with error handling
     */
    protected function safeFetchRow(string $sql, array $params = []): ?array
    {
        try {
            return $this->db->fetchRow($sql, $params);
        } catch (Exception $e) {
            error_log("Database fetch failed: " . $e->getMessage() . " SQL: " . $sql);
            errorResponse('Database query failed', 500);
        }
    }
    
    /**
     * Safe column fetch with error handling
     */
    protected function safeFetchColumn(string $sql, array $params = []): mixed
    {
        try {
            return $this->db->fetchColumn($sql, $params);
        } catch (Exception $e) {
            error_log("Database fetch failed: " . $e->getMessage() . " SQL: " . $sql);
            errorResponse('Database query failed', 500);
        }
    }
    
    /**
     * Log activity with error handling
     */
    protected function logActivity(string $message, string $level = 'info'): void
    {
        try {
            error_log("[$level] API: $message");
        } catch (Exception $e) {
            // Ignore logging errors
        }
    }
}
