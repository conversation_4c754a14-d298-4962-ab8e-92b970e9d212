<?php

/**
 * Environment Configuration Manager
 * Secure environment variable handling with fallback support
 */

class Environment
{
    private static array $cache = [];
    private static bool $loaded = false;

    /**
     * Load environment variables from .env file
     */
    public static function load(): void
    {
        if (self::$loaded) {
            return;
        }

        $envFile = __DIR__ . '/../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '#') === 0) {
                    continue; // Skip comments
                }
                
                if (strpos($line, '=') !== false) {
                    [$key, $value] = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value, " \t\n\r\0\x0B\"'");
                    
                    if (!empty($key)) {
                        self::$cache[$key] = $value;
                        $_ENV[$key] = $value;
                        putenv("$key=$value");
                    }
                }
            }
        }

        self::$loaded = true;
    }

    /**
     * Get environment variable with fallback
     */
    public static function get(string $key, string $default = ''): string
    {
        self::load();

        // Check cache first
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }

        // Check $_ENV
        if (isset($_ENV[$key])) {
            return $_ENV[$key];
        }

        // Check getenv()
        $value = getenv($key);
        if ($value !== false) {
            return $value;
        }

        return $default;
    }

    /**
     * Check if environment variable exists
     */
    public static function has(string $key): bool
    {
        self::load();
        return isset(self::$cache[$key]) || isset($_ENV[$key]) || getenv($key) !== false;
    }

    /**
     * Generate secure random password
     */
    public static function generateSecurePassword(int $length = 16): string
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
        $password = '';
        $max = strlen($chars) - 1;
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[random_int(0, $max)];
        }
        
        return $password;
    }

    /**
     * Generate secure random string
     */
    public static function generateSecureKey(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * Validate required environment variables
     */
    public static function validateRequired(array $requiredKeys): array
    {
        $missing = [];
        foreach ($requiredKeys as $key) {
            if (!self::has($key) || empty(self::get($key))) {
                $missing[] = $key;
            }
        }
        return $missing;
    }
}
