-- Migration to add tables for bulk action logging
-- Run this script to add the necessary tables for bulk action functionality

-- Email log table
CREATE TABLE IF NOT EXISTS email_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id VARCHAR(50),
    subject VARCHAR(255),
    message TEXT,
    sent_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- SMS log table
CREATE TABLE IF NOT EXISTS sms_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id VARCHAR(50),
    message TEXT,
    sent_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Employee notifications table
CREATE TABLE IF NOT EXISTS employee_notifications (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    employee_id VARCHAR(50),
    type VARCHAR(50),
    message TEXT,
    sent_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Reminder log table
CREATE TABLE IF NOT EXISTS reminder_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    reservation_id VARCHAR(50),
    customer_id VARCHAR(50),
    type VARCHAR(20), -- email, sms, both
    message TEXT,
    sent_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance (after tables are created)
CREATE INDEX IF NOT EXISTS idx_email_log_customer_id ON email_log(customer_id);
CREATE INDEX IF NOT EXISTS idx_email_log_sent_at ON email_log(sent_at);

CREATE INDEX IF NOT EXISTS idx_sms_log_customer_id ON sms_log(customer_id);
CREATE INDEX IF NOT EXISTS idx_sms_log_sent_at ON sms_log(sent_at);

CREATE INDEX IF NOT EXISTS idx_employee_notifications_employee_id ON employee_notifications(employee_id);
CREATE INDEX IF NOT EXISTS idx_employee_notifications_sent_at ON employee_notifications(sent_at);

CREATE INDEX IF NOT EXISTS idx_reminder_log_reservation_id ON reminder_log(reservation_id);
CREATE INDEX IF NOT EXISTS idx_reminder_log_customer_id ON reminder_log(customer_id);
CREATE INDEX IF NOT EXISTS idx_reminder_log_sent_at ON reminder_log(sent_at);

-- Add schedule column to employees table if it doesn't exist (SQLite compatible)
-- Note: SQLite doesn't support ADD COLUMN IF NOT EXISTS, so we'll handle this in the migration script
