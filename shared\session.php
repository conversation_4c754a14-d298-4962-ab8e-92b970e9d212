<?php

/**
 * Session Management System
 * Handles secure session management with automatic regeneration and security features
 */

class SessionManager
{
    private static bool $started = false;

    /**
     * Start session with security measures
     */
    public static function start(): void
    {
        if (self::$started || session_status() === PHP_SESSION_ACTIVE) {
            return;
        }

        // Configure session security settings
        ini_set('session.cookie_httponly', 1);
        ini_set('session.use_strict_mode', 1);
        ini_set('session.cookie_samesite', 'Strict');

        session_start();
        self::$started = true;

        // Regenerate session ID periodically for security
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
    
    /**
     * Set session variable
     */
    public static function set(string $key, mixed $value): void
    {
        self::start();
        $_SESSION[$key] = $value;
    }

    /**
     * Get session variable with optional default
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::start();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session variable exists
     */
    public static function has(string $key): bool
    {
        self::start();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session variable
     */
    public static function remove(string $key): void
    {
        self::start();
        unset($_SESSION[$key]);
    }
    
    /**
     * Destroy session completely and clear cookies
     */
    public static function destroy(): void
    {
        self::start();
        $_SESSION = [];

        // Clear session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }

        session_destroy();
        self::$started = false;
    }
    
    public static function flash(string $key, mixed $value = null): mixed
    {
        self::start();
        
        if ($value !== null) {
            $_SESSION['flash'][$key] = $value;
            return $value;
        }
        
        $flashValue = $_SESSION['flash'][$key] ?? null;
        unset($_SESSION['flash'][$key]);
        
        return $flashValue;
    }
    
    public static function hasFlash(string $key): bool
    {
        self::start();
        return isset($_SESSION['flash'][$key]);
    }
    
    public static function generateCsrfToken(): string
    {
        self::start();
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    public static function verifyCsrfToken(string $token): bool
    {
        self::start();
        
        return isset($_SESSION['csrf_token']) && 
               hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public static function isLoggedIn(): bool
    {
        return self::get('admin_logged_in', false) === true;
    }
    
    public static function login(array $user): void
    {
        self::start();
        
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['login_time'] = time();
    }
    
    public static function logout(): void
    {
        self::start();
        
        unset($_SESSION['admin_logged_in']);
        unset($_SESSION['admin_user_id']);
        unset($_SESSION['admin_username']);
        unset($_SESSION['login_time']);
    }
    
    public static function getUser(): ?array
    {
        if (!self::isLoggedIn()) {
            return null;
        }
        
        return [
            'id' => self::get('admin_user_id'),
            'username' => self::get('admin_username'),
            'login_time' => self::get('login_time')
        ];
    }
    
    public static function requireAuth(): void
    {
        if (!self::isLoggedIn()) {
            self::flash('error', 'Please log in to access this page.');
            header('Location: /store-admin/login.php');
            exit;
        }
    }
    
    public static function regenerateId(): void
    {
        self::start();
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}
