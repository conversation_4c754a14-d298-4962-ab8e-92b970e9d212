<?php

/**
 * File Permissions Setup Script
 * Sets appropriate file permissions for security
 */

echo "🔒 GK Radevou File Permissions Setup\n";
echo "====================================\n\n";

$isWindows = strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';

if ($isWindows) {
    echo "ℹ️  Windows detected - File permissions are handled by NTFS ACLs\n";
    echo "   Manual steps required:\n";
    echo "   1. Right-click on .env file → Properties → Security\n";
    echo "   2. Remove 'Everyone' and 'Users' groups\n";
    echo "   3. Keep only 'Administrators' and your user account\n";
    echo "   4. Ensure web server user has read access only\n\n";
    
    // Check if .env file exists and is readable
    if (file_exists('.env')) {
        echo "✅ .env file exists and is readable\n";
        
        // Check if file is writable (should be for owner only)
        if (is_writable('.env')) {
            echo "⚠️  .env file is writable - ensure only owner can write\n";
        } else {
            echo "✅ .env file is not writable by current process\n";
        }
    } else {
        echo "❌ .env file not found - copy from .env.example\n";
    }
    
} else {
    echo "🐧 Unix/Linux detected - Setting file permissions\n\n";
    
    $files = [
        '.env' => '600',
        'storage/logs' => '755',
        'storage/temp' => '755',
        'storage/uploads' => '755',
        'storage/backups' => '755',
        'data' => '755'
    ];
    
    foreach ($files as $file => $permission) {
        if (file_exists($file)) {
            $result = chmod($file, octdec($permission));
            if ($result) {
                echo "✅ Set $file permissions to $permission\n";
            } else {
                echo "❌ Failed to set $file permissions to $permission\n";
            }
        } else {
            echo "⚠️  $file not found - skipping\n";
        }
    }
}

echo "\n🔐 Security Recommendations:\n";
echo "- Keep .env file secure and never commit to version control\n";
echo "- Use strong passwords for admin accounts\n";
echo "- Set DEVELOPMENT_MODE=false in production\n";
echo "- Use production payment keys in live environment\n";
echo "- Regularly update passwords and API keys\n";
echo "- Monitor logs for suspicious activity\n\n";

echo "✅ File permissions setup complete!\n";
