<?php

/**
 * Edit Category Page
 * Dedicated page for editing existing categories
 */

// Get category ID
$categoryId = $_GET['id'] ?? '';
if (!$categoryId) {
    Application::redirect('/store-admin/?page=categories', 'Category ID is required', 'error');
}

// Get category data
$category = $db->fetchRow("SELECT * FROM categories WHERE id = :id", [':id' => $categoryId]);
if (!$category) {
    Application::redirect('/store-admin/?page=categories', 'Category not found', 'error');
}

// Handle form submission
if ($_POST && !isset($_POST['ajax'])) {
    $_POST['id'] = $categoryId; // Ensure ID is set
    require_once __DIR__ . '/../controllers/categories.php';
    $result = handleCategoriesForm($_POST, $db);
    if ($result['success']) {
        Application::redirect('/store-admin/?page=categories', $result['message'], 'success');
    } else {
        $error = $result['error'];
    }
}
?>

<div class="page-header">
    <div class="page-header-left">
        <h1 class="page-title">Edit Category</h1>
        <div class="breadcrumb">
            <a href="/store-admin/?page=categories">Categories</a>
            <span class="breadcrumb-separator">/</span>
            <span><?php echo htmlspecialchars($category['name']); ?></span>
        </div>
    </div>
    <div class="page-header-right">
        <a href="/store-admin/?page=categories" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Categories
        </a>
    </div>
</div>

<?php if (isset($error)): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<div class="content-card">
    <form method="POST" class="entity-form">
        <input type="hidden" name="action" value="save">
        <input type="hidden" name="id" value="<?php echo $category['id']; ?>">
        <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">

        <div class="form-section">
            <h3 class="form-section-title">Category Information</h3>

            <div class="form-group">
                <label for="name">Category Name *</label>
                <input type="text" id="name" name="name" required
                    value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : htmlspecialchars($category['name']); ?>"
                    placeholder="Enter category name">
            </div>

            <div class="form-group">
                <label for="description">Description</label>
                <textarea id="description" name="description" rows="3"
                    placeholder="Describe this category..."><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : htmlspecialchars($category['description']); ?></textarea>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="icon">Icon Class</label>
                    <input type="text" id="icon" name="icon"
                        placeholder="e.g., fas fa-cut"
                        value="<?php echo isset($_POST['icon']) ? htmlspecialchars($_POST['icon']) : htmlspecialchars($category['icon']); ?>">
                    <small class="help-text">Use Font Awesome icon classes</small>
                </div>
                <div class="form-group">
                    <label for="color">Color</label>
                    <input type="color" id="color" name="color"
                        value="<?php echo isset($_POST['color']) ? $_POST['color'] : htmlspecialchars($category['color'] ?: '#3498db'); ?>">
                    <small class="help-text">This color will be used for the category display</small>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="sort_order">Sort Order</label>
                    <input type="number" id="sort_order" name="sort_order" min="0"
                        value="<?php echo isset($_POST['sort_order']) ? $_POST['sort_order'] : $category['sort_order']; ?>"
                        placeholder="0">
                    <small class="help-text">Lower numbers appear first</small>
                </div>
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="is_active"
                            <?php echo (isset($_POST['is_active']) ? $_POST['is_active'] : $category['is_active']) ? 'checked' : ''; ?>>
                        <span class="checkmark"></span>
                        Active
                    </label>
                </div>
            </div>
        </div>

        <!-- Service Assignment Section -->
        <div class="form-section">
            <h3 class="form-section-title">Assign Services</h3>
            <div class="service-checkboxes">
                <?php
                // Get all services with their current category assignment
                $allServices = $db->fetchAll("SELECT id, name, price, duration, category_id FROM services ORDER BY name");
                $assignedServiceIds = [];

                foreach ($allServices as $service) {
                    if ($service['category_id'] == $category['id']) {
                        $assignedServiceIds[] = $service['id'];
                    }
                }
                ?>

                <?php if (empty($allServices)): ?>
                    <p class="text-muted">No services available. <a href="/store-admin/?page=add-service">Add services first</a>.</p>
                <?php else: ?>
                    <?php foreach ($allServices as $service): ?>
                        <label class="service-checkbox">
                            <input type="checkbox" name="service_ids[]" value="<?php echo $service['id']; ?>"
                                <?php echo in_array($service['id'], $assignedServiceIds) ? 'checked' : ''; ?>>
                            <div class="service-info">
                                <span><?php echo htmlspecialchars($service['name']); ?></span>
                                <small><?php echo $service['duration']; ?> min - €<?php echo number_format($service['price'], 2); ?></small>
                            </div>
                        </label>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save"></i> Update Category
            </button>
            <a href="/store-admin/?page=categories" class="btn btn-secondary">
                <i class="fas fa-times"></i> Cancel
            </a>
        </div>
    </form>
</div>

<style>
    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid var(--border-color);
    }

    .page-header-left h1 {
        margin: 0 0 0.5rem 0;
        color: var(--text-primary);
    }

    .breadcrumb {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .breadcrumb a {
        color: var(--primary-color);
        text-decoration: none;
    }

    .breadcrumb a:hover {
        text-decoration: underline;
    }

    .breadcrumb-separator {
        margin: 0 0.5rem;
    }

    .content-card {
        background: white;
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        padding: 2rem;
        margin-bottom: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
    }

    .form-section-title {
        margin: 0 0 1.5rem 0;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-primary);
        font-size: 1.125rem;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        font-size: 0.875rem;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .help-text {
        display: block;
        font-size: 0.75rem;
        color: var(--text-muted);
        margin-top: 0.25rem;
    }

    .checkbox-label {
        display: flex;
        align-items: center;
        cursor: pointer;
        font-weight: 500;
    }

    .checkbox-label input[type="checkbox"] {
        margin-right: 0.5rem;
        width: auto;
    }

    .form-actions {
        display: flex;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
    }

    .alert {
        padding: 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1.5rem;
    }

    .alert-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .content-card {
            padding: 1rem;
        }
    }

    /* Service Assignment Styles */
    .service-checkboxes {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }

    .service-checkbox {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .service-checkbox:hover {
        border-color: var(--primary-color);
        background-color: var(--light-color);
    }

    .service-checkbox input[type="checkbox"] {
        margin-right: 0.75rem;
        width: auto;
    }

    .service-info span {
        font-weight: 500;
        display: block;
    }

    .service-info small {
        color: var(--text-muted);
        font-size: 0.75rem;
    }

    @media (max-width: 768px) {
        .service-checkboxes {
            grid-template-columns: 1fr;
        }
    }
</style>