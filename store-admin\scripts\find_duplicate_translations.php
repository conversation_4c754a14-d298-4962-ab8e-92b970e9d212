<?php
/**
 * Find Duplicate Translations Script
 * Identifies and analyzes duplicate translation entries
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';

function findDuplicateTranslations(): void {
    echo "🔍 Finding Duplicate Translations\n";
    echo str_repeat("=", 60) . "\n";

    try {
        Config::init();
        TenantManager::init();
        $db = TenantManager::getDatabase();

        // Find duplicates by key and category
        echo "1. Checking for duplicates by key + category...\n";
        $duplicatesByKeyCategory = $db->fetchAll("
            SELECT key, category, COUNT(*) as count
            FROM translations
            GROUP BY key, category
            HAVING COUNT(*) > 1
            ORDER BY count DESC, key
        ");

        if (empty($duplicatesByKeyCategory)) {
            echo "   ✅ No duplicates found by key + category\n";
        } else {
            echo "   ❌ Found " . count($duplicatesByKeyCategory) . " duplicate key+category combinations:\n";
            foreach ($duplicatesByKeyCategory as $dup) {
                echo "     - '{$dup['key']}' in '{$dup['category']}': {$dup['count']} entries\n";
            }
        }

        // Find duplicates by key only (across categories)
        echo "\n2. Checking for duplicates by key only (across categories)...\n";
        $duplicatesByKey = $db->fetchAll("
            SELECT key, COUNT(*) as count, GROUP_CONCAT(DISTINCT category) as categories
            FROM translations
            GROUP BY key
            HAVING COUNT(*) > 1
            ORDER BY count DESC, key
        ");

        if (empty($duplicatesByKey)) {
            echo "   ✅ No key duplicates across categories\n";
        } else {
            echo "   ⚠️  Found " . count($duplicatesByKey) . " keys used in multiple categories:\n";
            foreach ($duplicatesByKey as $dup) {
                echo "     - '{$dup['key']}': {$dup['count']} entries in categories: {$dup['categories']}\n";

                // Show details for each cross-category duplicate
                $details = $db->fetchAll("
                    SELECT category, value_el, value_en
                    FROM translations
                    WHERE key = :key
                    ORDER BY category
                ", [':key' => $dup['key']]);

                foreach ($details as $detail) {
                    echo "       [{$detail['category']}] EL: {$detail['value_el']} | EN: {$detail['value_en']}\n";
                }
            }
        }

        // Find exact duplicates (same key, category, and values)
        echo "\n3. Checking for exact duplicates (same key, category, values)...\n";
        $exactDuplicates = $db->fetchAll("
            SELECT key, category, value_el, value_en, COUNT(*) as count
            FROM translations
            GROUP BY key, category, value_el, value_en
            HAVING COUNT(*) > 1
            ORDER BY count DESC, key
        ");

        if (empty($exactDuplicates)) {
            echo "   ✅ No exact duplicates found\n";
        } else {
            echo "   ❌ Found " . count($exactDuplicates) . " exact duplicates:\n";
            foreach ($exactDuplicates as $dup) {
                echo "     - '{$dup['key']}' ({$dup['category']}): {$dup['count']} identical entries\n";
                echo "       Greek: {$dup['value_el']}\n";
                echo "       English: {$dup['value_en']}\n";
            }
        }

        // Show detailed duplicate entries
        if (!empty($duplicatesByKeyCategory)) {
            echo "\n4. Detailed duplicate analysis...\n";
            echo str_repeat("-", 60) . "\n";

            foreach ($duplicatesByKeyCategory as $dup) {
                $entries = $db->fetchAll("
                    SELECT id, key, category, value_el, value_en, created_at, updated_at
                    FROM translations
                    WHERE key = :key AND category = :category
                    ORDER BY updated_at DESC, created_at DESC
                ", [
                    ':key' => $dup['key'],
                    ':category' => $dup['category']
                ]);

                echo "\n🔍 Key: '{$dup['key']}' Category: '{$dup['category']}' ({$dup['count']} entries)\n";
                
                foreach ($entries as $i => $entry) {
                    $status = $i === 0 ? '✅ KEEP (newest)' : '❌ DELETE';
                    echo "   {$status} ID: {$entry['id']}\n";
                    echo "     Greek: {$entry['value_el']}\n";
                    echo "     English: {$entry['value_en']}\n";
                    echo "     Created: {$entry['created_at']}\n";
                    echo "     Updated: {$entry['updated_at']}\n";
                }
            }
        }

        // Summary statistics
        echo "\n📊 Database Statistics\n";
        echo str_repeat("=", 60) . "\n";
        
        $totalTranslations = $db->fetchColumn("SELECT COUNT(*) FROM translations");
        $uniqueKeys = $db->fetchColumn("SELECT COUNT(DISTINCT key) FROM translations");
        $categories = $db->fetchAll("SELECT category, COUNT(*) as count FROM translations GROUP BY category ORDER BY count DESC");
        
        echo "Total translations: {$totalTranslations}\n";
        echo "Unique keys: {$uniqueKeys}\n";
        echo "Categories:\n";
        foreach ($categories as $cat) {
            echo "  - {$cat['category']}: {$cat['count']} translations\n";
        }

        // Recommendations
        echo "\n💡 Recommendations\n";
        echo str_repeat("=", 60) . "\n";
        
        if (!empty($duplicatesByKeyCategory)) {
            echo "❌ Action needed: Remove duplicate translations\n";
            echo "   - Keep the most recent entry for each key+category\n";
            echo "   - Delete older duplicates\n";
            echo "   - Run cleanup script to fix this\n";
        } else {
            echo "✅ No duplicates found - database is clean\n";
        }

        if (!empty($duplicatesByKey)) {
            echo "⚠️  Review needed: Keys used in multiple categories\n";
            echo "   - This might be intentional (same text in different contexts)\n";
            echo "   - Or it might indicate category confusion\n";
            echo "   - Review each case manually\n";
        }

    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Run the analysis
findDuplicateTranslations();
?>
