<?php
/**
 * Translation Discovery and Sync Script
 * Discovers all t() calls in the codebase and syncs them with the admin interface
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

class TranslationDiscovery
{
    private Database $db;
    private array $discoveredTranslations = [];
    private array $stats = [
        'files_scanned' => 0,
        'translations_found' => 0,
        'new_translations' => 0,
        'updated_translations' => 0
    ];

    public function __construct()
    {
        Config::init();
        TenantManager::init();
        Translation::init();
        $this->db = TenantManager::getDatabase();
    }

    /**
     * Scan all relevant directories for t() calls
     */
    public function discoverTranslations(): array
    {
        echo "🔍 Discovering translation calls in codebase...\n";
        echo str_repeat("=", 60) . "\n";

        $directories = [
            __DIR__ . '/../../client',
            __DIR__ . '/../../client/views',
            __DIR__ . '/../../client/js',
        ];

        foreach ($directories as $dir) {
            if (is_dir($dir)) {
                $this->scanDirectory($dir);
            }
        }

        echo "\n📊 Discovery Statistics:\n";
        echo "- Files scanned: {$this->stats['files_scanned']}\n";
        echo "- Translation calls found: {$this->stats['translations_found']}\n";
        echo "- Unique translations: " . count($this->discoveredTranslations) . "\n\n";

        return $this->discoveredTranslations;
    }

    /**
     * Scan a directory recursively for PHP files
     */
    private function scanDirectory(string $dir): void
    {
        $files = glob($dir . '/*.php');
        
        foreach ($files as $file) {
            $this->scanFile($file);
        }

        // Scan subdirectories
        $subdirs = glob($dir . '/*', GLOB_ONLYDIR);
        foreach ($subdirs as $subdir) {
            $this->scanDirectory($subdir);
        }
    }

    /**
     * Scan a single file for t() calls
     */
    private function scanFile(string $file): void
    {
        $content = file_get_contents($file);
        if ($content === false) {
            return;
        }

        $this->stats['files_scanned']++;
        $relativePath = str_replace(__DIR__ . '/../../', '', $file);
        echo "Scanning: {$relativePath}\n";

        // Pattern to match t() calls with various formats
        $patterns = [
            // t('key', 'default', 'category')
            '/t\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)/',
            // t("key", "default", "category")
            '/t\s*\(\s*"([^"]+)"\s*,\s*"([^"]*)"\s*(?:,\s*"([^"]*)")?\s*\)/',
            // Translation::t() calls
            '/Translation::t\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $key = $match[1];
                    $defaultValue = $match[2] ?? '';
                    $category = $match[3] ?? 'client'; // Default category for t() calls

                    $this->stats['translations_found']++;
                    
                    // Store discovered translation
                    $this->discoveredTranslations[$key] = [
                        'key' => $key,
                        'default_value' => $defaultValue,
                        'category' => $category,
                        'file' => $relativePath,
                        'found_in_db' => false
                    ];
                }
            }
        }
    }

    /**
     * Sync discovered translations with database
     */
    public function syncWithDatabase(): array
    {
        echo "🔄 Syncing discovered translations with database...\n";
        echo str_repeat("=", 60) . "\n";

        $results = [
            'added' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => []
        ];

        foreach ($this->discoveredTranslations as $key => $translation) {
            try {
                // Check if translation already exists
                $existing = $this->db->fetchRow("
                    SELECT * FROM translations 
                    WHERE key = :key AND category = :category
                ", [
                    ':key' => $translation['key'],
                    ':category' => $translation['category']
                ]);

                if ($existing) {
                    // Translation exists - mark as found
                    $this->discoveredTranslations[$key]['found_in_db'] = true;
                    $results['skipped']++;
                    echo "✓ Found: {$translation['key']} ({$translation['category']})\n";
                } else {
                    // Add new translation
                    $this->addTranslation($translation);
                    $results['added']++;
                    echo "➕ Added: {$translation['key']} ({$translation['category']})\n";
                }
            } catch (Exception $e) {
                $results['errors'][] = "Error processing {$translation['key']}: " . $e->getMessage();
                echo "❌ Error: {$translation['key']} - " . $e->getMessage() . "\n";
            }
        }

        echo "\n📊 Sync Results:\n";
        echo "- Added: {$results['added']}\n";
        echo "- Already existed: {$results['skipped']}\n";
        echo "- Errors: " . count($results['errors']) . "\n";

        if (!empty($results['errors'])) {
            echo "\n❌ Errors:\n";
            foreach ($results['errors'] as $error) {
                echo "  - {$error}\n";
            }
        }

        return $results;
    }

    /**
     * Add a new translation to the database
     */
    private function addTranslation(array $translation): void
    {
        // Use Translation::save method which handles duplicates properly
        $valueEl = !empty($translation['default_value']) ? $translation['default_value'] : $translation['key'];
        $valueEn = ''; // Leave English empty for admin to fill

        // Use the Translation::save method which handles duplicates and constraints
        $success = Translation::save($translation['key'], $valueEl, $valueEn, $translation['category']);

        if (!$success) {
            throw new Exception("Failed to save translation: {$translation['key']}");
        }
    }

    /**
     * Generate a report of discovered translations
     */
    public function generateReport(): void
    {
        echo "\n📋 Translation Discovery Report\n";
        echo str_repeat("=", 60) . "\n";

        // Group by category
        $byCategory = [];
        foreach ($this->discoveredTranslations as $translation) {
            $category = $translation['category'];
            if (!isset($byCategory[$category])) {
                $byCategory[$category] = [];
            }
            $byCategory[$category][] = $translation;
        }

        foreach ($byCategory as $category => $translations) {
            echo "\n📁 Category: {$category} (" . count($translations) . " translations)\n";
            echo str_repeat("-", 40) . "\n";
            
            foreach ($translations as $translation) {
                $status = $translation['found_in_db'] ? '✓' : '➕';
                echo "  {$status} {$translation['key']}\n";
                if (!empty($translation['default_value'])) {
                    echo "      Default: {$translation['default_value']}\n";
                }
                echo "      File: {$translation['file']}\n";
            }
        }
    }

    /**
     * Run complete discovery and sync process
     */
    public function run(): void
    {
        echo "🚀 Starting Translation Discovery and Sync\n";
        echo "==========================================\n\n";

        // Step 1: Discover translations
        $this->discoverTranslations();

        // Step 2: Sync with database
        $this->syncWithDatabase();

        // Step 3: Generate report
        $this->generateReport();

        echo "\n✅ Translation discovery and sync completed!\n";
        echo "You can now manage these translations in the admin interface.\n";
    }
}

// Run the discovery if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $discovery = new TranslationDiscovery();
        $discovery->run();
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
        exit(1);
    }
}
?>
