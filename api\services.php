<?php

/**
 * Services API
 * Provides service data for the booking system
 */

// Basic error handling
error_reporting(0);
ini_set('display_errors', 0);

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit;
}

try {
    // Simple response function
    function sendResponse($success, $data = null, $message = 'Success') {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data
        ];
        echo json_encode($response);
        exit;
    }

    // Initialize tenant management
    require_once __DIR__ . '/../shared/tenant_manager.php';
    TenantManager::init();

    // Get current tenant database
    $db = TenantManager::getDatabase();
    if (!$db) {
        sendResponse(false, null, 'Database not available');
    }
    
    // Check if category_id is provided
    if (isset($_GET['category_id'])) {
        // Fetch services for specific category
        $services = $db->fetchAll("
            SELECT s.*, c.name as category_name
            FROM services s
            LEFT JOIN categories c ON s.category_id = c.id
            WHERE s.category_id = :category_id AND s.is_active = 1
            ORDER BY s.sort_order, s.name
        ", [':category_id' => $_GET['category_id']]);

        // Add basic translation support
        foreach ($services as &$service) {
            $service['translated_name'] = $service['name'];
            $service['translated_description'] = $service['description'] ?? '';
        }

        sendResponse(true, $services);

    } else {
        // Fetch all services grouped by category
        $services = $db->fetchAll("
            SELECT s.*, c.name as category_name
            FROM services s
            LEFT JOIN categories c ON s.category_id = c.id
            WHERE s.is_active = 1
            ORDER BY c.sort_order, c.name, s.sort_order, s.name
        ");
        
        // Group by category
        $grouped = [];
        foreach ($services as $service) {
            $categoryName = $service['category_name'];
            if (!isset($grouped[$categoryName])) {
                $grouped[$categoryName] = [];
            }
            
            // Add basic translation support
            $service['translated_name'] = $service['name'];
            $service['translated_description'] = $service['description'] ?? '';
            
            $grouped[$categoryName][] = $service;
        }
        
        sendResponse(true, $grouped);
    }

} catch (Exception $e) {
    sendResponse(false, null, 'Error: ' . $e->getMessage());
} catch (Error $e) {
    sendResponse(false, null, 'Fatal Error: ' . $e->getMessage());
}

?>
