<?php
/**
 * Add Translation Constraints Script
 * Adds proper database constraints to prevent duplicate translations
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';

function addTranslationConstraints(): void {
    echo "🔒 Adding Translation Database Constraints\n";
    echo str_repeat("=", 60) . "\n";

    try {
        Config::init();
        TenantManager::init();
        $db = TenantManager::getDatabase();

        // Check current schema
        echo "1. Checking current database schema...\n";
        $tableInfo = $db->fetchAll("PRAGMA table_info(translations)");
        
        echo "   Current columns:\n";
        foreach ($tableInfo as $column) {
            echo "     - {$column['name']} ({$column['type']}) " . 
                 ($column['notnull'] ? 'NOT NULL' : 'NULL') . 
                 ($column['pk'] ? ' PRIMARY KEY' : '') . "\n";
        }

        // Check existing indexes
        echo "\n2. Checking existing indexes...\n";
        $indexes = $db->fetchAll("PRAGMA index_list(translations)");
        
        if (empty($indexes)) {
            echo "   No indexes found\n";
        } else {
            foreach ($indexes as $index) {
                $indexInfo = $db->fetchAll("PRAGMA index_info({$index['name']})");
                $columns = array_column($indexInfo, 'name');
                echo "   - {$index['name']}: " . implode(', ', $columns) . 
                     ($index['unique'] ? ' (UNIQUE)' : '') . "\n";
            }
        }

        // Add unique constraint on key + category
        echo "\n3. Adding unique constraint on (key, category)...\n";
        
        try {
            // First, check if constraint already exists
            $existingConstraint = false;
            foreach ($indexes as $index) {
                if ($index['unique'] && strpos($index['name'], 'key') !== false) {
                    $existingConstraint = true;
                    break;
                }
            }

            if ($existingConstraint) {
                echo "   ✅ Unique constraint already exists\n";
            } else {
                // Create unique index
                $db->query("
                    CREATE UNIQUE INDEX IF NOT EXISTS idx_translations_key_category 
                    ON translations(key, category)
                ");
                echo "   ✅ Added unique index on (key, category)\n";
            }
        } catch (Exception $e) {
            echo "   ❌ Failed to add unique constraint: " . $e->getMessage() . "\n";
        }

        // Add index on category for faster filtering
        echo "\n4. Adding index on category...\n";
        try {
            $db->query("
                CREATE INDEX IF NOT EXISTS idx_translations_category 
                ON translations(category)
            ");
            echo "   ✅ Added index on category\n";
        } catch (Exception $e) {
            echo "   ❌ Failed to add category index: " . $e->getMessage() . "\n";
        }

        // Add index on updated_at for versioning
        echo "\n5. Adding index on updated_at...\n";
        try {
            $db->query("
                CREATE INDEX IF NOT EXISTS idx_translations_updated_at 
                ON translations(updated_at)
            ");
            echo "   ✅ Added index on updated_at\n";
        } catch (Exception $e) {
            echo "   ❌ Failed to add updated_at index: " . $e->getMessage() . "\n";
        }

        // Verify constraints
        echo "\n6. Verifying constraints...\n";
        $newIndexes = $db->fetchAll("PRAGMA index_list(translations)");
        
        foreach ($newIndexes as $index) {
            $indexInfo = $db->fetchAll("PRAGMA index_info({$index['name']})");
            $columns = array_column($indexInfo, 'name');
            echo "   ✅ {$index['name']}: " . implode(', ', $columns) . 
                 ($index['unique'] ? ' (UNIQUE)' : '') . "\n";
        }

        // Test constraint by trying to insert duplicate
        echo "\n7. Testing duplicate prevention...\n";
        $testKey = 'test_duplicate_' . time();
        
        try {
            // Insert first entry
            $db->query("
                INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                VALUES (:id1, :key, 'Test 1', 'Test 1', 'test', :created_at, :updated_at)
            ", [
                ':id1' => 'TEST1',
                ':key' => $testKey,
                ':created_at' => date('Y-m-d H:i:s'),
                ':updated_at' => date('Y-m-d H:i:s')
            ]);
            echo "   ✅ First entry inserted successfully\n";

            // Try to insert duplicate
            try {
                $db->query("
                    INSERT INTO translations (id, key, value_el, value_en, category, created_at, updated_at)
                    VALUES (:id2, :key, 'Test 2', 'Test 2', 'test', :created_at, :updated_at)
                ", [
                    ':id2' => 'TEST2',
                    ':key' => $testKey,
                    ':created_at' => date('Y-m-d H:i:s'),
                    ':updated_at' => date('Y-m-d H:i:s')
                ]);
                echo "   ❌ Duplicate was allowed (constraint not working)\n";
            } catch (Exception $e) {
                echo "   ✅ Duplicate prevented: " . $e->getMessage() . "\n";
            }

            // Clean up test entry
            $db->query("DELETE FROM translations WHERE key = :key", [':key' => $testKey]);
            echo "   ✅ Test entry cleaned up\n";

        } catch (Exception $e) {
            echo "   ❌ Test failed: " . $e->getMessage() . "\n";
        }

        echo "\n📊 Final Database Statistics\n";
        echo str_repeat("=", 60) . "\n";
        
        $totalTranslations = $db->fetchColumn("SELECT COUNT(*) FROM translations");
        $uniqueKeys = $db->fetchColumn("SELECT COUNT(DISTINCT key) FROM translations");
        $categories = $db->fetchAll("SELECT category, COUNT(*) as count FROM translations GROUP BY category ORDER BY count DESC");
        
        echo "Total translations: {$totalTranslations}\n";
        echo "Unique keys: {$uniqueKeys}\n";
        echo "Categories:\n";
        foreach ($categories as $cat) {
            echo "  - {$cat['category']}: {$cat['count']} translations\n";
        }

        echo "\n✅ Translation constraints successfully added!\n";
        echo "Database is now protected against duplicate translations.\n";

    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Run the constraint addition
addTranslationConstraints();
?>
