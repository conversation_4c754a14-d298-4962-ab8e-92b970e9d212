<?php

/**
 * Enhanced Availability Checker
 * Handles complex availability logic with comprehensive time conflict detection
 */

require_once __DIR__ . '/time_calculator.php';

class AvailabilityChecker
{
    private Database $db;
    private TimeCalculator $timeCalculator;

    public function __construct(Database $db)
    {
        $this->db = $db;
        $this->timeCalculator = new TimeCalculator(Config::TIMEZONE);
    }
    
    public function isTimeSlotAvailable(string $serviceId, string $date, string $time): bool
    {
        // Get service details
        $service = $this->db->fetchRow(
            "SELECT * FROM services WHERE id = :id AND is_active = 1",
            [':id' => $serviceId]
        );

        if (!$service) {
            return false;
        }

        // Basic validations
        if (!$this->isValidDate($date)) {
            return false;
        }

        if (!$this->isValidTime($time)) {
            return false;
        }

        if ($this->isPastDateTime($date, $time)) {
            return false;
        }

        if (!$this->isWithinBookingAdvanceLimit($date)) {
            return false;
        }

        if (!$this->isWorkingDay($date)) {
            return false;
        }

        if (!$this->isWithinWorkingHours($date, $time)) {
            return false;
        }

        if (!$this->isWithinServiceHours($serviceId, $date, $time)) {
            return false;
        }

        // Check if any capable employee is available for this service at this time
        $availabilityResult = $this->checkEmployeeAvailabilityForService($serviceId, $date, $time, $service);
        return $availabilityResult['available'];
    }
    
    public function getAvailableTimeSlots(string $serviceId, string $date): array
    {
        $service = $this->db->fetchRow(
            "SELECT * FROM services WHERE id = :id AND is_active = 1",
            [':id' => $serviceId]
        );

        if (!$service) {
            return [];
        }

        if (!$this->isValidDate($date) || !$this->isWorkingDay($date)) {
            return [];
        }

        $workingHours = $this->getWorkingHours($date);
        if (!$workingHours) {
            return [];
        }

        // Generate time slots for all periods
        $availableSlots = [];

        if (isset($workingHours['periods']) && is_array($workingHours['periods']) && count($workingHours['periods']) > 0) {
            // Multiple periods (e.g., 9:00-14:00 and 17:00-21:00)
            foreach ($workingHours['periods'] as $period) {
                if (!isset($period['start']) || !isset($period['end'])) {
                    continue;
                }

                $periodSlots = $this->generateTimeSlots($period['start'], $period['end'], Config::SLOT_DURATION_MINUTES);

                // Filter available slots for this period
                foreach ($periodSlots as $slot) {
                    if ($this->isTimeSlotAvailable($serviceId, $date, $slot)) {
                        $availableSlots[] = $slot;
                    }
                }
            }
        } else {
            // Single period (backward compatibility)
            $startTime = $workingHours['start_time'] ?? '09:00';
            $endTime = $workingHours['end_time'] ?? '17:00';
            $slots = $this->generateTimeSlots($startTime, $endTime, Config::SLOT_DURATION_MINUTES);

            // Filter available slots
            foreach ($slots as $slot) {
                if ($this->isTimeSlotAvailable($serviceId, $date, $slot)) {
                    $availableSlots[] = $slot;
                }
            }
        }

        return $availableSlots;
    }

    /**
     * Get available time slots (simplified version for AJAX calls)
     * Returns array of available time strings
     */
    public function getAvailableSlots(string $serviceId, string $date, ?string $employeeId = null): array
    {
        $availableSlots = $this->getAvailableTimeSlots($serviceId, $date);

        // If specific employee is requested, filter by that employee's availability
        if ($employeeId) {
            $filteredSlots = [];
            foreach ($availableSlots as $slot) {
                if ($this->isEmployeeAvailableAtTime($employeeId, $date, $slot, 60)) { // Default 60 min duration for check
                    $filteredSlots[] = $slot;
                }
            }
            return $filteredSlots;
        }

        return $availableSlots;
    }

    /**
     * Get detailed availability for all time slots in a day
     * Returns availability status with reasons for unavailability
     */
    public function getDetailedTimeSlotAvailability(string $serviceId, string $date): array
    {
        $service = $this->db->fetchRow(
            "SELECT * FROM services WHERE id = :id AND is_active = 1",
            [':id' => $serviceId]
        );

        if (!$service) {
            return [];
        }

        if (!$this->isValidDate($date)) {
            return [];
        }

        // Check if it's a working day
        if (!$this->isWorkingDay($date)) {
            return []; // Return empty for non-working days
        }

        // Always use store working hours for time slot generation
        $workingHours = $this->getWorkingHours($date);
        if (!$workingHours) {
            return [];
        }

        // Generate time slots for all periods with interval indicators
        $slots = [];
        if (isset($workingHours['periods']) && is_array($workingHours['periods']) && count($workingHours['periods']) > 0) {
            // Multiple periods (e.g., 9:00-14:00 and 17:00-21:00)
            foreach ($workingHours['periods'] as $periodIndex => $period) {
                // Ensure period has required fields
                if (!isset($period['start']) || !isset($period['end'])) {
                    continue;
                }

                $periodSlots = $this->generateTimeSlots($period['start'], $period['end'], Config::SLOT_DURATION_MINUTES);

                // Mark slots with period information for interval display
                foreach ($periodSlots as $slot) {
                    $slots[] = [
                        'time' => $slot,
                        'period' => $periodIndex,
                        'period_start' => $period['start'],
                        'period_end' => $period['end'],
                        'is_interval_break' => false
                    ];
                }

                // Add interval break indicator if there's a next period
                if ($periodIndex < count($workingHours['periods']) - 1) {
                    $nextPeriod = $workingHours['periods'][$periodIndex + 1];
                    if (isset($nextPeriod['start'])) {
                        $slots[] = [
                            'time' => 'INTERVAL_BREAK',
                            'period' => -1,
                            'break_start' => $period['end'],
                            'break_end' => $nextPeriod['start'],
                            'is_interval_break' => true
                        ];
                    }
                }
            }
        } else {
            // Single period (backward compatibility)
            $startTime = $workingHours['start_time'] ?? '09:00';
            $endTime = $workingHours['end_time'] ?? '17:00';
            $periodSlots = $this->generateTimeSlots($startTime, $endTime, Config::SLOT_DURATION_MINUTES);
            foreach ($periodSlots as $slot) {
                $slots[] = [
                    'time' => $slot,
                    'period' => 0,
                    'period_start' => $startTime,
                    'period_end' => $endTime,
                    'is_interval_break' => false
                ];
            }
        }

        $detailedAvailability = [];
        foreach ($slots as $slotData) {
            if ($slotData['is_interval_break']) {
                // Add interval break indicator
                $detailedAvailability['INTERVAL_' . $slotData['break_start'] . '_' . $slotData['break_end']] = [
                    'available' => false,
                    'reason' => 'interval_break',
                    'break_start' => $slotData['break_start'],
                    'break_end' => $slotData['break_end'],
                    'is_interval_break' => true
                ];
            } else {
                $slot = $slotData['time'];
                $availability = $this->checkTimeSlotWithReason($serviceId, $date, $slot, $service);
                $availability['period'] = $slotData['period'];
                $availability['period_start'] = $slotData['period_start'];
                $availability['period_end'] = $slotData['period_end'];
                $detailedAvailability[$slot] = $availability;
            }
        }

        return $detailedAvailability;
    }

    /**
     * Get monthly availability for calendar display
     * Returns true/false for each date in the month
     */
    public function getMonthlyAvailability(string $serviceId, string $year, string $month): array
    {
        $service = $this->db->fetchRow(
            "SELECT * FROM services WHERE id = :id AND is_active = 1",
            [':id' => $serviceId]
        );

        if (!$service) {
            return [];
        }

        $availability = [];
        $daysInMonth = cal_days_in_month(CAL_GREGORIAN, (int)$month, (int)$year);

        for ($day = 1; $day <= $daysInMonth; $day++) {
            $date = sprintf('%04d-%02d-%02d', $year, $month, $day);

            // Check if date is valid and not in the past
            if (!$this->isValidDate($date) || $this->isPastDate($date)) {
                $availability[$date] = false;
                continue;
            }

            // Check if date is within booking advance limit
            if (!$this->isWithinBookingAdvanceLimit($date)) {
                $availability[$date] = false;
                continue;
            }

            // Check if it's a working day (store is open)
            if (!$this->isWorkingDay($date)) {
                $availability[$date] = false;
                continue;
            }

            // Check if any employee who can perform this service is working on this date
            if (!$this->hasAvailableEmployeeOnDate($serviceId, $date)) {
                $availability[$date] = false;
                continue;
            }

            // Check if there are any available time slots for this date
            $timeSlots = $this->getAvailableTimeSlots($serviceId, $date);
            $availability[$date] = !empty($timeSlots);
        }

        return $availability;
    }

    /**
     * Check if date is in the past
     */
    private function isPastDate(string $date): bool
    {
        $today = new DateTime('today', new DateTimeZone(Config::TIMEZONE));
        $checkDate = DateTime::createFromFormat('Y-m-d', $date, new DateTimeZone(Config::TIMEZONE));

        return $checkDate < $today;
    }

    /**
     * Check if any employee who can perform the service is working on the given date
     */
    private function hasAvailableEmployeeOnDate(string $serviceId, string $date): bool
    {
        // Get employees who can perform this service
        $employees = $this->db->fetchAll(
            "SELECT e.* FROM employees e
             JOIN employee_services es ON e.id = es.employee_id
             WHERE es.service_id = :service_id AND e.is_active = 1",
            [':service_id' => $serviceId]
        );

        if (empty($employees)) {
            // If no employees are specifically assigned to this service,
            // check if there are any active employees at all
            $anyEmployees = $this->db->fetchAll(
                "SELECT * FROM employees WHERE is_active = 1 LIMIT 1"
            );

            if (empty($anyEmployees)) {
                return false; // No employees exist at all
            }

            // If employees exist but none are assigned to this service,
            // assume the service is available (auto-assignment)
            return true;
        }

        $dayOfWeek = date('w', strtotime($date));

        foreach ($employees as $employee) {
            if ($this->isEmployeeWorkingOnDay($employee, $dayOfWeek, $date)) {
                return true; // At least one employee is working
            }
        }

        return false; // No employees are working on this day
    }

    /**
     * Check if a specific employee is working on a specific day
     */
    public function isEmployeeWorkingOnDay(array $employee, int $dayOfWeek, string $date): bool
    {
        // Get store working hours for this date first
        $storeHours = $this->getWorkingHours($date);
        if (!$storeHours) {
            return false; // Store is closed
        }

        // Check employee working hours
        if (empty($employee['working_hours'])) {
            // If no specific employee hours, assume they work when store is open
            return true;
        }

        try {
            $employeeHours = json_decode($employee['working_hours'], true);
            if (!$employeeHours || !is_array($employeeHours)) {
                // Invalid employee hours, assume they work when store is open
                return true;
            }

            // Convert day of week number to day name
            $dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            $dayName = $dayNames[$dayOfWeek];

            // Check if employee works on this day
            if (!isset($employeeHours[$dayName])) {
                return false; // Employee doesn't work on this day
            }

            $daySchedule = $employeeHours[$dayName];

            // Handle the format: {"monday": [{"start": "09:00", "end": "17:00"}], "tuesday": []}
            if (is_array($daySchedule)) {
                // Empty array means employee doesn't work on this day
                if (empty($daySchedule)) {
                    return false;
                }

                // Non-empty array means employee works on this day
                // We could check specific hours here, but for calendar availability,
                // we just need to know if they work at all on this day
                return true;
            }

            // Fallback for other formats
            return !empty($daySchedule);

        } catch (Exception $e) {
            error_log("Employee working hours check error: " . $e->getMessage());
            // On error, assume employee works when store is open
            return true;
        }
    }

    /**
     * Check time slot availability with detailed reason
     */
    private function checkTimeSlotWithReason(string $serviceId, string $date, string $time, array $service): array
    {
        // Check if time is in the past
        if ($this->isPastDateTime($date, $time)) {
            return [
                'available' => false,
                'reason' => 'past_time',
                'message' => 'Time slot is in the past'
            ];
        }

        // Check if service can fit in the remaining time (including prep/cleanup/buffer)
        if (!$this->serviceCanFitInTimeSlot($service, $date, $time)) {
            return [
                'available' => false,
                'reason' => 'insufficient_time',
                'message' => 'Not enough time before closing'
            ];
        }

        // Check if any capable employee is available for this service at this time
        return $this->checkEmployeeAvailabilityForService($serviceId, $date, $time, $service);
    }

    /**
     * Check if any capable employee is available for the service at the given time
     * This replaces the old checkServiceDurationConflict that incorrectly checked
     * ALL reservations instead of checking employee-specific availability
     *
     * Now provides detailed reasons for better user feedback
     */
    private function checkEmployeeAvailabilityForService(string $serviceId, string $date, string $time, array $service): array
    {
        // First check if any capable employee is working at this time
        if (!$this->isAnyCapableEmployeeWorkingAtTime($serviceId, $date, $time)) {
            return [
                'available' => false,
                'reason' => 'employee_unavailable',
                'message' => 'No staff working at this time'
            ];
        }

        // Get all employees who can perform this service
        $capableEmployees = $this->db->fetchAll(
            "SELECT e.* FROM employees e
             JOIN employee_services es ON e.id = es.employee_id
             WHERE es.service_id = :service_id AND e.is_active = 1",
            [':service_id' => $serviceId]
        );

        if (empty($capableEmployees)) {
            return [
                'available' => false,
                'reason' => 'employee_unavailable',
                'message' => 'No staff can perform this service'
            ];
        }

        // Check each capable employee and find the most specific reason if none are available
        $conflictReasons = [];

        foreach ($capableEmployees as $employee) {
            // Only check employees who are actually working at this time
            if (!$this->isEmployeeWorkingAtSpecificTime($employee['id'], $date, $time)) {
                continue; // Skip employees not working at this time
            }

            if (!$this->employeeHasConflict($employee['id'], $date, $time, $service['duration'])) {
                // Found an available employee who is working
                return [
                    'available' => true,
                    'reason' => 'available',
                    'message' => 'Available for booking',
                    'employee_id' => $employee['id']
                ];
            } else {
                // Employee is working but has conflict, determine why
                $conflictReasons[] = $this->getEmployeeConflictReason($employee['id'], $date, $time, $service);
            }
        }

        // All employees are busy, return the most relevant reason
        return $this->getBestConflictReason($conflictReasons);
    }

    /**
     * Get specific reason why an employee has a conflict
     */
    private function getEmployeeConflictReason(string $employeeId, string $date, string $time, array $service): array
    {
        try {
            // Calculate appointment time boundaries
            $globalBufferTime = 0;
            if (class_exists('Application')) {
                $globalBufferTime = (int)Application::getSetting('global_buffer_time', 15);
            }
            $appointmentTimes = $this->timeCalculator->calculateAppointmentTimes($date, $time, $service, $globalBufferTime);

            // Get conflicting reservations for this employee
            $conflictingReservations = $this->db->fetchAll(
                "SELECT r.*, s.duration, s.buffer_time, s.preparation_time, s.cleanup_time
                 FROM reservations r
                 JOIN services s ON r.service_id = s.id
                 WHERE r.employee_id = :employee_id
                 AND r.date = :date
                 AND r.status IN ('confirmed', 'pending')",
                [
                    ':employee_id' => $employeeId,
                    ':date' => $date
                ]
            );

            foreach ($conflictingReservations as $reservation) {
                $existingTimes = $this->timeCalculator->parseReservationTimes($reservation);
                $bufferTime = (int)($reservation['buffer_time'] ?? $globalBufferTime);
                $bufferedExisting = $this->timeCalculator->addBufferToReservation($existingTimes, $bufferTime);

                if ($this->timeCalculator->checkTimeOverlap(
                    $appointmentTimes['buffer_start'],
                    $appointmentTimes['buffer_end'],
                    $bufferedExisting['start'],
                    $bufferedExisting['end']
                )) {
                    // Determine if it's a direct booking conflict or duration conflict
                    $appointmentStart = $appointmentTimes['appointment_start'];
                    $existingStart = $existingTimes['start'];
                    $existingEnd = $existingTimes['end'];

                    if ($appointmentStart >= $existingStart && $appointmentStart < $existingEnd) {
                        return [
                            'available' => false,
                            'reason' => 'booked',
                            'message' => 'Time slot is already booked'
                        ];
                    } else {
                        return [
                            'available' => false,
                            'reason' => 'duration_conflict',
                            'message' => 'Service duration would conflict with existing booking'
                        ];
                    }
                }
            }

            // If we get here, there might be other reasons (working hours, etc.)
            return [
                'available' => false,
                'reason' => 'employee_unavailable',
                'message' => 'Staff not available at this time'
            ];

        } catch (Exception $e) {
            error_log("Employee conflict reason detection error: " . $e->getMessage());
            return [
                'available' => false,
                'reason' => 'employee_unavailable',
                'message' => 'Staff availability check failed'
            ];
        }
    }

    /**
     * Choose the best conflict reason from multiple employee conflicts
     */
    private function getBestConflictReason(array $conflictReasons): array
    {
        if (empty($conflictReasons)) {
            return [
                'available' => false,
                'reason' => 'employee_unavailable',
                'message' => 'No staff available at this time'
            ];
        }

        // Priority order: booked > duration_conflict > employee_unavailable
        $priorityOrder = ['booked', 'duration_conflict', 'employee_unavailable'];

        foreach ($priorityOrder as $priority) {
            foreach ($conflictReasons as $reason) {
                if ($reason['reason'] === $priority) {
                    return $reason;
                }
            }
        }

        // Fallback to first reason
        return $conflictReasons[0];
    }

    /**
     * Check if two time ranges overlap
     */
    private function timesOverlap(DateTime $start1, DateTime $end1, DateTime $start2, DateTime $end2): bool
    {
        // Two ranges overlap if start1 < end2 AND start2 < end1
        // Since we have 0 buffer time, end of one can be start of next (no overlap)
        // So we use strict < comparison (not <=)
        return $start1 < $end2 && $start2 < $end1;
    }

    /**
     * Check if service can fit in the time slot considering all time components
     */
    private function serviceCanFitInTimeSlot(array $service, string $date, string $time): bool
    {
        try {
            // Get working hours for this date
            $workingHours = $this->getWorkingHours($date);
            if (!$workingHours) {
                return false;
            }

            // Calculate all appointment times
            $globalBufferTime = 15; // Default value
            if (class_exists('Application')) {
                $globalBufferTime = (int)Application::getSetting('global_buffer_time', 15);
            }
            $appointmentTimes = $this->timeCalculator->calculateAppointmentTimes($date, $time, $service, $globalBufferTime);

            // Check if appointment (including buffer) fits within any working period
            $bufferStart = $appointmentTimes['buffer_start'];
            $bufferEnd = $appointmentTimes['buffer_end'];

            // Check against all periods
            if (isset($workingHours['periods']) && is_array($workingHours['periods'])) {
                foreach ($workingHours['periods'] as $period) {
                    if (!isset($period['start']) || !isset($period['end'])) {
                        continue;
                    }

                    $workStart = DateTime::createFromFormat('Y-m-d H:i', "$date {$period['start']}");
                    $workEnd = DateTime::createFromFormat('Y-m-d H:i', "$date {$period['end']}");

                    if ($bufferStart >= $workStart && $bufferEnd <= $workEnd) {
                        return true; // Fits in this period
                    }
                }
                return false; // Doesn't fit in any period
            } else {
                // Single period fallback
                $workingEndTime = DateTime::createFromFormat('Y-m-d H:i', "$date {$workingHours['end_time']}");
                return $appointmentTimes['buffer_end'] <= $workingEndTime;
            }

        } catch (Exception $e) {
            error_log("Service fit check error: " . $e->getMessage());
            return false;
        }
    }

    private function isValidDate(string $date): bool
    {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
    
    private function isValidTime(string $time): bool
    {
        $t = DateTime::createFromFormat('H:i', $time);
        return $t && $t->format('H:i') === $time;
    }
    
    private function isPastDateTime(string $date, string $time): bool
    {
        $datetime = strtotime($date . ' ' . $time);
        return $datetime < time();
    }

    private function isWithinBookingAdvanceLimit(string $date): bool
    {
        // Get booking advance days setting (default 60 days)
        $advanceDays = 60; // Default value
        if (class_exists('Application')) {
            $advanceDays = (int)Application::getSetting('booking_advance_days', 60);
        }

        $today = new DateTime('today', new DateTimeZone(Config::TIMEZONE));
        $maxBookingDate = clone $today;
        $maxBookingDate->add(new DateInterval("P{$advanceDays}D"));

        $checkDate = DateTime::createFromFormat('Y-m-d', $date, new DateTimeZone(Config::TIMEZONE));

        return $checkDate <= $maxBookingDate;
    }
    
    public function isWorkingDay(string $date): bool
    {
        $dayOfWeek = date('w', strtotime($date));

        // Check special days first
        $specialDay = $this->db->fetchRow(
            "SELECT * FROM special_days WHERE date = :date",
            [':date' => $date]
        );

        if ($specialDay) {
            return !$specialDay['is_closed'];
        }

        // Use business hours from settings instead of database
        $businessHours = '{}'; // Default empty
        if (class_exists('Application')) {
            $businessHours = Application::getSetting('business_hours', '{}');
        }
        $businessHoursData = json_decode($businessHours, true);

        if (!is_array($businessHoursData)) {
            return false;
        }

        $dayName = strtolower(date('l', strtotime($date)));

        return isset($businessHoursData[$dayName]) &&
               is_array($businessHoursData[$dayName]) &&
               !empty($businessHoursData[$dayName]);
    }
    
    private function isWithinWorkingHours(string $date, string $time): bool
    {
        $workingHours = $this->getWorkingHours($date);

        if (!$workingHours) {
            return false;
        }

        $timeStamp = strtotime($time);

        // Check against all periods
        if (isset($workingHours['periods']) && is_array($workingHours['periods'])) {
            foreach ($workingHours['periods'] as $period) {
                if (!isset($period['start']) || !isset($period['end'])) {
                    continue;
                }

                $startStamp = strtotime($period['start']);
                $endStamp = strtotime($period['end']);

                if ($timeStamp >= $startStamp && $timeStamp < $endStamp) {
                    return true; // Time is within this period
                }
            }
            return false; // Time is not within any period
        } else {
            // Single period fallback
            $startStamp = strtotime($workingHours['start_time']);
            $endStamp = strtotime($workingHours['end_time']);

            return $timeStamp >= $startStamp && $timeStamp < $endStamp;
        }
    }
    
    public function getWorkingHours(string $date): ?array
    {
        $dayOfWeek = date('w', strtotime($date));

        // Check special days first (stored in settings as JSON)
        if (class_exists('Application')) {
            $specialDaysJson = Application::getSetting('special_days', '');
            if ($specialDaysJson) {
                $specialDays = json_decode($specialDaysJson, true);
                if ($specialDays && isset($specialDays[$date])) {
                    $specialDay = $specialDays[$date];

                    // If it's closed all day, return null
                    if (empty($specialDay['hours'])) {
                        return null;
                    }

                    // If it has custom hours, return all periods
                    if (!empty($specialDay['hours']) && is_array($specialDay['hours'])) {
                        return [
                            'start_time' => $specialDay['hours'][0]['start'], // First period for compatibility
                            'end_time' => $specialDay['hours'][0]['end'],
                            'is_active' => 1,
                            'periods' => $specialDay['hours'] // All periods
                        ];
                    }
                }
            }
        }

        // Regular working hours - try new format first
        if (class_exists('Application')) {
            $businessHoursJson = Application::getSetting('business_hours', '');
            if ($businessHoursJson) {
                $businessHours = json_decode($businessHoursJson, true);
                if ($businessHours) {
                    $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                    $dayName = $days[$dayOfWeek];

                    // Check if day exists in business hours
                    if (isset($businessHours[$dayName])) {
                        $periods = $businessHours[$dayName];

                        // If periods is empty array, store is closed
                        if (empty($periods)) {
                            return null;
                        }

                        // Return working hours with periods
                        return [
                            'start_time' => $periods[0]['start'], // First period for compatibility
                            'end_time' => $periods[0]['end'],
                            'is_active' => 1,
                            'periods' => $periods // All periods
                        ];
                    }
                }
            }
        }

        // Fallback to old working_hours table
        $workingHours = $this->db->fetchRow(
            "SELECT * FROM working_hours WHERE day_of_week = :day AND is_active = 1",
            [':day' => $dayOfWeek]
        );

        if ($workingHours && !empty($workingHours['periods'])) {
            $periods = json_decode($workingHours['periods'], true);
            if ($periods) {
                $workingHours['periods'] = $periods;
            }
        }

        return $workingHours;
    }

    /**
     * Get effective working hours (intersection of store hours and employee hours)
     */
    public function getEffectiveWorkingHours(string $serviceId, string $date): ?array
    {
        // Get store working hours
        $storeHours = $this->getWorkingHours($date);
        if (!$storeHours) {
            return null;
        }

        // Get employees who can perform this service
        $employees = $this->db->fetchAll(
            "SELECT e.* FROM employees e
             JOIN employee_services es ON e.id = es.employee_id
             WHERE es.service_id = :service_id AND e.is_active = 1",
            [':service_id' => $serviceId]
        );

        if (empty($employees)) {
            return null;
        }

        $dayOfWeek = date('w', strtotime($date));
        $dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        $dayName = $dayNames[$dayOfWeek];

        // Get store periods
        $storePeriods = [];
        if (isset($storeHours['periods']) && is_array($storeHours['periods'])) {
            $storePeriods = $storeHours['periods'];
        } else {
            // Single period fallback
            $storePeriods = [[
                'start' => $storeHours['start_time'],
                'end' => $storeHours['end_time']
            ]];
        }

        // Collect all effective periods from all employees
        $effectivePeriods = [];

        foreach ($employees as $employee) {
            // Get employee working periods for this day
            $employeePeriods = [];

            if (empty($employee['working_hours'])) {
                // Employee with no specific hours works store hours
                $employeePeriods = $storePeriods;
            } else {
                try {
                    $employeeHours = json_decode($employee['working_hours'], true);
                    if (!isset($employeeHours[$dayName]) || empty($employeeHours[$dayName])) {
                        continue; // Employee doesn't work on this day
                    }

                    $employeePeriods = $employeeHours[$dayName];
                } catch (Exception $e) {
                    // On error, use store hours
                    $employeePeriods = $storePeriods;
                }
            }

            // Calculate intersections between employee periods and store periods
            foreach ($employeePeriods as $empPeriod) {
                if (!isset($empPeriod['start']) || !isset($empPeriod['end'])) {
                    continue;
                }

                foreach ($storePeriods as $storePeriod) {
                    if (!isset($storePeriod['start']) || !isset($storePeriod['end'])) {
                        continue;
                    }

                    // Calculate intersection
                    $effectiveStart = max($empPeriod['start'], $storePeriod['start']);
                    $effectiveEnd = min($empPeriod['end'], $storePeriod['end']);

                    // Add valid intersection to effective periods
                    if ($effectiveStart < $effectiveEnd) {
                        $effectivePeriods[] = [
                            'start' => $effectiveStart,
                            'end' => $effectiveEnd
                        ];
                    }
                }
            }
        }

        if (empty($effectivePeriods)) {
            return null; // No employees available
        }

        // Merge overlapping periods and find overall range
        $mergedPeriods = $this->mergeOverlappingPeriods($effectivePeriods);

        // Find earliest start and latest end for backward compatibility
        $earliestStart = null;
        $latestEnd = null;

        foreach ($mergedPeriods as $period) {
            if ($earliestStart === null || $period['start'] < $earliestStart) {
                $earliestStart = $period['start'];
            }
            if ($latestEnd === null || $period['end'] > $latestEnd) {
                $latestEnd = $period['end'];
            }
        }

        return [
            'start_time' => $earliestStart,
            'end_time' => $latestEnd,
            'periods' => $mergedPeriods // Include all effective periods
        ];
    }

    /**
     * Merge overlapping time periods
     */
    private function mergeOverlappingPeriods(array $periods): array
    {
        if (empty($periods)) {
            return [];
        }

        // Sort periods by start time
        usort($periods, function($a, $b) {
            return strcmp($a['start'], $b['start']);
        });

        $merged = [$periods[0]];

        for ($i = 1; $i < count($periods); $i++) {
            $current = $periods[$i];
            $lastMerged = &$merged[count($merged) - 1];

            // If current period overlaps with last merged period, merge them
            if ($current['start'] <= $lastMerged['end']) {
                $lastMerged['end'] = max($lastMerged['end'], $current['end']);
            } else {
                // No overlap, add as new period
                $merged[] = $current;
            }
        }

        return $merged;
    }

    private function isWithinServiceHours(string $serviceId, string $date, string $time): bool
    {
        // Check if any capable employee is working at this time
        return $this->isAnyCapableEmployeeWorkingAtTime($serviceId, $date, $time);
    }

    /**
     * Check if any employee capable of performing the service is working at the given time
     * This is the key method that determines if a time slot should be available or greyed out
     */
    private function isAnyCapableEmployeeWorkingAtTime(string $serviceId, string $date, string $time): bool
    {
        // Get employees who can perform this service
        $employees = $this->db->fetchAll(
            "SELECT e.* FROM employees e
             JOIN employee_services es ON e.id = es.employee_id
             WHERE es.service_id = :service_id AND e.is_active = 1",
            [':service_id' => $serviceId]
        );

        if (empty($employees)) {
            return false; // No employees can perform this service
        }

        // Check if at least one capable employee is working at this time
        foreach ($employees as $employee) {
            if ($this->isEmployeeWorkingAtSpecificTime($employee['id'], $date, $time)) {
                return true; // Found at least one employee working
            }
        }

        return false; // No capable employees are working at this time
    }

    /**
     * Check if a specific employee is working at a given time
     * This checks the employee's individual working hours
     */
    private function isEmployeeWorkingAtSpecificTime(string $employeeId, string $date, string $time): bool
    {
        try {
            // Get employee working hours
            $employee = $this->db->fetchRow(
                "SELECT working_hours FROM employees WHERE id = :id",
                [':id' => $employeeId]
            );

            if (!$employee || empty($employee['working_hours'])) {
                // If no specific employee hours, assume they work store hours
                return true;
            }

            $workingHours = json_decode($employee['working_hours'], true);
            if (!$workingHours) {
                // If invalid JSON, assume they work store hours
                return true;
            }

            // Get day name (monday, tuesday, etc.)
            $dayName = strtolower(date('l', strtotime($date)));

            // Check if employee works on this day
            if (!isset($workingHours[$dayName]) || empty($workingHours[$dayName])) {
                return false; // Employee doesn't work on this day
            }

            $daySchedule = $workingHours[$dayName];

            // Handle the format: {"monday": [{"start": "09:00", "end": "17:00"}]}
            if (is_array($daySchedule) && !empty($daySchedule)) {
                foreach ($daySchedule as $period) {
                    if (isset($period['start']) && isset($period['end'])) {
                        $startTime = $period['start'];
                        $endTime = $period['end'];

                        // Check if the given time falls within this working period
                        if ($time >= $startTime && $time < $endTime) {
                            return true;
                        }
                    }
                }
            }

            return false; // Time is outside employee's working hours

        } catch (Exception $e) {
            error_log("Employee working time check error: " . $e->getMessage());
            // On error, assume employee works (safer for availability)
            return true;
        }
    }

    /**
     * Check if all capable employees are busy at the given time
     * This replaces the old hasConflictingReservation method that incorrectly
     * blocked bookings when ANY reservation existed, regardless of employee availability
     */
    private function areAllCapableEmployeesBusy(string $serviceId, string $date, string $time): bool
    {
        // Get service details for time calculations
        $service = $this->db->fetchRow(
            "SELECT * FROM services WHERE id = :id",
            [':id' => $serviceId]
        );

        if (!$service) {
            return true; // If service not found, consider all employees busy
        }

        try {
            // Get all employees who can perform this service
            $capableEmployees = $this->db->fetchAll(
                "SELECT e.* FROM employees e
                 JOIN employee_services es ON e.id = es.employee_id
                 WHERE es.service_id = :service_id AND e.is_active = 1",
                [':service_id' => $serviceId]
            );

            if (empty($capableEmployees)) {
                return true; // No employees can perform this service
            }

            // Check if at least one capable employee is available
            foreach ($capableEmployees as $employee) {
                if (!$this->employeeHasConflict($employee['id'], $date, $time, $service['duration'])) {
                    return false; // Found an available employee
                }
            }

            return true; // All capable employees are busy

        } catch (Exception $e) {
            error_log("Employee availability check error: " . $e->getMessage());
            return true; // If error occurs, consider all employees busy for safety
        }
    }
    
    private function generateTimeSlots(string $startTime, string $endTime, int $slotDuration): array
    {
        $slots = [];
        $current = strtotime($startTime);
        $end = strtotime($endTime);
        
        while ($current < $end) {
            $slots[] = date('H:i', $current);
            $current += $slotDuration * 60;
        }
        
        return $slots;
    }
    
    public function findAvailableEmployee(string $serviceId, string $date, string $time): ?string
    {
        $service = $this->db->fetchRow(
            "SELECT * FROM services WHERE id = :id",
            [':id' => $serviceId]
        );
        
        if (!$service) {
            return null;
        }
        
        // Get employees who can perform this service
        $employees = $this->db->fetchAll(
            "SELECT e.* FROM employees e
             JOIN employee_services es ON e.id = es.employee_id
             WHERE es.service_id = :service_id AND e.is_active = 1
             ORDER BY e.name",
            [':service_id' => $serviceId]
        );
        
        foreach ($employees as $employee) {
            if ($this->isEmployeeAvailableAtTime($employee['id'], $date, $time, $service['duration'])) {
                return $employee['id'];
            }
        }
        
        return null;
    }

    /**
     * Check if employee is available at specific time considering store hours intersection
     */
    public function isEmployeeAvailableAtTime(string $employeeId, string $date, string $time, int $duration): bool
    {
        // First check if employee has any conflicts (existing reservations)
        if ($this->employeeHasConflict($employeeId, $date, $time, $duration)) {
            return false;
        }

        // Check if time is within store working hours (already validated by caller)
        // But also check employee-specific working hours intersection
        return $this->isEmployeeWorkingAtTime($employeeId, $date, $time, $duration);
    }

    /**
     * Check if employee is working at specific time (intersection with store hours)
     */
    private function isEmployeeWorkingAtTime(string $employeeId, string $date, string $time, int $duration): bool
    {
        // Get store working hours for this date
        $storeHours = $this->getWorkingHours($date);
        if (!$storeHours) {
            return false;
        }

        // Get employee working hours
        $employee = $this->db->fetchRow(
            "SELECT working_hours FROM employees WHERE id = :id",
            [':id' => $employeeId]
        );

        if (!$employee || empty($employee['working_hours'])) {
            // If no specific employee hours, check if time fits within any store period
            return $this->isTimeWithinMultiplePeriods($time, $duration, $storeHours);
        }

        try {
            $employeeHours = json_decode($employee['working_hours'], true);
            $dayOfWeek = date('w', strtotime($date));

            // Convert day of week number to day name
            $dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
            $dayName = $dayNames[$dayOfWeek];

            // Check if employee works on this day using the correct format
            if (!isset($employeeHours[$dayName])) {
                return false; // Employee doesn't work on this day
            }

            $daySchedule = $employeeHours[$dayName];

            // Handle the format: {"monday": [{"start": "09:00", "end": "17:00"}], "tuesday": []}
            if (is_array($daySchedule)) {
                // Empty array means employee doesn't work on this day
                if (empty($daySchedule)) {
                    return false;
                }

                // Check all employee periods against all store periods
                return $this->isTimeWithinEmployeeAndStorePeriods($time, $duration, $daySchedule, $storeHours);
            } else {
                // Fallback for other formats
                return false;
            }

        } catch (Exception $e) {
            error_log("Employee working hours check error: " . $e->getMessage());
            // Fallback to store hours only
            return $this->isTimeWithinMultiplePeriods($time, $duration, $storeHours);
        }
    }

    /**
     * Check if time fits within multiple periods (store or employee)
     */
    private function isTimeWithinMultiplePeriods(string $time, int $duration, array $hours): bool
    {
        // Handle multiple periods
        if (isset($hours['periods']) && is_array($hours['periods'])) {
            foreach ($hours['periods'] as $period) {
                if (isset($period['start']) && isset($period['end'])) {
                    if ($this->isTimeWithinHours($time, $duration, $period['start'], $period['end'])) {
                        return true;
                    }
                }
            }
            return false;
        }

        // Handle single period (backward compatibility)
        if (isset($hours['start_time']) && isset($hours['end_time'])) {
            return $this->isTimeWithinHours($time, $duration, $hours['start_time'], $hours['end_time']);
        }

        return false;
    }

    /**
     * Check if time fits within intersection of employee periods and store periods
     */
    private function isTimeWithinEmployeeAndStorePeriods(string $time, int $duration, array $employeePeriods, array $storeHours): bool
    {
        // Get store periods
        $storePeriods = [];
        if (isset($storeHours['periods']) && is_array($storeHours['periods'])) {
            $storePeriods = $storeHours['periods'];
        } else {
            // Single period fallback
            $storePeriods = [[
                'start' => $storeHours['start_time'],
                'end' => $storeHours['end_time']
            ]];
        }

        // Check each employee period against each store period
        foreach ($employeePeriods as $empPeriod) {
            if (!isset($empPeriod['start']) || !isset($empPeriod['end'])) {
                continue;
            }

            foreach ($storePeriods as $storePeriod) {
                if (!isset($storePeriod['start']) || !isset($storePeriod['end'])) {
                    continue;
                }

                // Calculate intersection of employee period and store period
                $effectiveStart = max($empPeriod['start'], $storePeriod['start']);
                $effectiveEnd = min($empPeriod['end'], $storePeriod['end']);

                // Check if there's a valid intersection
                if ($effectiveStart < $effectiveEnd) {
                    // Check if appointment fits within this intersection
                    if ($this->isTimeWithinHours($time, $duration, $effectiveStart, $effectiveEnd)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Check if appointment time fits within given hours
     */
    private function isTimeWithinHours(string $time, int $duration, string $startTime, string $endTime): bool
    {
        $appointmentStart = DateTime::createFromFormat('H:i', $time);
        $appointmentEnd = clone $appointmentStart;
        $appointmentEnd->add(new DateInterval('PT' . $duration . 'M'));

        $workStart = DateTime::createFromFormat('H:i', $startTime);
        $workEnd = DateTime::createFromFormat('H:i', $endTime);

        return $appointmentStart >= $workStart && $appointmentEnd <= $workEnd;
    }

    /**
     * Enhanced employee conflict detection
     */
    private function employeeHasConflict(string $employeeId, string $date, string $time, int $duration): bool
    {
        try {
            // Create a temporary service array for time calculations
            $globalBufferTime = 0; // Default to 0
            if (class_exists('Application')) {
                $globalBufferTime = (int)Application::getSetting('global_buffer_time', 15);
            }
            $tempService = [
                'duration' => $duration,
                'preparation_time' => 0,
                'cleanup_time' => 0
            ];

            // Calculate appointment time boundaries
            $appointmentTimes = $this->timeCalculator->calculateAppointmentTimes($date, $time, $tempService, $globalBufferTime);

            $conflictingReservations = $this->db->fetchAll(
                "SELECT r.*, s.duration, s.preparation_time, s.cleanup_time
                 FROM reservations r
                 JOIN services s ON r.service_id = s.id
                 WHERE r.employee_id = :employee_id
                 AND r.date = :date
                 AND r.status IN ('confirmed', 'pending')",
                [
                    ':employee_id' => $employeeId,
                    ':date' => $date
                ]
            );

            foreach ($conflictingReservations as $reservation) {
                // Parse existing reservation times
                $existingTimes = $this->timeCalculator->parseReservationTimes($reservation);

                // Add global buffer time to existing reservation
                $globalBufferTime = 15; // Default to 15
                if (class_exists('Application')) {
                    $globalBufferTime = (int)Application::getSetting('global_buffer_time', 15);
                }
                $bufferTime = $globalBufferTime;
                $bufferedExisting = $this->timeCalculator->addBufferToReservation($existingTimes, $bufferTime);

                // Check for overlap
                if ($this->timeCalculator->checkTimeOverlap(
                    $appointmentTimes['buffer_start'],
                    $appointmentTimes['buffer_end'],
                    $bufferedExisting['start'],
                    $bufferedExisting['end']
                )) {
                    return true;
                }
            }

            return false;

        } catch (Exception $e) {
            error_log("Employee conflict detection error: " . $e->getMessage());
            return true; // If error occurs, consider it conflicted for safety
        }
    }

    /**
     * Comprehensive conflict detection for atomic booking operations
     * Returns detailed conflict information for better error messages
     */
    public function checkAllConflicts(string $serviceId, ?string $employeeId, string $date, string $time): array
    {
        $conflicts = [];

        try {
            // Get service details
            $service = $this->db->fetchRow(
                "SELECT * FROM services WHERE id = :id AND is_active = 1",
                [':id' => $serviceId]
            );

            if (!$service) {
                $conflicts[] = [
                    'type' => 'service_not_found',
                    'message' => 'Service not found or inactive'
                ];
                return $conflicts;
            }

            // Basic validations
            if (!$this->isValidDate($date)) {
                $conflicts[] = [
                    'type' => 'invalid_date',
                    'message' => 'Invalid date format'
                ];
            }

            if (!$this->isValidTime($time)) {
                $conflicts[] = [
                    'type' => 'invalid_time',
                    'message' => 'Invalid time format'
                ];
            }

            if ($this->timeCalculator->isPastDateTime($date, $time)) {
                $conflicts[] = [
                    'type' => 'past_datetime',
                    'message' => 'Cannot book appointments in the past'
                ];
            }

            if (!$this->isWorkingDay($date)) {
                $conflicts[] = [
                    'type' => 'non_working_day',
                    'message' => 'Business is closed on this day'
                ];
            }

            if (!$this->isWithinWorkingHours($date, $time)) {
                $conflicts[] = [
                    'type' => 'outside_working_hours',
                    'message' => 'Time is outside business hours'
                ];
            }

            // If employee is specified, check employee-specific conflicts
            if ($employeeId) {
                if ($this->employeeHasConflict($employeeId, $date, $time, $service['duration'])) {
                    $conflicts[] = [
                        'type' => 'employee_conflict',
                        'message' => 'Employee is not available at this time',
                        'employee_id' => $employeeId
                    ];
                }
            }

            // Check if any capable employee is available (only if no specific employee was requested)
            if (!$employeeId) {
                if ($this->areAllCapableEmployeesBusy($serviceId, $date, $time)) {
                    $conflicts[] = [
                        'type' => 'no_available_employees',
                        'message' => 'No staff available at this time'
                    ];
                }
            }

            return $conflicts;

        } catch (Exception $e) {
            error_log("Comprehensive conflict check error: " . $e->getMessage());
            return [
                [
                    'type' => 'system_error',
                    'message' => 'System error during availability check'
                ]
            ];
        }
    }

    /**
     * Find alternative available employees for a time slot
     */
    public function findAlternativeEmployees(string $serviceId, string $date, string $time, ?string $excludeEmployeeId = null): array
    {
        try {
            $service = $this->db->fetchRow(
                "SELECT * FROM services WHERE id = :id",
                [':id' => $serviceId]
            );

            if (!$service) {
                return [];
            }

            // Get all employees who can perform this service
            $employees = $this->db->fetchAll(
                "SELECT e.* FROM employees e
                 JOIN employee_services es ON e.id = es.employee_id
                 WHERE es.service_id = :service_id
                 AND e.is_active = 1" .
                 ($excludeEmployeeId ? " AND e.id != :exclude_id" : ""),
                array_filter([
                    ':service_id' => $serviceId,
                    ':exclude_id' => $excludeEmployeeId
                ])
            );

            $availableEmployees = [];

            foreach ($employees as $employee) {
                if (!$this->employeeHasConflict($employee['id'], $date, $time, $service['duration'])) {
                    $availableEmployees[] = $employee;
                }
            }

            return $availableEmployees;

        } catch (Exception $e) {
            error_log("Find alternative employees error: " . $e->getMessage());
            return [];
        }
    }
}
