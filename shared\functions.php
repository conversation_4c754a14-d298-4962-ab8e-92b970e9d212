<?php

/**
 * Common Utility Functions
 * Shared functions for validation, formatting, and data processing
 */

// Ensure dependencies are loaded
if (!class_exists('Config')) {
    require_once __DIR__ . '/config.php';
}

if (!class_exists('TenantManager')) {
    require_once __DIR__ . '/tenant_manager.php';
}

/**
 * Sanitize user input for safe output
 */
function sanitizeInput(string $input): string
{
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address format
 */
function validateEmail(string $email): bool
{
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate Greek phone number format
 */
function validatePhone(string $phone): bool
{
    $phone = preg_replace('/[^0-9]/', '', $phone);
    return preg_match('/^(69|21|22|23|24|25|26|27|28)\d{8}$/', $phone) === 1;
}

/**
 * Format phone number for display
 */
function formatPhone(string $phone): string
{
    $phone = preg_replace('/[^0-9]/', '', $phone);
    if (strlen($phone) === 10) {
        return substr($phone, 0, 3) . ' ' . substr($phone, 3, 3) . ' ' . substr($phone, 6);
    }
    return $phone;
}

/**
 * Format date string
 */
function formatDate(string $date, string $format = 'Y-m-d'): string
{
    try {
        return date($format, strtotime($date));
    } catch (Exception $e) {
        return $date;
    }
}

/**
 * Format time string
 */
function formatTime(string $time, string $format = 'H:i'): string
{
    try {
        return date($format, strtotime($time));
    } catch (Exception $e) {
        return $time;
    }
}

/**
 * Format currency amount
 */
function formatCurrency(float $amount, string $currency = '€'): string
{
    return $currency . number_format($amount, 2);
}

/**
 * Generate unique ID with optional prefix
 */
function generateId(string $prefix = ''): string
{
    $id = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
    return $prefix ? $prefix . '-' . $id : $id;
}

/**
 * Send successful JSON response
 */
function successResponse(mixed $data = null, string $message = 'Success'): void
{
    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Send error JSON response
 */
function errorResponse(string $message = 'Error', int $code = 400): void
{
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * Redirect to URL with optional parameters
 */
function redirect(string $url, array $params = []): void
{
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    header("Location: $url");
    exit;
}

/**
 * Log activity message with tenant context
 */
function logActivity(string $message, string $level = 'info'): void
{
    $timestamp = date('Y-m-d H:i:s');
    $tenant = TenantManager::getCurrentTenant() ?? 'system';

    error_log("[$timestamp] [$tenant] [$level] $message");
}

/**
 * Check if request is AJAX
 */
function isAjaxRequest(): bool
{
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

function getCurrentUrl(): string
{
    $scheme = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $scheme . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

function getBaseUrl(): string
{
    $scheme = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    return $scheme . '://' . $_SERVER['HTTP_HOST'];
}

function escapeHtml(string $text): string
{
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function escapeJs(string $text): string
{
    return json_encode($text, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
}

function formatDuration(int $minutes): string
{
    if ($minutes < 60) {
        return $minutes . ' min';
    }
    
    $hours = floor($minutes / 60);
    $remainingMinutes = $minutes % 60;
    
    if ($remainingMinutes === 0) {
        return $hours . 'h';
    }
    
    return $hours . 'h ' . $remainingMinutes . 'min';
}

function getTimeSlots(string $startTime, string $endTime, int $slotDuration = 15): array
{
    $slots = [];
    $current = strtotime($startTime);
    $end = strtotime($endTime);
    
    while ($current < $end) {
        $slots[] = date('H:i', $current);
        $current += $slotDuration * 60;
    }
    
    return $slots;
}

function isWorkingDay(string $date): bool
{
    $dayOfWeek = date('w', strtotime($date));
    return Config::isWorkingDay(strtolower(date('l', strtotime($date))));
}

function getWorkingHours(string $date): ?array
{
    $dayName = strtolower(date('l', strtotime($date)));
    return Config::getWorkingHours($dayName);
}

function isPastDate(string $date): bool
{
    return strtotime($date) < strtotime(date('Y-m-d'));
}

function isPastTime(string $date, string $time): bool
{
    return strtotime($date . ' ' . $time) < time();
}

function arrayToSelectOptions(array $array, string $valueKey = 'id', string $textKey = 'name', mixed $selected = null): string
{
    $options = '';
    
    foreach ($array as $item) {
        $value = $item[$valueKey];
        $text = $item[$textKey];
        $selectedAttr = ($value == $selected) ? ' selected' : '';
        
        $options .= "<option value=\"" . escapeHtml($value) . "\"$selectedAttr>" . escapeHtml($text) . "</option>";
    }
    
    return $options;
}

function paginate(int $total, int $perPage, int $currentPage = 1): array
{
    $totalPages = ceil($total / $perPage);
    $currentPage = max(1, min($currentPage, $totalPages));
    $offset = ($currentPage - 1) * $perPage;
    
    return [
        'total' => $total,
        'per_page' => $perPage,
        'current_page' => $currentPage,
        'total_pages' => $totalPages,
        'offset' => $offset,
        'has_prev' => $currentPage > 1,
        'has_next' => $currentPage < $totalPages,
        'prev_page' => $currentPage - 1,
        'next_page' => $currentPage + 1
    ];
}

function renderPagination(array $pagination, string $baseUrl): string
{
    if ($pagination['total_pages'] <= 1) {
        return '';
    }
    
    $html = '<div class="pagination">';
    
    if ($pagination['has_prev']) {
        $html .= '<a href="' . $baseUrl . '?page=' . $pagination['prev_page'] . '">&laquo; Previous</a>';
    }
    
    for ($i = 1; $i <= $pagination['total_pages']; $i++) {
        $active = $i === $pagination['current_page'] ? ' class="active"' : '';
        $html .= '<a href="' . $baseUrl . '?page=' . $i . '"' . $active . '>' . $i . '</a>';
    }
    
    if ($pagination['has_next']) {
        $html .= '<a href="' . $baseUrl . '?page=' . $pagination['next_page'] . '">Next &raquo;</a>';
    }
    
    $html .= '</div>';
    
    return $html;
}
