<?php
/**
 * Show Cross-Category Duplicates
 * Shows keys that appear in multiple categories
 */

require_once __DIR__ . '/../../shared/config.php';
require_once __DIR__ . '/../../shared/database.php';
require_once __DIR__ . '/../../shared/tenant_manager.php';

try {
    Config::init();
    TenantManager::init();
    $db = TenantManager::getDatabase();

    echo "🔍 Cross-Category Duplicate Keys\n";
    echo str_repeat("=", 50) . "\n";

    // Find keys used in multiple categories
    $duplicatesByKey = $db->fetchAll("
        SELECT key, COUNT(*) as count, GROUP_CONCAT(DISTINCT category) as categories
        FROM translations
        GROUP BY key
        HAVING COUNT(*) > 1
        ORDER BY count DESC, key
    ");

    if (empty($duplicatesByKey)) {
        echo "✅ No keys used in multiple categories\n";
    } else {
        echo "Found " . count($duplicatesByKey) . " keys used in multiple categories:\n\n";
        
        foreach ($duplicatesByKey as $dup) {
            echo "🔑 Key: '{$dup['key']}' ({$dup['count']} categories)\n";
            echo "   Categories: {$dup['categories']}\n";
            
            // Show details for each category
            $details = $db->fetchAll("
                SELECT category, value_el, value_en
                FROM translations
                WHERE key = :key
                ORDER BY category
            ", [':key' => $dup['key']]);
            
            foreach ($details as $detail) {
                echo "   [{$detail['category']}] EL: {$detail['value_el']}\n";
                echo "   [{$detail['category']}] EN: {$detail['value_en']}\n";
            }
            echo "\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
