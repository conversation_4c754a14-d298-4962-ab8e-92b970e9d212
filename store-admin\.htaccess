RewriteEngine On

# Default to index.php
DirectoryIndex index.php

# Handle store-admin specific routes
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# Allow access to assets
RewriteRule ^assets/ - [L]

# Security - block access to sensitive files
<Files "*.db">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>

<Files "config.php">
    Require all denied
</Files>

# Block access to hidden files
<Files ".*">
    Require all denied
</Files>
