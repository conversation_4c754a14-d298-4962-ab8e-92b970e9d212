<?php
/**
 * Security Setup Helper
 * Helps configure environment variables and validate security settings
 */

require_once __DIR__ . '/shared/environment.php';

echo "🔒 GK Radevou Security Setup\n";
echo "=============================\n\n";

// Load existing environment
Environment::load();

$envFile = __DIR__ . '/.env';
$envExists = file_exists($envFile);

if (!$envExists) {
    echo "⚠️  No .env file found. Creating from template...\n";
    if (file_exists(__DIR__ . '/.env.example')) {
        copy(__DIR__ . '/.env.example', $envFile);
        echo "✅ Created .env file from template\n\n";
    } else {
        echo "❌ .env.example template not found\n\n";
    }
}

// Check required environment variables
$requiredVars = [
    'SYSTEM_ADMIN_PASSWORD',
    'STRIPE_PUBLISHABLE_KEY',
    'STRIPE_SECRET_KEY',
    'PAYPAL_CLIENT_ID',
    'PAYPAL_CLIENT_SECRET'
];

$missing = Environment::validateRequired($requiredVars);

if (!empty($missing)) {
    echo "❌ Missing required environment variables:\n";
    foreach ($missing as $var) {
        echo "   - $var\n";
    }
    echo "\nPlease update your .env file with these values.\n\n";
} else {
    echo "✅ All required environment variables are set\n\n";
}

// Generate secure passwords if needed
echo "🔑 Password Generation\n";
echo "---------------------\n";

if (!Environment::has('SYSTEM_ADMIN_PASSWORD') || empty(Environment::get('SYSTEM_ADMIN_PASSWORD'))) {
    $systemPassword = Environment::generateSecurePassword(16);
    echo "Generated system admin password: $systemPassword\n";
    echo "Add this to your .env file: SYSTEM_ADMIN_PASSWORD=$systemPassword\n\n";
}

if (!Environment::has('STORE_ADMIN_PASSWORD') || empty(Environment::get('STORE_ADMIN_PASSWORD'))) {
    $storePassword = Environment::generateSecurePassword(16);
    echo "Generated store admin password: $storePassword\n";
    echo "Add this to your .env file: STORE_ADMIN_PASSWORD=$storePassword\n\n";
}

// Security checks
echo "🛡️  Security Validation\n";
echo "-----------------------\n";

// Check if using development mode in production
$devMode = Environment::get('DEVELOPMENT_MODE', 'true');
if ($devMode === 'true') {
    echo "⚠️  DEVELOPMENT_MODE is enabled. Set to 'false' in production\n";
} else {
    echo "✅ Development mode is disabled\n";
}

// Check payment configuration
$stripeKey = Environment::get('STRIPE_SECRET_KEY');
if (strpos($stripeKey, 'sk_test_') === 0) {
    echo "⚠️  Using Stripe test key. Use live key in production\n";
} elseif (strpos($stripeKey, 'sk_live_') === 0) {
    echo "✅ Using Stripe live key\n";
} elseif (!empty($stripeKey)) {
    echo "❓ Stripe key format not recognized\n";
}

// Check file permissions
if (file_exists($envFile)) {
    $perms = fileperms($envFile) & 0777;
    if ($perms !== 0600) {
        echo "⚠️  .env file permissions should be 600 (owner read/write only)\n";
        echo "   Run: chmod 600 .env\n";
    } else {
        echo "✅ .env file has secure permissions\n";
    }
}

echo "\n🚀 Setup Complete!\n";
echo "==================\n";
echo "Next steps:\n";
echo "1. Update .env file with your actual credentials\n";
echo "2. Set file permissions: chmod 600 .env\n";
echo "3. Test admin login with new credentials\n";
echo "4. Verify payment integrations work\n";
echo "5. Monitor logs for any issues\n\n";

echo "Security documentation: See SECURITY_FIXES.md for details\n";
