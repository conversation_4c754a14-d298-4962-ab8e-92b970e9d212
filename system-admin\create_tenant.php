<?php

/**
 * Tenant Creation Interface
 * Interface for creating new tenant instances with proper validation and dummy data support
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../shared/tenant_manager.php';
require_once __DIR__ . '/../shared/environment.php';

// Require system authentication
requireSystemAuth();

// Get current domain for display
$currentDomain = TenantManager::getBaseDomain();

$message = '';
$messageType = 'info';
$tenantResult = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $subdomain = trim($_POST['subdomain'] ?? '');
    $businessName = trim($_POST['business_name'] ?? '');
    $ownerName = trim($_POST['owner_name'] ?? '');
    $ownerEmail = trim($_POST['owner_email'] ?? '');
    $adminUsername = trim($_POST['admin_username'] ?? '');
    $adminPassword = trim($_POST['admin_password'] ?? '');
    $createDummyData = isset($_POST['create_dummy_data']);
    
    // Validation
    $errors = [];
    
    if (empty($subdomain)) {
        $errors[] = "Subdomain is required";
    } elseif (!preg_match('/^[a-z0-9\-]+$/', $subdomain)) {
        $errors[] = "Subdomain can only contain lowercase letters, numbers, and hyphens";
    } elseif (strlen($subdomain) < 3) {
        $errors[] = "Subdomain must be at least 3 characters long";
    }
    
    if (empty($businessName)) {
        $errors[] = "Business name is required";
    }
    
    if (!empty($ownerEmail) && !filter_var($ownerEmail, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    if (empty($adminUsername)) {
        $adminUsername = $subdomain;
    }
    
    if (empty($adminPassword)) {
        // Generate secure random password instead of predictable pattern
        $adminPassword = Environment::generateSecurePassword(16);
    }
    
    if (empty($errors)) {
        $tenantData = [
            'subdomain' => $subdomain,
            'business_name' => $businessName,
            'owner_name' => $ownerName,
            'owner_email' => $ownerEmail,
            'admin_username' => $adminUsername,
            'admin_password' => $adminPassword
        ];
        
        $result = TenantManager::createTenant($tenantData, $createDummyData);

        if ($result['success']) {
            $tenantResult = $result;
            $message = "Tenant created successfully!";
            $messageType = 'success';

            if ($createDummyData) {
                $message .= " Added complete business data with translations, categories, services, employees, customers, and bookings.";
            }
        } else {
            $message = "Failed to create tenant: " . $result['error'];
            $messageType = 'error';
        }
    } else {
        $message = implode('<br>', $errors);
        $messageType = 'error';
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= SYSTEM_NAME ?> - Create Tenant</title>
    <link rel="stylesheet" href="assets/system.css">
</head>
<body>
    <div class="layout">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1><?= SYSTEM_NAME ?></h1>
                <button class="sidebar-toggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="index.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        <span class="nav-text">Dashboard</span>
                    </a>
                    <a href="tenant_manager.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                        <span class="nav-text">Tenant Manager</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Management</div>
                    <a href="create_tenant.php" class="nav-link active">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="12" y1="8" x2="12" y2="16"></line>
                            <line x1="8" y1="12" x2="16" y2="12"></line>
                        </svg>
                        <span class="nav-text">Create Tenant</span>
                    </a>
                    <a href="dummy_data.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        <span class="nav-text">Dummy Data</span>
                    </a>
                    <a href="clean_db.php" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M4 7h16"></path>
                            <path d="M10 11v6"></path>
                            <path d="M14 11v6"></path>
                            <path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2l1-12"></path>
                            <path d="M9 7V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v3"></path>
                        </svg>
                        <span class="nav-text">Database Tools</span>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <a href="?logout=1" class="nav-link">
                        <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <header class="header">
                <div class="header-left">
                    <h1 class="header-title">Create Tenant</h1>
                    <div class="breadcrumb">
                        <span>System</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Management</span>
                        <span class="breadcrumb-separator">/</span>
                        <span>Create Tenant</span>
                    </div>
                </div>
                <div class="header-right">
                    <a href="tenant_manager.php" class="btn btn-secondary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M19 12H5"></path>
                            <path d="M12 19l-7-7 7-7"></path>
                        </svg>
                        Back to Manager
                    </a>
                </div>
            </header>

            <main class="main">
                <div class="container">

            <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= $message ?>
                </div>
            <?php endif; ?>

            <?php if ($tenantResult): ?>
                <div class="success-info">
                    <h2>✅ Tenant Created Successfully!</h2>
                    <div class="tenant-details">
                        <h3>Tenant Information</h3>
                        <table>
                            <tr>
                                <td><strong>Tenant ID:</strong></td>
                                <td><?= htmlspecialchars($tenantResult['tenant_id']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Subdomain:</strong></td>
                                <td>
                                    <a href="https://<?= htmlspecialchars($subdomain) ?>.skrtz.gr" target="_blank">
                                        <?= htmlspecialchars($subdomain) ?>.skrtz.gr
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Admin Username:</strong></td>
                                <td><?= htmlspecialchars($tenantResult['admin_username']) ?></td>
                            </tr>
                            <tr>
                                <td><strong>Admin Password:</strong></td>
                                <td><?= htmlspecialchars($tenantResult['admin_password']) ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="quick-actions">
                        <a href="https://<?= htmlspecialchars($subdomain) ?>.skrtz.gr/store-admin" target="_blank" class="btn btn-primary">Open Admin Panel</a>
                        <a href="https://<?= htmlspecialchars($subdomain) ?>.skrtz.gr" target="_blank" class="btn btn-secondary">View Site</a>
                        <a href="tenant_manager.php" class="btn btn-info">Manage Tenants</a>
                    </div>
                </div>
            <?php endif; ?>

            <div class="form-container">
                <form method="post" class="tenant-form">
                    <div class="form-section">
                        <h2>Basic Information</h2>
                        
                        <div class="form-group">
                            <label for="subdomain">Subdomain *</label>
                            <div class="input-group">
                                <input type="text" id="subdomain" name="subdomain" required 
                                       pattern="[a-z0-9\-]+" 
                                       value="<?= htmlspecialchars($_POST['subdomain'] ?? '') ?>"
                                       placeholder="e.g., mybusiness">
                                <span class="input-group-text">.<?= htmlspecialchars($currentDomain) ?></span>
                            </div>
                            <small class="form-text">Only lowercase letters, numbers, and hyphens allowed</small>
                        </div>

                        <div class="form-group">
                            <label for="business_name">Business Name *</label>
                            <input type="text" id="business_name" name="business_name" required 
                                   value="<?= htmlspecialchars($_POST['business_name'] ?? '') ?>"
                                   placeholder="e.g., My Awesome Business">
                        </div>

                        <div class="form-group">
                            <label for="owner_name">Owner Name</label>
                            <input type="text" id="owner_name" name="owner_name" 
                                   value="<?= htmlspecialchars($_POST['owner_name'] ?? '') ?>"
                                   placeholder="e.g., John Doe">
                        </div>

                        <div class="form-group">
                            <label for="owner_email">Owner Email</label>
                            <input type="email" id="owner_email" name="owner_email" 
                                   value="<?= htmlspecialchars($_POST['owner_email'] ?? '') ?>"
                                   placeholder="e.g., <EMAIL>">
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Admin Account</h2>
                        
                        <div class="form-group">
                            <label for="admin_username">Admin Username</label>
                            <input type="text" id="admin_username" name="admin_username" 
                                   value="<?= htmlspecialchars($_POST['admin_username'] ?? '') ?>"
                                   placeholder="Will use subdomain if empty">
                            <small class="form-text">Leave empty to use subdomain as username</small>
                        </div>

                        <div class="form-group">
                            <label for="admin_password">Admin Password</label>
                            <input type="text" id="admin_password" name="admin_password" 
                                   value="<?= htmlspecialchars($_POST['admin_password'] ?? '') ?>"
                                   placeholder="Will generate if empty">
                            <small class="form-text">Leave empty to auto-generate secure random password</small>
                        </div>
                    </div>

                    <div class="form-section">
                        <h2>Options</h2>
                        
                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" name="create_dummy_data"
                                       <?= isset($_POST['create_dummy_data']) ? 'checked' : '' ?> checked>
                                Create complete translation system
                            </label>
                            <small class="form-text">Adds complete business setup: 362 translations, service categories, employees with working hours, services, sample customers, and bookings</small>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Create Tenant</button>
                        <a href="tenant_manager.php" class="btn btn-secondary">Cancel</a>
                    </div>
                </form>
            </div>

                    <div class="help-section">
                        <h2>Help</h2>
                        <div class="help-content">
                            <h3>Creating a Tenant</h3>
                            <p>A tenant is a separate instance of the booking system with its own database, settings, and data. Each tenant gets:</p>
                            <ul>
                                <li>Unique subdomain (e.g., business.<?= htmlspecialchars($currentDomain) ?>)</li>
                                <li>Separate database and data</li>
                                <li>Independent admin account</li>
                                <li>Own settings and configuration</li>
                            </ul>

                            <h3>Subdomain Rules</h3>
                            <ul>
                                <li>Must be at least 3 characters long</li>
                                <li>Can only contain lowercase letters, numbers, and hyphens</li>
                                <li>Cannot start or end with a hyphen</li>
                                <li>Must be unique across all tenants</li>
                            </ul>

                            <h3>Default Admin Account</h3>
                            <p>If you don't specify admin credentials, the system will create:</p>
                            <ul>
                                <li>Username: Same as subdomain</li>
                                <li>Password: Secure auto-generated password</li>
                                <li>Password: Subdomain + '123'</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay"></div>

    <script src="assets/system.js"></script>
    <script>
        // Auto-generate admin credentials based on subdomain
        document.getElementById('subdomain').addEventListener('input', function() {
            const subdomain = this.value.toLowerCase();
            const adminUsername = document.getElementById('admin_username');
            const adminPassword = document.getElementById('admin_password');
            
            if (!adminUsername.value) {
                adminUsername.placeholder = subdomain || 'admin';
            }
            
            if (!adminPassword.value) {
                adminPassword.placeholder = 'Will generate secure password';
            }
        });

        // Validate subdomain format
        document.getElementById('subdomain').addEventListener('blur', function() {
            const subdomain = this.value.toLowerCase();
            const pattern = /^[a-z0-9\-]+$/;
            
            if (subdomain && !pattern.test(subdomain)) {
                this.setCustomValidity('Subdomain can only contain lowercase letters, numbers, and hyphens');
            } else if (subdomain && subdomain.length < 3) {
                this.setCustomValidity('Subdomain must be at least 3 characters long');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
