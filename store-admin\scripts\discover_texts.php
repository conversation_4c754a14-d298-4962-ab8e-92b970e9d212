<?php
/**
 * Auto-Discovery Script
 * Scans codebase for t() calls and creates database entries
 */

require_once __DIR__ . '/../../shared/tenant_manager.php';
require_once __DIR__ . '/../../shared/translation.php';

class TextDiscovery
{
    private array $discoveredTexts = [];
    private array $scanPaths = [
        __DIR__ . '/../../client',
        __DIR__ . '/../../api',
        __DIR__ . '/../../shared',
        __DIR__ . '/../../store-admin/views',
        __DIR__ . '/../../store-admin/controllers'
    ];
    
    public function scan(): void
    {
        echo "Scanning for translation keys...\n";
        
        foreach ($this->scanPaths as $path) {
            $this->scanDirectory($path);
        }
        
        echo "Found " . count($this->discoveredTexts) . " translation keys\n";
        $this->saveToDatabase();
    }
    
    private function scanDirectory(string $dir): void
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && in_array($file->getExtension(), ['php', 'js'])) {
                $this->scanFile($file->getPathname());
            }
        }
    }
    
    private function scanFile(string $filePath): void
    {
        $content = file_get_contents($filePath);

        echo "Scanning file: " . basename($filePath) . "\n";

        // Multiple patterns to catch different t() call variations
        $patterns = [
            // t('key', 'default', 'category') - basic pattern
            '/\bt\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)/',
            // Translation::t('key', 'default', 'category')
            '/Translation::t\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)/',
            // ${t('key', 'default')} in JavaScript template literals
            '/\$\{t\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)\}/',
            // window.t('key', 'default') in JavaScript
            '/window\.t\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)/',
            // Simple t() calls without window prefix
            '/(?<![a-zA-Z_])t\s*\(\s*[\'"]([^\'\"]+)[\'"]\s*,\s*[\'"]([^\'\"]*)[\'"](?:\s*,\s*[\'"]([^\'\"]*)[\'"])?\s*\)/'
        ];

        $foundInFile = 0;
        foreach ($patterns as $patternIndex => $pattern) {
            if (preg_match_all($pattern, $content, $matches, PREG_SET_ORDER)) {
                foreach ($matches as $match) {
                    $key = $match[1];
                    $defaultValue = $match[2] ?? '';
                    $category = $match[3] ?? 'client';

                    if (!isset($this->discoveredTexts[$key])) {
                        $this->discoveredTexts[$key] = [
                            'default_value' => $defaultValue,
                            'category' => $category,
                            'file' => $filePath
                        ];
                        $foundInFile++;
                    }
                }
            }
        }

        echo "  Found {$foundInFile} translation keys\n";
    }
    
    private function saveToDatabase(): void
    {
        try {
            $db = TenantManager::getDatabase();
            $timestamp = date('Y-m-d H:i:s');
            $saved = 0;
            
            foreach ($this->discoveredTexts as $key => $data) {
                // Check if already exists
                $existing = $db->fetchRow("
                    SELECT id FROM texts 
                    WHERE text_key = :key AND language = 'el'
                ", [':key' => $key]);
                
                if (!$existing) {
                    // Create Greek entry
                    $db->query("
                        INSERT INTO texts (id, text_key, text_value, language, category, auto_discovered, created_at, updated_at) 
                        VALUES (:id, :key, :value, 'el', :category, 1, :created_at, :updated_at)
                    ", [
                        ':id' => $this->generateId(),
                        ':key' => $key,
                        ':value' => $data['default_value'],
                        ':category' => $data['category'],
                        ':created_at' => $timestamp,
                        ':updated_at' => $timestamp
                    ]);
                    
                    // Create English entry (empty)
                    $db->query("
                        INSERT INTO texts (id, text_key, text_value, language, category, auto_discovered, created_at, updated_at) 
                        VALUES (:id, :key, '', 'en', :category, 1, :created_at, :updated_at)
                    ", [
                        ':id' => $this->generateId(),
                        ':key' => $key,
                        ':category' => $data['category'],
                        ':created_at' => $timestamp,
                        ':updated_at' => $timestamp
                    ]);
                    
                    $saved++;
                }
            }
            
            echo "Saved {$saved} new translation keys to database\n";
            
        } catch (Exception $e) {
            echo "Error saving to database: " . $e->getMessage() . "\n";
        }
    }
    
    private function generateId(): string
    {
        return 'TXT' . strtoupper(substr(uniqid(), -8));
    }
    
    public function listDiscovered(): void
    {
        echo "\nDiscovered translation keys:\n";
        echo str_repeat("-", 80) . "\n";
        
        foreach ($this->discoveredTexts as $key => $data) {
            echo sprintf("%-30s | %-20s | %s\n", 
                $key, 
                $data['category'], 
                $data['default_value']
            );
        }
    }
}

// Run discovery
try {
    // Initialize tenant context (use first available tenant for testing)
    TenantManager::init();

    $discovery = new TextDiscovery();
    $discovery->scan();
    
    if (isset($argv[1]) && $argv[1] === '--list') {
        $discovery->listDiscovered();
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
