<?php
/**
 * Customers Controller
 * Handles customer CRUD operations
 */

/**
 * Handle customer form submission
 */
function handleCustomersForm(array $data, Database $db): array
{
    $action = $data['action'] ?? '';
    $id = $data['id'] ?? '';
    
    if ($action === 'save') {
        // Validate CSRF token
        if (!Application::verifyCsrfToken($data['csrf_token'] ?? '')) {
            return ['success' => false, 'error' => 'Invalid request token'];
        }
        
        // Validate required fields
        if (empty(trim($data['name'] ?? ''))) {
            return ['success' => false, 'error' => 'Customer name is required'];
        }
        
        // Validate email if provided
        if (!empty($data['email']) && !Application::validateEmail($data['email'])) {
            return ['success' => false, 'error' => 'Invalid email address'];
        }
        
        // Validate phone if provided
        if (!empty($data['phone']) && !Application::validatePhone($data['phone'])) {
            return ['success' => false, 'error' => 'Invalid phone number'];
        }
        
        $customerData = [
            ':name' => Application::sanitize($data['name']),
            ':email' => Application::sanitize($data['email']),
            ':phone' => Application::sanitize($data['phone']),
            ':language' => $data['language'] ?? 'el',
            ':notes' => Application::sanitize($data['notes']),
            ':updated_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            if ($id) {
                // Update existing customer
                $customerData[':id'] = $id;
                $sql = "UPDATE customers SET 
                        name = :name, 
                        email = :email, 
                        phone = :phone, 
                        language = :language, 
                        notes = :notes, 
                        updated_at = :updated_at 
                        WHERE id = :id";
            } else {
                // Check for duplicate email if provided
                if (!empty($data['email'])) {
                    $existing = $db->fetchRow("SELECT id FROM customers WHERE email = :email", [':email' => $data['email']]);
                    if ($existing) {
                        return ['success' => false, 'error' => 'A customer with this email already exists'];
                    }
                }
                
                // Create new customer
                $customerData[':id'] = Application::generateId('CUS');
                $customerData[':created_at'] = date('Y-m-d H:i:s');
                $sql = "INSERT INTO customers (id, name, email, phone, language, notes, created_at, updated_at) 
                        VALUES (:id, :name, :email, :phone, :language, :notes, :created_at, :updated_at)";
            }
            
            $result = $db->query($sql, $customerData);
            
            if ($result !== false) {
                return [
                    'success' => true,
                    'redirect' => '/store-admin/?page=customers',
                    'message' => $id ? 'Customer updated successfully' : 'Customer created successfully'
                ];
            } else {
                return ['success' => false, 'error' => 'Failed to save customer'];
            }
            
        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Database error: ' . $e->getMessage()];
        }
    }
    
    return ['success' => false, 'error' => 'Invalid action'];
}
