<?php

/**
 * Application Class
 * Bridge between store-admin and the shared architecture
 */

class Application
{
    private static $initialized = false;
    private static $db = null;
    private static $config = null;

    /**
     * Initialize the application
     */
    public static function init()
    {
        if (self::$initialized) {
            return;
        }

        // Include shared dependencies (order matters!)
        require_once __DIR__ . '/../../shared/config.php';
        require_once __DIR__ . '/../../shared/database.php';
        require_once __DIR__ . '/../../shared/tenant_manager.php';

        // Initialize systems first
        Config::init();
        TenantManager::init();

        // Include functions.php after initialization (it depends on Config and TenantManager)
        require_once __DIR__ . '/../../shared/functions.php';

        self::$initialized = true;
    }

    /**
     * Get database instance
     */
    public static function getDb()
    {
        if (self::$db === null) {
            self::$db = TenantManager::getDatabase();
        }
        return self::$db;
    }

    /**
     * Get configuration setting
     */
    public static function getSetting($key, $default = null)
    {
        $db = self::getDb();
        $setting = $db->fetchRow(
            "SELECT value FROM settings WHERE key = :key",
            [':key' => $key]
        );
        
        return $setting ? $setting['value'] : $default;
    }

    /**
     * Set configuration setting
     */
    public static function setSetting($key, $value)
    {
        $db = self::getDb();

        // Use INSERT OR REPLACE since key is the primary key
        $db->query(
            "INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (:key, :value, :updated_at)",
            [
                ':key' => $key,
                ':value' => $value,
                ':updated_at' => date('Y-m-d H:i:s')
            ]
        );
    }

    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken()
    {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    /**
     * Verify CSRF token
     */
    public static function verifyCsrfToken($token)
    {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Set flash message
     */
    public static function setFlashMessage($message, $type = 'info')
    {
        $_SESSION['flash_message'] = [
            'message' => $message,
            'type' => $type
        ];
    }

    /**
     * Get and clear flash message
     */
    public static function getFlashMessage()
    {
        $message = $_SESSION['flash_message'] ?? ['message' => '', 'type' => ''];
        unset($_SESSION['flash_message']);
        return $message;
    }

    /**
     * Redirect with flash message
     */
    public static function redirect($url, $message = '', $type = 'info')
    {
        if ($message) {
            self::setFlashMessage($message, $type);
        }
        header("Location: $url");
        exit;
    }

    /**
     * Get current tenant info
     */
    public static function getCurrentTenant()
    {
        return TenantManager::getCurrentTenant();
    }

    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated()
    {
        return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
    }

    /**
     * Require authentication
     */
    public static function requireAuth()
    {
        if (!self::isAuthenticated()) {
            header('Location: /store-admin/login.php');
            exit;
        }
    }

    /**
     * Log activity
     */
    public static function logActivity($message, $level = 'info')
    {
        if (function_exists('logActivity')) {
            logActivity($message, $level);
        }
    }

    /**
     * Get current user
     */
    public static function getCurrentUser()
    {
        return $_SESSION['admin_username'] ?? 'admin';
    }

    /**
     * Format date
     */
    public static function formatDate($date, $format = 'Y-m-d H:i:s')
    {
        if (is_string($date)) {
            $date = new DateTime($date);
        }
        return $date->format($format);
    }

    /**
     * Sanitize input
     */
    public static function sanitize($input)
    {
        if (is_array($input)) {
            return array_map([self::class, 'sanitize'], $input);
        }
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Validate email
     */
    public static function validateEmail($email)
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Validate phone number
     */
    public static function validatePhone($phone)
    {
        // Basic phone validation - remove spaces, dashes, parentheses
        $cleanPhone = preg_replace('/[\s\-\(\)]/', '', $phone);
        // Check if it contains only digits and optional + at the beginning
        return preg_match('/^\+?[0-9]{8,15}$/', $cleanPhone);
    }

    /**
     * Generate random string
     */
    public static function generateRandomString($length = 10)
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Generate ID with prefix
     */
    public static function generateId($prefix = '')
    {
        $id = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
        return $prefix ? $prefix . $id : $id;
    }
}
