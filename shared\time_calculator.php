<?php

/**
 * Enhanced Time Calculator
 * Handles all time calculations with proper DateTime objects and timezone support
 */

class TimeCalculator 
{
    private string $timezone;
    private DateTimeZone $timezoneObj;
    
    public function __construct(string $timezone = 'Europe/Athens') 
    {
        $this->timezone = $timezone;
        $this->timezoneObj = new DateTimeZone($timezone);
    }
    
    /**
     * Calculate all time boundaries for an appointment including prep, cleanup, and buffer times
     */
    public function calculateAppointmentTimes(string $date, string $time, array $service, ?int $globalBufferTime = null): array
    {
        // Parse appointment time safely
        $appointmentStart = DateTime::createFromFormat('Y-m-d H:i', "$date $time", $this->timezoneObj);
        if (!$appointmentStart) {
            throw new InvalidArgumentException("Invalid date/time format: $date $time");
        }
        
        // Get service durations (all in minutes)
        $duration = (int)($service['duration'] ?? 60);
        $prepTime = (int)($service['preparation_time'] ?? 0);
        $cleanupTime = (int)($service['cleanup_time'] ?? 0);
        // Use global buffer time, then fallback to config
        $bufferTime = (int)($globalBufferTime ?? Config::BUFFER_TIME_MINUTES);
        
        // Calculate appointment end time
        $appointmentEnd = clone $appointmentStart;
        $appointmentEnd->add(new DateInterval("PT{$duration}M"));
        
        // Calculate preparation start time (before appointment)
        $prepStart = clone $appointmentStart;
        if ($prepTime > 0) {
            $prepStart->sub(new DateInterval("PT{$prepTime}M"));
        }
        
        // Calculate cleanup end time (after appointment)
        $cleanupEnd = clone $appointmentEnd;
        if ($cleanupTime > 0) {
            $cleanupEnd->add(new DateInterval("PT{$cleanupTime}M"));
        }
        
        // Calculate buffer zones (around everything)
        $bufferStart = clone $prepStart;
        if ($bufferTime > 0) {
            $bufferStart->sub(new DateInterval("PT{$bufferTime}M"));
        }
        
        $bufferEnd = clone $cleanupEnd;
        if ($bufferTime > 0) {
            $bufferEnd->add(new DateInterval("PT{$bufferTime}M"));
        }
        
        return [
            'appointment_start' => $appointmentStart,
            'appointment_end' => $appointmentEnd,
            'prep_start' => $prepStart,
            'cleanup_end' => $cleanupEnd,
            'buffer_start' => $bufferStart,
            'buffer_end' => $bufferEnd,
            'total_duration_minutes' => $this->getMinutesDifference($bufferStart, $bufferEnd),
            'service_duration' => $duration,
            'preparation_time' => $prepTime,
            'cleanup_time' => $cleanupTime,
            'buffer_time' => $bufferTime
        ];
    }
    
    /**
     * Check if two time periods overlap
     */
    public function checkTimeOverlap(DateTime $start1, DateTime $end1, DateTime $start2, DateTime $end2): bool
    {
        // Two periods overlap if: start1 < end2 AND end1 > start2
        return $start1 < $end2 && $end1 > $start2;
    }
    
    /**
     * Get the difference between two DateTime objects in minutes
     */
    public function getMinutesDifference(DateTime $start, DateTime $end): int
    {
        $diff = $start->diff($end);
        return ($diff->days * 24 * 60) + ($diff->h * 60) + $diff->i;
    }
    
    /**
     * Parse existing reservation times safely
     */
    public function parseReservationTimes(array $reservation): array
    {
        $date = $reservation['date'];
        $startTime = $reservation['start_time'];
        $endTime = $reservation['end_time'] ?? null;
        
        // Try parsing with seconds first, then without seconds
        $start = DateTime::createFromFormat('Y-m-d H:i:s', "$date $startTime", $this->timezoneObj);
        if (!$start) {
            $start = DateTime::createFromFormat('Y-m-d H:i', "$date $startTime", $this->timezoneObj);
        }
        if (!$start) {
            throw new InvalidArgumentException("Invalid reservation start time: $date $startTime");
        }

        if ($endTime) {
            $end = DateTime::createFromFormat('Y-m-d H:i:s', "$date $endTime", $this->timezoneObj);
            if (!$end) {
                $end = DateTime::createFromFormat('Y-m-d H:i', "$date $endTime", $this->timezoneObj);
            }
            if (!$end) {
                throw new InvalidArgumentException("Invalid reservation end time: $date $endTime");
            }
        } else {
            // Calculate end time from duration if not provided
            $duration = (int)($reservation['duration'] ?? 60);
            $end = clone $start;
            $end->add(new DateInterval("PT{$duration}M"));
        }
        
        return [
            'start' => $start,
            'end' => $end,
            'duration_minutes' => $this->getMinutesDifference($start, $end)
        ];
    }
    
    /**
     * Generate time slots for a given time range
     */
    public function generateTimeSlots(string $startTime, string $endTime, int $slotDuration = 15): array
    {
        $slots = [];
        
        // Create DateTime objects for today (date doesn't matter for time generation)
        $today = date('Y-m-d');
        $current = DateTime::createFromFormat('Y-m-d H:i', "$today $startTime", $this->timezoneObj);
        $end = DateTime::createFromFormat('Y-m-d H:i', "$today $endTime", $this->timezoneObj);
        
        if (!$current || !$end) {
            throw new InvalidArgumentException("Invalid time format: $startTime - $endTime");
        }
        
        while ($current < $end) {
            $slots[] = $current->format('H:i');
            $current->add(new DateInterval("PT{$slotDuration}M"));
        }
        
        return $slots;
    }
    
    /**
     * Check if a time is within business hours
     */
    public function isWithinBusinessHours(DateTime $dateTime, array $workingHours): bool
    {
        $dayOfWeek = (int)$dateTime->format('w'); // 0 = Sunday, 1 = Monday, etc.
        
        if (!isset($workingHours[$dayOfWeek]) || !$workingHours[$dayOfWeek]['is_active']) {
            return false;
        }
        
        $timeStr = $dateTime->format('H:i');
        $startTime = $workingHours[$dayOfWeek]['start_time'];
        $endTime = $workingHours[$dayOfWeek]['end_time'];
        
        return $timeStr >= $startTime && $timeStr <= $endTime;
    }
    
    /**
     * Format DateTime for database storage
     */
    public function formatForDatabase(DateTime $dateTime): array
    {
        return [
            'date' => $dateTime->format('Y-m-d'),
            'time' => $dateTime->format('H:i'),
            'datetime' => $dateTime->format('Y-m-d H:i:s')
        ];
    }
    
    /**
     * Create DateTime from database values
     */
    public function createFromDatabase(string $date, string $time): DateTime
    {
        $dateTime = DateTime::createFromFormat('Y-m-d H:i', "$date $time", $this->timezoneObj);
        if (!$dateTime) {
            throw new InvalidArgumentException("Invalid database date/time: $date $time");
        }
        return $dateTime;
    }
    
    /**
     * Check if a date/time is in the past
     */
    public function isPastDateTime(string $date, string $time): bool
    {
        $dateTime = $this->createFromDatabase($date, $time);
        $now = new DateTime('now', $this->timezoneObj);
        
        return $dateTime <= $now;
    }
    
    /**
     * Get current date and time in the configured timezone
     */
    public function getCurrentDateTime(): DateTime
    {
        return new DateTime('now', $this->timezoneObj);
    }
    
    /**
     * Add buffer time to existing reservation times for conflict checking
     */
    public function addBufferToReservation(array $reservationTimes, int $bufferMinutes): array
    {
        $bufferedStart = clone $reservationTimes['start'];
        $bufferedEnd = clone $reservationTimes['end'];
        
        if ($bufferMinutes > 0) {
            $bufferedStart->sub(new DateInterval("PT{$bufferMinutes}M"));
            $bufferedEnd->add(new DateInterval("PT{$bufferMinutes}M"));
        }
        
        return [
            'start' => $bufferedStart,
            'end' => $bufferedEnd,
            'original_start' => $reservationTimes['start'],
            'original_end' => $reservationTimes['end']
        ];
    }
}
