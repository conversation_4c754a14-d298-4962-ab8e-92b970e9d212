<?php
/**
 * Step 4: Time Selection View Template
 */
?>

<div class="step-title"><?= t('select_time', 'Select Time', 'client') ?></div>
<div class="step-subtitle"><?= t('select_time_subtitle', 'Choose your preferred time slot', 'client') ?></div>

<div class="time-slots-container">
    <div class="time-slots-header">
        <div class="selected-date" id="selectedDate"></div>
        <div class="selected-service" id="selectedService"></div>
    </div>
    <div class="time-slots-grid" id="timeSlotsGrid">
        <!-- Time slots will be loaded dynamically -->
    </div>
</div>

<script>
// Load time slots when this step is shown
document.addEventListener('DOMContentLoaded', function() {
    if (typeof BookingSystem !== 'undefined' && BookingSystem.instance) {
        BookingSystem.instance.loadTimeSlots();
    }
});
</script>
