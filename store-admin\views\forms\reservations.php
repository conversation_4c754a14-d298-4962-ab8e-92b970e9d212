<?php

/**
 * Reservation Form
 * Add/Edit reservation form
 */

$isEdit = !empty($item);
$reservation = $item;

// Get customers
$customers = $db->fetchAll("SELECT * FROM customers ORDER BY name ASC");

// Get services
$services = $db->fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name ASC");

// Get employees
$employees = $db->fetchAll("SELECT * FROM employees WHERE is_active = 1 ORDER BY name ASC");

// Pre-fill customer if provided in URL
$preSelectedCustomer = $_GET['customer_id'] ?? '';
?>

<form method="POST" class="modal-form">
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="csrf_token" value="<?php echo Application::generateCsrfToken(); ?>">
    <?php if ($isEdit): ?>
        <input type="hidden" name="id" value="<?php echo $reservation['id']; ?>">
    <?php endif; ?>

    <!-- Customer Selection -->
    <div class="form-group">
        <label for="customer_id">Customer *</label>
        <select id="customer_id" name="customer_id" required data-searchable data-placeholder="Search customers..." data-max-visible="5">
            <option value="">Select Customer</option>
            <?php foreach ($customers as $customer): ?>
                <option value="<?php echo $customer['id']; ?>"
                    <?php echo ($isEdit && $reservation['customer_id'] === $customer['id']) ||
                        $preSelectedCustomer === $customer['id'] ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($customer['name']); ?>
                    <?php if ($customer['email']): ?>
                        (<?php echo htmlspecialchars($customer['email']); ?>)
                    <?php endif; ?>
                </option>
            <?php endforeach; ?>
        </select>
        <small>
            <a href="/store-admin/?page=customers&action=add" target="_blank">Add new customer</a>
        </small>
    </div>

    <!-- Service Selection -->
    <div class="form-group">
        <label for="service_id">Service *</label>
        <select id="service_id" name="service_id" required onchange="updateServiceInfo()" data-searchable data-placeholder="Search services..." data-max-visible="5">
            <option value="">Select Service</option>
            <?php foreach ($services as $service): ?>
                <option value="<?php echo $service['id']; ?>"
                    data-duration="<?php echo $service['duration']; ?>"
                    data-price="<?php echo $service['price']; ?>"
                    <?php echo ($isEdit && $reservation['service_id'] === $service['id']) ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($service['name']); ?>
                    (<?php echo $service['duration']; ?> min - €<?php echo number_format($service['price'], 2); ?>)
                </option>
            <?php endforeach; ?>
        </select>
    </div>

    <!-- Date and Time -->
    <div class="form-row">
        <div class="form-group">
            <label for="date">Date *</label>
            <input type="date" id="date" name="date" required
                min="<?php echo date('Y-m-d'); ?>"
                value="<?php echo $isEdit ? $reservation['date'] : ($_GET['date'] ?? ''); ?>"
                onchange="updateAvailableSlots()">
        </div>
        <div class="form-group">
            <label for="start_time">Start Time *</label>
            <input type="time" id="start_time" name="start_time" required
                value="<?php echo $isEdit ? $reservation['start_time'] : ''; ?>">
        </div>
    </div>

    <!-- Available Time Slots -->
    <div class="form-group">
        <label>Available Time Slots</label>
        <div id="available-slots" class="time-slots">
            <div class="loading">Select service and date to see available slots</div>
        </div>
    </div>

    <!-- Employee Selection -->
    <div class="form-group">
        <label for="employee_id">Employee</label>
        <select id="employee_id" name="employee_id" data-searchable data-placeholder="Search employees..." data-max-visible="5">
            <option value="">Auto-assign (First Available)</option>
            <?php foreach ($employees as $employee): ?>
                <option value="<?php echo $employee['id']; ?>"
                    <?php echo ($isEdit && $reservation['employee_id'] === $employee['id']) ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($employee['name']); ?>
                    <?php if ($employee['position']): ?>
                        - <?php echo htmlspecialchars($employee['position']); ?>
                    <?php endif; ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>

    <!-- Price and Status -->
    <div class="form-row">
        <div class="form-group">
            <label for="price">Price (€)</label>
            <input type="number" id="price" name="price" step="0.01" min="0"
                value="<?php echo $isEdit ? $reservation['price'] : ''; ?>">
            <small>Leave empty to use service default price</small>
        </div>
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="pending" <?php echo (!$isEdit || $reservation['status'] === 'pending') ? 'selected' : ''; ?>>
                    Pending
                </option>
                <option value="confirmed" <?php echo ($isEdit && $reservation['status'] === 'confirmed') ? 'selected' : ''; ?>>
                    Confirmed
                </option>
                <option value="completed" <?php echo ($isEdit && $reservation['status'] === 'completed') ? 'selected' : ''; ?>>
                    Completed
                </option>
                <option value="cancelled" <?php echo ($isEdit && $reservation['status'] === 'cancelled') ? 'selected' : ''; ?>>
                    Cancelled
                </option>
            </select>
        </div>
    </div>

    <!-- Notes -->
    <div class="form-group">
        <label for="notes">Notes</label>
        <textarea id="notes" name="notes" rows="3"
            placeholder="Any special notes for this reservation..."><?php echo $isEdit ? htmlspecialchars($reservation['notes']) : ''; ?></textarea>
    </div>

    <?php if ($isEdit): ?>
        <!-- Reservation Details -->
        <div class="reservation-details">
            <h4>Reservation Details</h4>
            <div class="detail-grid">
                <div class="detail-item">
                    <strong>Created:</strong>
                    <?php echo date('M j, Y g:i A', strtotime($reservation['created_at'])); ?>
                </div>
                <?php if ($reservation['updated_at'] !== $reservation['created_at']): ?>
                    <div class="detail-item">
                        <strong>Last Updated:</strong>
                        <?php echo date('M j, Y g:i A', strtotime($reservation['updated_at'])); ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    <?php endif; ?>

    <div class="form-actions">
        <button type="submit" class="btn btn-primary">
            <?php echo $isEdit ? 'Update' : 'Create'; ?> Reservation
        </button>
        <button type="button" class="btn btn-secondary" onclick="closeModal()">
            Cancel
        </button>
    </div>
</form>

<script>
    function updateServiceInfo() {
        const serviceSelect = document.getElementById('service_id');
        const priceInput = document.getElementById('price');
        const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];

        if (selectedOption.value) {
            const price = selectedOption.dataset.price;
            if (priceInput.value === '' || priceInput.value === '0') {
                priceInput.value = price;
            }
            updateAvailableSlots();
        }
    }

    function updateAvailableSlots() {
        const dateInput = document.getElementById('date');
        const serviceSelect = document.getElementById('service_id');
        const employeeSelect = document.getElementById('employee_id');
        const slotsContainer = document.getElementById('available-slots');

        const date = dateInput.value;
        const serviceId = serviceSelect.value;
        const employeeId = employeeSelect.value;

        if (!date || !serviceId) {
            slotsContainer.innerHTML = '<div class="loading">Select service and date to see available slots</div>';
            return;
        }

        slotsContainer.innerHTML = '<div class="loading">Loading available slots...</div>';

        fetch('/store-admin/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: `ajax=1&action=get_available_slots&date=${date}&service_id=${serviceId}&employee_id=${employeeId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.slots.length === 0) {
                        slotsContainer.innerHTML = '<div class="no-slots">No available slots for this date</div>';
                    } else {
                        slotsContainer.innerHTML = data.slots.map(slot =>
                            `<button type="button" class="slot-btn" onclick="selectSlot('${slot}')">${slot}</button>`
                        ).join('');
                    }
                } else {
                    slotsContainer.innerHTML = '<div class="error">Error loading slots</div>';
                }
            })
            .catch(error => {
                slotsContainer.innerHTML = '<div class="error">Error loading slots</div>';
            });
    }

    function selectSlot(time) {
        const timeInput = document.getElementById('start_time');
        const slotButtons = document.querySelectorAll('.slot-btn');

        timeInput.value = time;

        slotButtons.forEach(btn => btn.classList.remove('selected'));
        event.target.classList.add('selected');
    }

    // Initialize if editing
    <?php if ($isEdit): ?>
        document.addEventListener('DOMContentLoaded', function() {
            updateServiceInfo();
        });
    <?php endif; ?>
</script>

<style>
    .modal-form {
        max-width: 600px;
    }

    .time-slots {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        padding: 15px;
        background: var(--light-color);
        border-radius: 4px;
        min-height: 60px;
        align-items: center;
    }

    .slot-btn {
        padding: 8px 15px;
        border: 1px solid var(--border-color);
        background: white;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        font-size: 0.9rem;
    }

    .slot-btn:hover {
        background: var(--light-color);
        border-color: var(--primary-color);
    }

    .slot-btn.selected {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .loading {
        color: var(--text-muted);
        font-style: italic;
    }

    .no-slots {
        color: var(--warning-color);
        font-weight: 500;
    }

    .error {
        color: var(--danger-color);
        font-weight: 500;
    }

    .reservation-details {
        margin: 25px 0;
        padding: 20px;
        background: var(--light-color);
        border-radius: 4px;
    }

    .reservation-details h4 {
        margin: 0 0 15px 0;
        color: var(--secondary-color);
    }

    .detail-grid {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .detail-item {
        padding: 8px 12px;
        background: white;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        font-size: 0.9rem;
    }

    @media (max-width: 768px) {
        .form-row {
            flex-direction: column;
        }

        .time-slots {
            justify-content: center;
        }

        .form-actions {
            flex-direction: column;
        }
    }
</style>